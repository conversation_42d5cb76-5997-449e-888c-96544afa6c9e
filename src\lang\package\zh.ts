/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-13 15:57:27
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-21 09:55:32
 * @FilePath: \flb-receipt\src\lang\package\zh.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  receipt: {
    loadingInProgress: "加载中",
    salesBilling: "销售开单",
    salesOrderManagement: "销售单管理",
    priceRevision: "价格修订",
    editSalesOrder: "编辑销售单",
    addSalesOrder: "新增销售单",
    addSalesOrderTips:
      "使用优惠信息的订单只能修改总价，未使用优惠可修改单个商品价格",
    salesOrderCodeM: "销售单编码：",
    pleaseEnter: "请输入",
    billingDateM: "开单日期：",
    pleaseChoose: "请选择",
    storeM: "门店：",
    salesmanM: "销售人员：",
    reviewerM: "审核人员：",
    registerAsAMember: "注册会员",
    memberM: "会员：",
    memberPointsM: "会员积分：",
    remarksM: "备注：",
    addProduct: "添加商品",
    commodity: "商品",
    codeM: "编码：",
    stock: "库存",
    unitPrice: "单价",
    unitPriceM: "单价：",
    quantity: "数量",
    addSerialCode: "添加串码",
    unit: "单位",
    unitM: "单位：",
    discountedUnitPrice: "优惠单价",
    pointsDeduction: "积分抵扣",
    deductionM: "抵扣：",
    subtotals: "小计",
    operation: "操作",
    discountedActivities: "优惠活动",
    addSalesCouponTips: "「满减」和「折扣」只能使用一种",
    exchangeVoucherDetailM: "兑换券（兑换数量）：",
    discountM: "优惠：",
    discount: "优惠",
    giftGiveawayM: "赠送礼品：",
    paymentInformation: "付款信息",
    totalPriceProductM: "商品总价：",
    totalReductionM: "共减：",
    pointsDeductionM: "积分抵扣：",
    payableM: "应付：",
    modifiedTotalOrderPriceM: "修改后订单总价：",
    return: "返回",
    preservation: "保存",
    submit: "提交",
    networkError: "网络错误",
    pleaseSelectAStoreFirst: "请先选择门店",
    pleaseSelectAMemberFirst: "请先选择会员",
    pleaseSelectTheProductFirst: "请先选择商品",
    operationSuccess: "操作成功",
    numberM: "编号：",
    fullDeductionAmountM: "满减金额：",
    full: "满",
    rabateM: "折扣：",
    fracture: "折",
    noExchangeVoucher: "暂无兑换券",
    noCoupon: "暂无优惠券",
    determine: "确定",
    cancel: "取消",
    exchangeCoupons: "兑换券",
    orderStatus: "单据状态",
    searchPlaceholder: "请输入单据编码、手机号搜索",
    orderDate: "开单日期",
    store: "门店",
    priceChange: "价格变动",
    draft: "草稿",
    export: "导出",
    orderNo: "单据编号",
    member: "会员",
    beforeTotalDiscountPrice: "优惠前总价",
    afterTotalDiscountPrice: "优惠后总价",
    salePerson: "销售人员",
    reviewer: "审核人员",
    remark: "备注",
    status: "状态",
    pending: "挂起",
    reviewIng: "审核中",
    passed: "已通过",
    pass: "通过",
    rejected: "已拒绝",
    waitAudit: "待审核",
    reject: "拒绝",
    finish: "已完成",
    filterM: "筛选项：",
    clear: "清空",
    auditPass: "审核通过",
    auditReject: "审核拒绝",
    recallPrompt: "撤回提示",
    recallPromptContent: "是否确认撤回？",
    recall: "撤回",
    returnGoods: "退货",
    resubmit: "重新提交",
    deletePrompt: "删除提示",
    deletePromptContent: "删除后不可恢复，是否继续？",
    delete: "删除",
    noData: "暂无数据",
    categoryList: "分类列表",
    firstLevelGroup: "一级分组",
    productName: "货品名称",
    productCode: "货品编号",
    auditOpinionM: "审核意见：",
    salesOrderDetail: "销售单详情",
    salesOrderM: "销售单：",
    priceChangeRecord: "价格修改记录",
    goodsInfo: "商品信息",
    serialCodeM: "串码：",
    payableAmountM: "应付款：",
    exchangeQuantityM: "兑换数量：",
    fullDeductionM: "满减：",
    giftGiveaway: "赠送礼品",
    giftNumber: "礼品数量",
    beforePayableAmountM: "修改前应付：",
    afterPayableAmountM: "修改后应付：",
    draftBox: "草稿箱",
    edit: "编辑",
    inventoryQuantity: "库存数量",
    addGift: "添加礼品",
    pleaseChooseGift: "请选择赠品",
    pleaseEnterGiftQuantity: "请输入赠品数量",
    phone: "手机号",
    verificationCode: "验证码",
    sendVerificationCode: "发送验证码",
    name: "姓名",
    pleaseEnterPhoneNumber: "请输入手机号",
    pleaseEnterValidPhoneNumber: "请输入有效的手机号",
    pleaseEnterVerificationCode: "请输入验证码",
    pleaseEnterName: "请输入姓名",
    registerSuccess: "注册成功",
    modifiedByM: "修改人：",
    beforePriceM: "修改前单价：",
    afterPriceM: "修改后单价：",
    beforeTotalPriceM: "修改前总价：",
    afterTotalPriceM: "修改后总价：",
    addReturnOrder: "新增退货单",
    returnOrder: "退货单",
    relatedSalesOrder: "关联销售单",
    returnDate: "退货日期",
    returnAmount: "退货金额",
    auditStatus: "审核状态",
    purchaseIn: "采购入库",
    returnIn: "退货入库",
    surplusIn: "盘盈入库",
    allocateIn: "调拨入库",
    customize: "自定义",
    returnBilling: "退货开单",
    deleteSuccess: "删除成功",
    returnOrderCodeM: "退货单编码：",
    returnDateM: "退货日期：",
    chooseSalesOrder: "选择销售单后带出",
    orderQuantityM: "下单数量：",
    payAmountM: "实付款：",
    returnQuantityM: "退货数量：",
    returnPointsM: "退回积分：",
    returnAmountM: "退货金额：",
    refundAmountM: "退款总金额：",
    chooseSalesOrderTip: "选择销售单后带出商品",
    substract: "减",
    returnOrderDetail: "退货单详情",
    returnOrderM: "退货单：",
    relatedSalesOrderM: "关联销售单：",
    returnProduct: "退货商品",
    returnTotalAmount: "退货总金额：",
    pleaseEnterPrice: "请输入单价",
    pleaseEnterQuantity: " 请输入数量",
    operateTips: "操作提示",
    place: "输入关键字搜索",
    totalItems: "共 {count} 条",
    specDetails: "规格详情",
    itemCodeM: "商品编号：",
    close: "关闭",
    stop: "已停用",
    enable: "已启用",
    receiptName: "开单管理",
    salesOrderManage: "销售开单",
    returnOrderManage: "退货开单",
    inputAccountPhone: "请输入账户、手机号搜索",
    addAccount: "新增账户",
    employeeName: "员工姓名",
    account: "账户",
    email: "邮箱",
    role: "角色",
    delAccountConfirm: "是否删除该账户",
    editAccount: "编辑账户",
    staff: "员工",
    enterpriseEmail: "企业邮箱",
    selEmployeeBringOut: "选择员工后带出",
    addRole: "新增角色",
    number: "编号",
    roleName: "角色名称",
    roleDescription: "角色说明",
    managePower: "管理权限",
    delRoleConfirm: "是否删除该角色",
    editRole: "编辑角色",
    powerManage: "权限管理",
    roleManage: "角色管理",
    selectMenuTips: "请至少勾选一个菜单",
    authorityManage: "权限管理",
    accountManage: "账户管理",
    storeManage: "管理门店",
    employeeM: "员工：",
    accountM: "账户：",
    phoneNumberM: "手机号：",
    enterpriseEmailM: "企业邮箱：",
    roleM: "角色：",
    addTo: "添加",
    selectAll: "全选",
    inputSkuNameSearch: "请输入商品/商品组名称搜索",
    inputStoreNameSearch: "请输入门店名称搜索",
    selected: "已选择",
    code: "编码",
    storeName: "门店名称",
    storeCode: "门店编号",
    addStore: "添加门店",
    storeList: "门店列表",
    naviConfig: "开单导航配置",
    index: "序号",
    groupName: "分组名称",
    parentName: "上级分组",
    mainStatus: "主数据状态",
    changeSort: "调整排序",
    productGroup: "商品分组",
    addGroup: "添加分组",
    insert: "插入到",
    intoRow: "行前",
    exchangeTips: "本次下单，最多可使用【{count}】张兑换券",
    bomConfig: "BOM配方配置",
    bomName: "配方名称",
    bomNameM: "配方名称：",
    addBom: "新增配方",
    basicInfo: "基础信息",
    commodityM: "商品：",
    material: "原料",
    consumeQty: "消耗数量",
    rootMenu: "根菜单",
    salesOrder: "销售单",
    productList: "商品列表",
    messageSearchHint: "输入消息标题搜索",
    pleaseRemindDate: "请选择发送提醒日期",
    headline: "标题",
    triggerMode: "触发方式",
    creationTime: "创建时间",
    webLandingPagePath: "web落地页路径",
    appLandingPagePath: "app落地页路径",
    newMessage: "新增消息",
    see: "查看",
    editMessage: "编辑消息",
    config: "配置内容",
    messageTitle: "消息标题",
    pleaseInput: "请输入",
    mainBody: "正文",
    pushAndRemind: "推送、提醒",
    whetherPeriodicMessage: "是否周期性消息",
    whetherApopUpReminderIsRequired: "是否需要弹窗提醒",
    everyday: "每天",
    weekly: "每周",
    monthly: "每月",
    dimensionDeliveryTime: "下发维度时间",
    deliveryTime: "下发时间",
    deliveryDate: "下发日期",
    messageDetails: "消息详情",
    deleteHint1: "确定删除该业务信息吗？",
    thisSystemConfig: "本系统内配置",
    busiSystemTrig: "业务系统触发",
    monday: "周一",
    tuesday: "周二",
    wednesday: "周三",
    thursday: "周四",
    friday: "周五",
    saturday: "周六",
    sunday: "周天",
    pleaseSelect: "请选择",
    contentSource: "正文来源",
    delSuccess: "删除成功！",
    saveSuccess: "保存成功！",
    salesOrderNumber: "销售单号",
    salesReturnNumber: "销售退货单号",
    yes: "是",
    no: "否",

    discountRole: "开单优惠权限配置",
    discountPrivilegesM: "制单人优惠权限：",
    authChangePriceM: "审批人改价权限：",
    discountProductPrice: "可优惠商品售价的",
    isChangePrice: "是否价格修订",
    useMultipleDiscount: "是否使用多件折扣活动",
    multiDiscountTip: "当所选商品满足已启用的多件折扣活动奖励规则时可以使用",
    discountRule: "折扣活动规则",
    enableDiscountTip: "启用成功，已为您配置每个商品的最优活动方案",
    multiDiscountTip2:
      "当所选商品满足已启用的多件折扣活动奖励规则。您可以启用多件折扣活动",
    secondHand: "二手",
    needApproveM: "开单是否需要审批：",
    receiveInfo: "收款信息",
    payType: "支付方式",
    stageNum: "分期数",
    payVoucher: "支付凭证",
    payTypeHeader: "终端-银行-支付方式",
    receiveAmount: "收款金额",
    voucher: "凭证",
    totalAmountM: "总金额：",
    currentAmountM: "当前合计金额：",
    addPayType: "添加支付方式",
    unionPay: "单一支付方式",
    clickUpload: "点击上传",
    multiPay: "多种支付方式",
    configed: "已配置",
    noConfig: "未配置",
    barcodeTip: "串码不存在",
    barcodeTip2: "串码已存在",
    stockNumTip: "库存不足",
    secondHandTip: "商品未维护二手价不允许销售",
    financeConfigLabel: "终端-银行卡-支付方式",
    uniPayTip: "将所有金额统一进行支付",
    multiPayTip: "将总金额进行拆分，分别使用不同的支付方式",
    multiPayTip2: "请选择第 {line} 行终端-银行-支付方式",
    multiPayTip3: "请选择第 {line} 行分期数",
    multiPayTip4: "请选择第 {line} 行收款金额",
    payTypeTip: "请配置支付方式",
    appPathType: "app落地类型",
    protogenesis: "原生",
    h5: "h5",
    useBundledSale: "是否使用捆绑销售活动",
    bundleSaleTips:
      "所选商品满足已启用的捆绑销售活动奖励规则。您可以启用捆绑销售活动",
    bundleSaleTips2: "捆绑销售活动不可与其他优惠叠加使用",
    cantMultiTips: "无法使用  {name}  活动，因为当前商品组合不满足",
    multiActivity: "多件折扣",
    bundleActivity: "捆绑销售",
    addProductTips: "请添加商品",
    bindActivityRule: "捆绑活动规则",
    discountActivityM: "优惠活动：",
    barcodeIn: "串码录入",
    batchBarcodeTip1: "提交失败，串码【{code}】不在当前门店",
    batchBarcodeTip2: "提交失败，商品【{code}】已销售",
    batchBarcodeTip3: "提交失败，商品【{code}】未维护二手价，不允许销售",
    changePriceAudit: "仅改价时需要审批",
    pleaseChooseAudit: "请选择审批人",
    pleaseInputProdcut: "请输入商品名称",
    paymentTypeTip: "多种支付方式收款金额不等于实付金额",
    customerM: "客户：",
    customerManageTip: "可管理已选客户的 {count} 个门店",
    storeDetail: "门店详情",
    belongCustom: "所属客户",
    belongArea: "所属区域",
    noDocuments: "暂无任何单据",
    msgManage: "消息管理",

    disountOrPrice: "折扣/价格",
    editDiscount: "修改折扣",
    editDeduction: "修改扣减",
    discountedPriceM: "折后价格：",
    productNameM: "商品名称：",
    deductionM2: "扣减：",
    subPriceM: "减后价格：",
    noBundleActivity: "无捆绑活动",
    bundleActivityTip: "已配：{0}（剩余可配：{1}）",
    reduceProduct: "减少主商品数量",
    mainProductM: "主商品：",
    chooseActivityM: "选择活动：",
    reduceProductM: "减少主商品数量：",
    deleteProductTip: "删除后当前关联的商品活动将失效，确认删除吗？",
    addBundleActivity: "添加捆绑活动规则",
    currentActivityNumM: "当前可参与活动规则：{count}",
    quantityM: "数量：",
    originPrice: "原价",
    free: "免费",
    addBundleTip: "缺少商品{name}，选择后系统将自动添加",
    activityNum: "活动组数",
    activityNumM: "活动{index}：",
    noActivityProduct: "非活动商品",
    pleaceAddCode: "请添加串码",
    stockTip: "商品{name}库存不足",

    phoneRegister: "手机号注册",
    emailRegister: "邮箱注册",
    emailAccount: "邮箱地址",
    cashierDayily: "收银日报",
    startTime: "开始日期",
    endTime: "结束日期",
    statisticsDateM: "统计日期：",
    collectionM: "收款：",
    returnAmountM2: "退款：",
    totalM: "合计：",
    salesOrder2: "销售订单",
    salesProduct: "销售商品",
    closingInventory: "闭店盘点",
    salesOrderData: "销售订单数据",
    dateM: "日期：",
    orderNoTip: "请输入单据编码",
    salesProductData: "销售商品数据",
    searchProduct: "请输入商品名称搜索",
    productName2: "商品名称",
    productCode2: "商品编号",
    serialCode: "串码",
    payableAmount: "应付款",
    returnOrderData: "退货单数据",
    returnProductData: "退货商品数据",
    relatedReturnOrder: "关联退货单",
    barcodeProduct: "串码商品",
    noBarcodeProduct: "非串码商品",
  },
};
