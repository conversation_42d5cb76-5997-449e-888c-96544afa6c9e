$height: 56px;
.avue-top {
  padding: 0 24px;
  position: relative;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.65);
  height: $height;
  line-height: $height;
  box-sizing: border-box;
  white-space: nowrap;
  .el-menu-item {
    font-size: 14px;
    i,
    span {
      color: rgba(255, 255, 255, 0.65);
    }
    &.is-active {
      i,
      span {
        color: #ffffff;
      }
    }
  }
  .el-menu--horizontal > .el-menu-item {
    height: $height;
    line-height: $height;
  }
}
.avue-breadcrumb {
  height: 100%;
  i {
    font-size: 20px !important;
    cursor: pointer;
  }
  // &--active {
  //     transform:rotate(180deg);
  // }
}

.top-menu {
  box-sizing: border-box;
  padding-left: 32px;
  .el-menu-item {
    padding: 0 16px;
    border: none !important;
  }
}

.top-search {
  line-height: $height;
  position: absolute !important;
  left: 20px;
  top: 0;
  width: 400px !important;
  .el-input__inner {
    font-size: 13px;
    border: none;
    background-color: transparent;
  }
}

.top-bar__img {
  margin: 0 8px 0 5px;
  width: 30px;
  height: 30px;
  border-radius: 100%;
  box-sizing: border-box;
  vertical-align: middle;
}

.top-bar__left,
.top-bar__right {
  height: $height;
  position: absolute;
  top: -2px;
  i {
    line-height: $height;
  }
}

.top-bar__left {
  left: 24px;
  display: flex;
}

.top-bar__right {
  background: #fff;
  right: 24px;
  display: flex;
  align-items: center;
}

.top-bar__item {
  position: relative;
  display: inline-block;
  height: $height;
  margin: 0;
  font-size: 16px;
  &--show {
    display: inline-block !important;
  }
  .el-badge__content.is-fixed {
    top: 12px;
    right: 5px;
  }
}

.top-bar__title {
  height: 100%;
  padding: 0 0 0 146px;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: inherit;
  font-weight: 400;
}