<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <!-- 展开收起菜单 -->
      <div class="avue-breadcrumb" :class="[{ 'avue-breadcrumb--active': isCollapse }]" v-if="showCollapse">
        <i class="iconfont-flb"  :class="{ 'icon-shouqicaidan': !isCollapse, 'icon-zhankaicaidan': isCollapse }"
          @click="setCollapse"></i>
      </div>
    </div>

    <!-- 用户昵称 -->
    <div class="top-bar__right">
      <el-dropdown class="pointer">
        <span class="flex align_items_center cur_system_box">
          <el-avatar :size="24" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
          <span class="ml8">{{ userInfo.name }}</span>
          <el-icon class="ml4">
            <CaretBottom />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="custom_dropdown_menu">
            <el-dropdown-item @click="loginOutVisible = true">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 退出登录提示 -->
    <el-dialog width="400px" append-to-body class="common-confirm-dialog" v-model="loginOutVisible" :show-close="false">
      <div class="confirm-dialog-title">
        <i class="title-icon iconfont-flb icon-tishi violet"></i>
        <span class="title-text">操作提示</span>
      </div>
      <div class="confirm-dialog-content">退出系统, 是否继续?</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="loginOutVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmLoginout()">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码 -->
    <el-dialog v-model="editPwdVisible" width="480px" append-to-body :show-close="false"
      class="common-dialog-center edit-password-dialog">
      <div class="content-form">
        <el-form :model="editPasswordForm" :rules="passwordFormRules" ref="editPwdFormRef" label-width="0">
          <div class="title-box">修改密码
            <i class="icon-24gl-cross" @click="editPwdVisible = false">
            </i>
          </div>
          <div class="title-tip">修改密码后需重新登录，请牢记修改后的密码</div>
          <!-- 原密码 -->
          <el-form-item prop="oldPassword">
            <el-input type="password" v-model="editPasswordForm.oldPassword" size="large" placeholder="请输入旧密码">
            </el-input>
          </el-form-item>

          <!-- 新密码 -->
          <el-form-item prop="newPassword">
            <el-input type="password" v-model="editPasswordForm.newPassword" size="large"
              placeholder="请输入新密码（英文+数字组合，8～16位）"></el-input>
          </el-form-item>

          <!-- 确认新密码 -->
          <el-form-item prop="confirmNewPwd">
            <el-input type="password" v-model="editPasswordForm.confirmNewPwd" size="large"
              placeholder="请再次输入新密码"></el-input>
          </el-form-item>

          <!-- 按钮 -->
          <el-form-item class="button-item">
            <el-button class="float-r" type="primary" @click="onReset(editPwdFormRef)" :loading="btnLoading">修
              改</el-button>
            <div class="forgetPwd-box" @click="changeActiveName">忘记密码</div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { workbenchUrl, logOutPage } from "@/config/env"
import { editPasswordApi } from "@/api/user"
import { computed, reactive, ref } from "vue"
import { FormInstance, ElMessage, FormRules } from 'element-plus'
import store from "@/store"
import { useRouter } from "vue-router"
import { CaretBottom, Select } from '@element-plus/icons-vue'

const validatePwd = (rule, value, callback) => {
  if (value !== editPasswordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
}

const validatePass = (rule, value, callback) => {
  //必须包含⼤⼩写字母、数字长度再8-16位之间
  let regex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{8,16}");
  if (value === "") {
    callback(new Error("请输⼊密码"));
  } else if (value.length < 8 || value.length > 16) {
    callback(new Error("请输⼊8~16位密码"));
  } else if (!regex.test(value)) {
    callback(new Error("密码（英文+数字组合，8～16位）"));
  } else {
    callback();
  }
}

const router = useRouter()
const showCollapse = computed(() => {
  return store.getters.showCollapse
})
const userInfo = computed(() => {
  return store.getters.userInfo
})
const isCollapse = computed(() => {
  return store.getters.isCollapse
})

const btnLoading = ref(false)
const loginOutVisible = ref(false)
const editPwdVisible = ref(false)
const editPwdFormRef = ref<FormInstance>()
const editPasswordForm = reactive({
  newPassword: '',
  oldPassword: '',
  confirmNewPwd: '',
  userId: ''
})
const passwordFormRules = reactive<FormRules>({
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { required: true, validator: validatePass, trigger: "blur" }
  ],
  confirmNewPwd: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { required: true, validator: validatePwd, trigger: "blur" }
  ]
})

//忘记密码
const changeActiveName = () => {
  router.push({ path: '/forgetPwd' })
}

//变化菜单的展开收起
const setCollapse = () => {
  store.commit("common/SET_COLLAPSE");
}


//确认退出
const confirmLoginout = () => {
  store.dispatch("user/LogOut").then(() => {
    window.location.href = workbenchUrl + "/#/login" + logOutPage();
  });
}


//点击确认修改
const onReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  editPasswordForm.userId = userInfo.value.id
  formEl.validate((valid) => {
    if (valid) {
      btnLoading.value = true
      editPasswordApi(editPasswordForm).then((res) => {
        if (res.success) {
          ElMessage.success('重置成功！');
          store.dispatch("user/LogOut").then(() => {
            window.location.href = workbenchUrl + "/#/login" + logOutPage();
          });
        } else {
          ElMessage.error(res.msg);
        }
        btnLoading.value = false
      }).catch(() => {
        btnLoading.value = false
        ElMessage.error('网路错误');
      });
    }
  });
}
</script>

<style lang="scss" scoped>
.cur_system_box {
  padding: 8px 12px;
  border-radius: 4px;

  &:hover {
    background-color: #F3F6F8;
  }
}

.edit-password-dialog {
  border-radius: 8px;

  :deep(.el-dialog__header) {
    .el-dialog__header {
      padding: 0px;
    }
  }

  :deep(.el-input) {
    font-size: 12px;
  }

  :deep(.el-button span) {
    font-size: 14px;
  }

  .content-form {
    padding: 0 22px 10px 22px;

    .el-form-item {
      margin-bottom: 32px
    }

    .button-item {
      padding-top: 8px;
    }

    .forgetPwd-box {
      width: 388px;
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: #0054FF;
      line-height: 22px;
      text-align: right;
      margin-top: 24px;
      cursor: pointer;
      background: none;
    }

    .float-r {
      width: 388px;
      height: 46px;
      background: #0054FF;
      border-radius: 4px 4px 4px 4px;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #FFFFFF;
      }
    }
  }

  .title-tip {
    height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    line-height: 24px;
    padding-bottom: 40px;
  }

  .title-box {
    padding-bottom: 8px;
    line-height: 44px;
    height: 44px;
    font-size: 32px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
    position: relative;

    .icon-24gl-cross {
      position: absolute;
      right: 0;
      font-size: 16px;
      cursor: pointer;
      color: #777;
    }
  }
}

.select-lang-dialog {
  :deep(.el-radio-group) {
    display: block;
    height: 204px;
    overflow-y: auto;
  }

  :deep(.el-radio) {
    display: block;
    margin-bottom: 16px;
  }

  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
  }

  :deep(.el-radio__label) {
    font-size: 14px;
    color: #666666;
  }

  :deep(.el-dialog__header) {
    position: relative;
  }

  :deep(.el-dialog__headerbtn) {
    top: 50%;
    margin-top: -10px;
  }
}
</style>
<style lang="scss">
.custom_dropdown_menu {
  &.el-dropdown-menu {
    padding: 4px;
    width: 120px;
  }

  .el-dropdown-menu__item {
    border-radius: 4px;
  }
}
</style>
