<template>
  <div
    class="bgc_white p24 flex flex_direction_column heightP100"
    v-loading="loading"
  >
    <div class="flex justify_content_between">
      <search
        :placeholder="$t('receipt.messageSearchHint')"
        @on-search="onSearch"
      ></search>
      <el-button type="primary" size="small" @click="onEdit(null)"
        >+ {{ $t("receipt.newMessage") }}</el-button
      >
    </div>

    <div class="flex1 mt16">
      <el-table
        class="custom_radius_table"
        size="small"
        :height="tableHeight"
        :data="tableData"
        border
      >
        <el-table-column
          :label="$t('receipt.headline')"
          prop="title"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          :label="$t('receipt.triggerMode')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{
              scope.row.triggerThird.code == 1
                ? $t("receipt.busiSystemTrig")
                : $t("receipt.thisSystemConfig")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.creationTime')"
          prop="ctime"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          :label="$t('receipt.webLandingPagePath')"
          prop="webLink"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          :label="$t('receipt.appLandingPagePath')"
          prop="appLink"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column :label="$t('receipt.operation')" min-width="85">
          <template #default="scope">
            <el-button link type="primary" @click="onSeeDetails(scope.row)">
              {{ $t("receipt.see") }}
            </el-button>
            <el-divider class="ml8 mr8" direction="vertical"></el-divider>
            <el-button link type="primary" @click="onEdit(scope.row)">
              {{ $t("receipt.edit") }}
            </el-button>
            <el-divider class="ml8 mr8" direction="vertical"></el-divider>
            <el-button
              link
              type="danger"
              @click="onShowDeleteDialog(scope.row)"
            >
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>
      <pagination
        :pageObj="pageObj"
        @onPageChange="onPageChange"
        @currentChange="onInterpret"
      ></pagination>
    </div>

    <Edit
      v-if="isShowEdit"
      @onClose="isShowEdit = false"
      :row="currentSelectRow"
      @onRefresh="getList"
    ></Edit>
    <Details
      v-if="isShowDetails"
      @onClose="isShowDetails = false"
      :row="currentSelectRow"
    ></Details>
    <delete
      :content="$t('receipt.deleteHint1')"
      :show="isShowDelete"
      @confirm="onDelete"
      @close="isShowDelete = false"
    ></delete>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import website from "@/config/website";
import { deleteMessageApi, getMessageManageList } from "@/api/messageManage";
import { ElMessage } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import lang from "@/lang";
import Delete from "@cp/dialog/delete.vue";
import Search from "@cp/filter/search.vue";
import Pagination from "@cp/pagination/index.vue";
import Details from "@views/messageManage/details.vue";
import Edit from "@views/messageManage/edit.vue";

const i18n = lang.global;
const loading = ref(false);
const isShowEdit = ref(false);
const isShowDetails = ref(false);
const isShowDelete = ref(false);
const tableData = ref([]);
const currentSelectRow = ref(null);
const pageObj = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0,
  title: "",
  moduleId: website.moduleId,
});
const tableHeight = ref(0);

onMounted(() => {
  window.addEventListener("resize", getTableHeight);
  getTableHeight();
  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 132 - 58;
};

//切换每页显示多少条
const onPageChange = (pageSize) => {
  pageObj.value.pageSize = pageSize;
  pageObj.value.pageNo = 1;
  getList();
};

//点击翻页
const onInterpret = (current) => {
  pageObj.value.pageNo = current;
  getList();
};

const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj.value);
  getMessageManageList(params).then((res) => {
    if (res.code == 200) {
      tableData.value = res.data.records || [];
      pageObj.value.total = Number(res.data.total);
    } else {
      ElMessage.error(res.msg);
    }
    loading.value = false;
  });
};

//查看详情
const onSeeDetails = (row) => {
  currentSelectRow.value = row;
  isShowDetails.value = true;
};

//编辑
const onEdit = (row) => {
  currentSelectRow.value = row;
  isShowEdit.value = true;
};
//显示删除
const onShowDeleteDialog = (row) => {
  currentSelectRow.value = row;
  isShowDelete.value = true;
};

//删除
const onDelete = () => {
  deleteMessageApi(currentSelectRow.value.id).then((res) => {
    if (res.success) {
      ElMessage.success(i18n.t("receipt.delSuccess"));
      getList();
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const onSearch = (value) => {
  pageObj.value.pageNo = 1;
  pageObj.value.title = value;
  getList();
};
</script>

<style scoped lang="scss">
.el-button--text {
  border: none;
  background: none;
  &:hover {
    background: none;
    border: none;
    color: #2a4bff;
  }
}
</style>
