.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.forgetPwd-box {
  width: 100%;
  height: 100%;
  background: #F9F9F9;

  .login-container {
    height: calc(100vh - 56px);
    position: relative;
  }

  .forgetPwd_img_box {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 513px;
    height: 633px !important;
    // height: calc(100vh - 56px);
    background: url('~@/assets/img/default/frame-bg.png') no-repeat;
    background-size: 100% 100%;
  }

  .forgetPwd_img_box-right {
    position: absolute;
    bottom: 21.22vh;
    right: 64px;
    width: 261px;
    height: 304px;
    background: url('~@/assets/img/default/right-bg.png') no-repeat;
    background-size: 100% 100%;
  }

  .login-weaper {
    padding-left: 0;
  }
}

.forgetPwd-header {
  width: 100%;
  height: 56px;
  background: #FFFFFF;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  position: relative;

  img {
    margin-left: 24px;
  }

  .forgetPwd-header-right {
    position: absolute;
    right: 24px;
    font-size: 16px;

    span {
      color: #0054FF;
      font-size: 16px;
      cursor: pointer;
    }
  }
}

.login-weaper {
  width: 492px;
  padding-left: 44.44vh;

  .el-input-group__append {
    border: none;
  }
}

.login-border {
  width: 100%;
  border-radius: 5px;
  box-shadow: 0px 20px 80px 0px rgba(0, 0, 0, 0.1);
  padding: 48px 46px;
  box-sizing: border-box;
  color: #fff;
  background-color: #fff;
  min-height: 450px;
}

.login-main {
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;

  span,
  div,
  em {
    font-size: 14px;
  }
}

.login-main > h3 {
  margin-bottom: 20px;
}

.login-main > p {
  color: #76838f;
}

.login-submit {
  width: 100%;
  height: 45px;
  border: 1px solid #409eff;
  background: none;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 300;
  color: #409eff;
  cursor: pointer;
  font-family: "neo";
  transition: 0.25s;
  font-weight: 600;
}

.login-form {
  padding-top: 40px;

  i {
    color: #666666;
  }

  .el-form-item__content {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-col {
    margin-bottom: 0;
  }

  .el-input {
    input {
      border-radius: 2px;
      background: transparent;
      color: #333;
    }

    .el-input__prefix {
      i {
        font-size: 18px !important;
        color: #0054FF;
      }
    }
  }

  .title-box {
    line-height: 24px;
    padding-bottom: 8px;
    padding-top: 40px;
    font-weight: 500;
    color: #333333;
  }

  .bottom-operation {
    height: 14px;
    color: rgba(0, 0, 0, 0.65);

    .el-checkbox__label {
      font-size: 12px;
    }

    .float-r {
      cursor: pointer;
      font-size: 12px;
      margin-top: 14px;
    }
  }

  .el-input--prefix .el-input__inner {
    // padding-left: 48px;
  }

  .el-input__prefix {
    left: 11px;
  }
}

.login-code {
  display: flex;
  // align-items: center;
  // justify-content: space-around;
  margin: 0 0 0 16px;
  width: 140px;
}

.resetPassword {
  .el-input__inner {
    font-size: 16px;
  }

  .el-form-item__error {
    font-size: 16px;
  }

  .el-button {
    span {
      font-size: 16px;
    }
  }
}

.login-num-code {
  display: flex;
  margin: 0 0 0 16px;
  width: 104px;
}

.login-num .el-button {
  width: 104px;
  height: 40px;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 1px solid #0054FF;
  color: #0054FF;
  line-height: 40px;
  text-indent: 5px;
  text-align: center;

  span {
    font-size: 16px !important;
    font-weight: 500;
  }
}

.login-code-img {
  width: 100%;
  height: 38px;
  // background-color: #fdfdfd;
  background: #EEEEEE;
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #f0f0f0;
  // color: #333;
  // font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;

  span {
    font-size: 18px !important;
    font-weight: 550;
  }
}

.login-place-box {
  // height: 28px;
  padding: 0 16px;
  line-height: 28px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  color: #333333;
  background: #fff;
  font-size: 14px;

  i {
    font-size: 14px;
    color: #333333;
  }

  span {
    float: right;
    cursor: pointer;
  }

  &[data-type="0"] {
    background: #f8f8f8;

    i {
      color: rgba(0, 0, 0, 0.25);
    }

    span {
      display: none;
    }
  }
}