<template>
  <div class="avue-contail">
    <!-- iframe部分的蒙版 -->
    <div :class="{ 'model-wrap': modelOn }"></div>
    <!-- 顶部导航栏 -->
    <!-- <div class="avue-header" v-show="!isFullScreen">
      <top ref="top"
           @setIframeShowFalse="setIframeShowFalse"></top>
    </div> -->
    <div class="avue-layout">
      <div class="avue-left" :style="{top:`${topBarHeight}px`}">
        <!-- 左侧导航栏 -->
        <!-- {{ $route }} -->
        <sidebar :childrenMenu="menu"
                 @setIframeShowFalse="setIframeShowFalse"></sidebar>
      </div>
      <div class="avue-main" :style="{height:`calc(100vh - ${topBarHeight}px`}">
        <!-- 主体视图层 -->
        <div style="height:100%;overflow-y:auto;overflow-x:hidden"
             id="avue-view"
             v-show="!isSearch">
          <!-- <tags
            ref="tags"
            @setIframeShowFalse="setIframeShowFalse"
            @setTopMenuHighlight="setTopMenuHighlight"
            v-show="!isFullScreen"
            @refreshRouter="refreshRouter"
          ></tags> -->
          <router-view class="avue-view"
                       @setIframeShowFalse="setIframeShowFalse"
                       @setFullScreen="setFullScreen"
                       v-slot="{ Component }">
            <keep-alive>
              <component :is="Component"
                         v-if="($route.meta.keepAlive && !showIframe)"
                         :key="$route.name" />
            </keep-alive>
            <component :is="Component"
                       v-if="(!$route.meta.keepAlive && rereshEmitShow && !showIframe)"
                       :key="$route.name" />
          </router-view>
          <!-- <router-view
            class="avue-view"
            v-show="(!$route.meta.keepAlive && rereshEmitShow && !showIframe)"
            :key="$route.path + $route.query.t"
            @setIframeShowFalse="setIframeShowFalse"
            @setFullScreen="setFullScreen"
          ></router-view>-->
          <div id="frame"></div>
          <iframe v-show="showIframe"
                  class="ifrmae-wrap"
                  style="width:100%;height:100%;border:none"
                  :src="srcUrl"></iframe>
        </div>
      </div>

      <el-backtop target="#avue-view"
                  :bottom="50">
        <i class="icon-back-top"></i>
      </el-backtop>
    </div>
    <div class="avue-shade"
         @click="showCollapse"></div>
         
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import { getPath } from "@/util/util"
import top from "./top/"
import sidebar from "./sidebar"
import admin from "@/util/admin"
import tags from "./tags.vue"
import { iframeUrl } from '@/config/env.ts'
import { mainStore } from '@/storePinia/index.ts'
import { mapState } from 'pinia'

export default {
  components: {
    top,
    sidebar,
    tags,
  },
  name: "index",
  provide () {
    return {
      index: this,
    }
  },
  data () {
    return {
      isSearch: false,
      refreshLock: false,
      rereshEmitShow: true,
      showIframe: false,
      refreshTime: "",
      srcUrl: "",
      modelOn: false,
      isFullScreen: false,
    }
  },
  mounted () {
    this.init()
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    window.addEventListener('message', function (event) {
      //此处执行事件
      // 通过origin对消息进行过滤，避免遭到XSS攻击 iframe中内部弹窗向外部添加蒙版
      if (event.data == 'openModule') {
        that.modelOn = true
        that.$refs.tags.modelOn = true
        console.log('openModule1111')
      }
      if (event.data == 'closeModule') {
        that.modelOn = false
        that.$refs.tags.modelOn = false
        console.log('closeModule1111')
      }
    });
  },
  computed: {
    ...mapGetters(["isMenu", "isLock", "isCollapse", "website", "menuChildren","topBarHeight", "menu"])
  },
 
  props: [],
  methods: {
    showCollapse () {
      this.$store.commit("common/SET_COLLAPSE")
    },
    // 屏幕检测
    init () {
      this.$store.commit("common/SET_SCREEN", admin.getScreen())
      window.onresize = () => {
        setTimeout(() => {
          this.$store.commit("common/SET_SCREEN", admin.getScreen())
        }, 0)
      }
    },
    //打开菜单
    openMenu (item = {}) {
      this.$store.dispatch("user/GetMenu", item.parentId).then((data) => {
        if (data.length !== 0) {
          this.$router.$avueRouter.formatRoutes(data, true)
        }
        //当点击顶部菜单做的事件
        if (!this.validatenull(item)) {
          let itemActive = {},
            childItemActive = 0
          //vue-router路由
          if (item.path) {
            itemActive = item
          } else {
            if (this.menu[childItemActive].length == 0) {
              itemActive = this.menu[childItemActive]
            } else {
              itemActive = this.menu[childItemActive].children[childItemActive]
            }
          }
          this.$store.commit("SET_MENUID", item)
          this.$router.push({
            path: getPath(
              {
                name: itemActive.label,
                src: itemActive.path,
              },
              itemActive.meta
            ),
          })
        }
      })
    },
    //隐藏iframe部分
    setIframeShowFalse () {
      this.showIframe = false
    },
    //触发顶部菜单部分的高亮
    setTopMenuHighlight () {
      this.$refs.top.setCIndexByRoute()
    },
    //刷新当前页面
    refreshRouter () {
      this.rereshEmitShow = false
      setTimeout(() => {
        this.rereshEmitShow = true
      }, 300)
    },
    //设置全屏
    setFullScreen (val) {
      this.isFullScreen = val
    }
  },
}
</script>

<style lang="scss" scoped>
.avue-main-fullWidth {
  width: 100%;
  left: 0;
}

.ifrmae-wrap {
  height: calc(100vh - 115px) !important;
}

.model-wrap {
  position: fixed;
  z-index: 1026;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}
</style>
