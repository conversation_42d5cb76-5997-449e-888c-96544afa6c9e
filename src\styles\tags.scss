.avue-tags {
  user-select: none;
  position: relative;
  padding: 0;
  margin-bottom: 0px;
  box-sizing: border-box;
  overflow: hidden;

  // border-top: 1px solid #f6f6f6;
  // background-color: #fff;
  // box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  .el-tabs--card>.el-tabs__header {
    margin: 0;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__nav,
  .el-tabs--card>.el-tabs__header .el-tabs__item,
  .el-tabs--card>.el-tabs__header {
    border: none;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item:first-child {
    border-left-width: 1px;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item .is-icon-close{
    // width:14px;
    position: absolute;
    right:-18px;
    right: 6px;
    top: 9px;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item {
    position: relative;
    border-radius: 4px 4px 0 0;
    margin: 0 6px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    font-weight: normal;
    color: #666666;
    background-color: #f0f2f5;
    margin-left: 0;
    padding-left: 16px !important;
    min-width: 116px;

    .tags-left-corner {
      overflow: hidden;
      position: absolute;
      left: -3px;
      bottom: 0px;
      background-color: #e1e3e7;
      width: 3px;
      height: 6px;

      .tags-left-corner-inner {
        background-color: #f0f2f5;
        height: 5.9px;
        width: 8px;
        border-radius: 50%;
        position: relative;
        left: -5px;
        top: -1.5px;
      }
    }

    .tags-right-corner {
      overflow: hidden;
      position: absolute;
      right: -3px;
      bottom: 0px;
      background-color: #e1e3e7;
      width: 3px;
      height: 6px;

      .tags-right-corner-inner {
        background-color: #f0f2f5;
        height: 5.9px;
        width: 8px;
        border-radius: 50%;
        position: relative;
        right: 0px;
        top: -1.5px;
      }
    }

    &.is-active {
      background-color: #f2f3f6;
      color: #333333;

      .tags-right-corner,
      .tags-left-corner {
        background-color: #fff;
      }
    }

    &.is-closable:hover {
      padding-left: 16px;
    }
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item {
    width: 156px;
    background-color: #eee;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    background-color: #fff;
    color: #333333;
    font-weight:550;
  }

  &__box {
    position: relative;
    box-sizing: border-box;
    padding-right: 106px;
    width: 100%;

    &--close {
      .el-tabs__item {
        &:first-child {
          padding: 0 20px !important;

          .el-icon-close {
            display: none;
          }
        }

        .el-icon-close {
          overflow: initial !important;
        }
      }
    }
  }

  &__contentmenu {
    position: fixed;
    width: 120px;
    background-color: #fff;
    z-index: 1024;
    border-radius: 5px;
    box-shadow: 1px 2px 10px #ccc;

    .item {
      cursor: pointer;
      font-size: 14px;
      padding: 8px 20px 8px 15px;
      color: #606266;

      &:first-child {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
      }

      &:last-child {
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
      }

      &:hover {
        background-color: #409eff;
        color: #fff;
      }
    }
  }

  &__menu {
    position: absolute !important;
    top: 3px;
    right: 0;
    padding: 1px 0 0 15px;
    box-sizing: border-box;
  }
}