/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-06 11:39:44
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-21 09:45:29
 * @FilePath: \flb-receipt\src\config\env.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Base64 } from "js-base64";

//获取环境配置
const env = process.env;

const baseUrl = env.VUE_APP_BASE_URL;
const hrBaseUrl = env.VUE_APP_HR_BASE_URL;
const workbenchUrl = env.VUE_APP_WORKBENCH_URL;
const domain =  env.VUE_APP_COOKIE_DOMAIN;
const iframeUrl = env.VUE_APP_IFRAME_URL;
const languageNodeId = env.VUE_APP_LANGUAGE_NODE_ID; //获取多语言数据的nodeId,上线时记得替换
const uploadUrl = env.VUE_APP_UPLOAD_URL;
const iconfontVersion = ["4657581_d22strfxzdv"];
const iconfontUrl = `//at.alicdn.com/t/c/font_4657581_d22strfxzdv.css`;
const codeUrl = `${baseUrl}/code`;

console.log("环境变量", env)

const logOutPage = (): string => {
  const encodeStr = localStorage.getItem("buId") || "";
  return "?buId=" + Base64.encode(encodeStr);
};
export {
  baseUrl,
  iconfontUrl,
  iconfontVersion,
  codeUrl,
  workbenchUrl,
  iframeUrl,
  languageNodeId,
  logOutPage,
  domain,
  env,
  hrBaseUrl,
  uploadUrl,
};
