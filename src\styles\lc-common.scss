a {
  text-decoration: none;
  color: #333;
}

* {
  outline: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  word-break: break-all;

  div,
  p,
  ul,
  ol,
  li,
  span,
  i,
  strong {
    font-size: 12px;
  }
}

i,
em,
strong {
  font-style: normal;
}

//滚动条样式
@include scrollBar;

//重置avue样式
.avue-main {
  padding-bottom: 0;
  background: #f6f9fa;
}

.avue-top {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

//左侧菜单滚动条样式重置
.avue-sidebar {
  .el-scrollbar__thumb {
    background-color: rgba(255, 255, 255, 0.4);
  }

  .el-scrollbar__view {
    height: auto;
  }
}

//公用confirm弹窗
.common-confirm-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -40px !important;
  transform: translate(-50%, -50%);

  .el-dialog__header {
    padding: 12px 24px;
  }

  .el-dialog__body {
    padding-top: 0;
  }

  .confirm-dialog-title {
    .title-icon {
      float: left;
      font-size: 18px;
      color: #ec2d30;
      vertical-align: sub;
      margin-right: 8px;
      margin-top: 1px;

      &.green {
        color: #00c800;
      }

      &.blue {
        color: #415fff;
      }

      &.orange {
        color: #fe9b0e;
      }

      &.gray {
        color: gray;
      }
    }
  }

  .confirm-dialog-content {
    padding: 16px 0 24px 24px;
  }

  .el-dialog__footer {
    padding-top: 0;
  }
}

//弹窗居中样式
.common-dialog-center {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: calc(100% - 30px);
}
.common-dialog-center .el-dialog__body {
  flex: 1;
  overflow: auto;
}

// 公用弹窗头部底部边框样式
.common_border_dialog {
  .el-dialog__header {
    padding: 16px 24px;
    margin-right: 0;
    box-shadow: inset 0px -1px 0px 0px #d6dce1;

    .el-dialog__title {
      font-size: 16px;
    }
  }

  .el-dialog__footer {
    padding: 4px 24px 24px 24px;
  }
}
// 公用弹窗头部底部边框样式
.common_border_top_dialog {
  .el-dialog__header {
    padding: 16px 24px;
    box-shadow: inset 0px -1px 0px 0px #d6dce1;
  }
  .el-dialog__body {
    padding-top: 24px;
  }
}

//公用面包屑样式
.common-crumbs-box {
  display: inline-flex;
  align-items: center;
  padding: 4px 16px;
  border-radius: 4px;
  background-color: #f3f6f8;
  .disable-span {
    color: #989cac;
  }
  .current-span {
    color: #1d2330;
  }
  .hover-span {
    color: #989cac;
    cursor: pointer;
    &:hover {
      color: #695bff;
    }
  }
}

// 圆点样式
.common-status-circle {
  display: flex;
  align-items: center;
  color: #1d2330;
  &::before {
    content: "";
    width: 6px;
    min-width: 6px;
    min-height: 6px;
    height: 6px;
    margin-right: 4px;
    border-radius: 50%;
  }
  &.green {
    &::before {
      background-color: #10d986;
    }
  }
  &.gray {
    &::before {
      background-color: #f0f0f0;
    }
  }
  &.orange {
    &::before {
      background-color: #fe9b0e;
    }
  }
  &.red {
    &::before {
      background-color: #fd6361;
    }
  }
}

// 状态box样式
.common-status-box {
  color: #1d2330;
  line-height: 18px;
  font-weight: 550;
  padding: 2px 8px;
  display: inline-block;
  border-radius: 4px;
  max-width: 100%;
  vertical-align: middle;

  &.green {
    background-color: #d8ffc8;
  }
  &.gray {
    background-color: #edf0f2;
  }
  &.orange {
    background-color: #ffedc8;
  }
  &.red {
    background-color: #ffd8d8;
  }
  &.blue {
    background-color: #e0eaff;
  }
}

// 表格上对齐
.el-table {
  td {
    vertical-align: top;
  }
}

//radio margin样式修改
.el-radio {
  margin-right: 8px;
}

//选择树样式覆盖
.vue-treeselect {
  .vue-treeselect__control,
  .vue-treeselect__value-container {
    height: 26px;
    line-height: 26px;
  }

  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    line-height: 26px;
  }

  .vue-treeselect__multi-value {
    line-height: 0;
    margin-bottom: 0;
    vertical-align: top;
  }

  .vue-treeselect__multi-value-item {
    padding: 0;
    line-height: 16px;
  }

  .vue-treeselect__limit-tip {
    line-height: 16px;
  }

  .vue-treeselect__limit-tip-text {
    margin: 0;
  }

  .vue-treeselect__tip {
    display: none;
  }

  &.vue-treeselect--has-value {
    .vue-treeselect__multi-value {
      margin-bottom: 0;
    }
  }
}

.el-table__fixed-right::before,
.el-table__fixed::before {
  background-color: transparent;
}
.common_bar_btn {
  padding: 12px 16px;
  font-size: 12px;
  color: #1d2330;
  line-height: 18px;
  text-align: left;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  justify-content: space-between;
  .right-content {
    display: flex;
    align-items: center;
  }
}
//带主题色竖线的标题
.common_title_with_flag {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px dashed #d6dce1;
  color: #1d2330;
  font-size: 14px;
  font-weight: 550;

  &::before {
    content: "";
    width: 3px;
    height: 12px;
    background-color: #415fff;
    border-radius: 2px;
    margin-right: 8px;
  }
}

// 自定义switch
.custom_switch {
  .el-switch__core {
    height: 20px;
    border-radius: 4px;
    .el-switch__action {
      width: 24px;
      height: 16px;
      border-radius: 4px;
    }
  }

  &.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 25px);
  }
}

// 自定义审批弹出层
.custom_approve_popover {
  --el-popover-padding: 16px;
  --el-popover-border-radius: 8px;
}

// 自定义实心radio样式
.custom_solid_radio {
  .el-radio-button__inner {
    --el-fill-color-blank: #edf0f2;
    border: 2px solid #edf0f2;
  }
  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    color: #1d2330 !important;
    background-color: #ffffff !important;
    border-color: #edf0f2 !important;
    box-shadow: -1px 0 0 0 #edf0f2 !important;
  }
}

// 自定义空心radio样式
.custom_hollow_radio {
  .el-radio-button {
    background-color: transparent;
  }

  .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 4px 0px 0px 4px;
  }

  .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0px 4px 4px 0px;
  }

  .el-radio-button__inner {
    border: 1px solid #dfe2e7;
    color: #4f5363;
    border-left: 0;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
  }

  .el-radio-button.is-active
    .el-radio-button__original-radio:not(:disabled)
    + .el-radio-button__inner {
    background: #ffffff;
    border-color: #415fff;
    color: #415fff;
  }
}

// 隐藏数字input上下箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

// 表格--操作列
.operate_column .cell {
  padding: 7px 12px !important;
}

.transparent_modal {
  background-color: transparent;
}

// 单据置顶提示
.order_pop {
  max-width: 247px;
  padding: 8px 20px;
  border-radius: 4px;
  opacity: 0.9;
  background: #222933;
}

.detail_drawer_modal {
  left: 652px !important;

  .el-drawer {
    border-radius: 8px 0px 0px 8px;
    box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .el-drawer__header {
    padding: 16px 24px 12px 24px;
    border-bottom: 1px solid #dfe2e7;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    color: #1d2330;
    margin-bottom: 0;

    .el-drawer__close {
      font-size: 16px;
      color: #646878;
    }
  }
}

.left_menu_drawer_modal {
  left: 252px;
  width: auto !important;
  border-radius: 8px 0px 0px 8px;
  box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  .el-drawer__header {
    padding: 16px 24px 12px 24px;
    border-bottom: 1px solid #dfe2e7;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    color: #1d2330;
    margin-bottom: 0;

    .el-drawer__close {
      font-size: 16px;
      color: #646878;
    }
  }
}

.draw_footer_16 {
  .el-drawer__footer {
    padding: 16px 24px;
  }
}

.draw_footer_24 {
  .el-drawer__footer {
    padding: 24px;
  }
}

