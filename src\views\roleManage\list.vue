<template>
  <div
    class="p24 bgc_white"
    v-loading="loading"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <el-button
      type="primary"
      @click="
        operateRow = {};
        operateDialog = 'addDialog';
        showDialog = true;
      "
    >
      <i class="iconfont-flb icon-add mr2"></i
      >{{ $t("receipt.addRole") }}</el-button
    >

    <!-- 表格 -->
    <div class="mt16">
      <el-table
        :data="tableData"
        size="small"
        border
        :height="tableHeight"
        class="custom_radius_table"
        style="width: 100%"
      >
        <el-table-column prop="id" :label="$t('receipt.number')" width="100" />
        <el-table-column
          prop="title"
          :label="$t('receipt.roleName')"
          width="200"
        >
          <template #default="scope">
            <span class="c_1D2330 fw600">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="descp" :label="$t('receipt.roleDescription')" />
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="224"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="
                operateRow = scope.row;
                operateDialog = 'addDialog';
                showDialog = true;
              "
              >{{ $t("receipt.edit") }}</el-button
            >
            <el-divider direction="vertical" class="ml12 mr12"></el-divider>
            <el-button
              link
              type="primary"
              @click="
                operateRow = scope.row;
                operateDialog = 'powerDrawer';
                showDialog = true;
              "
              >{{ $t("receipt.managePower") }}</el-button
            >
            <el-divider direction="vertical" class="ml12 mr12"></el-divider>
            <el-button
              link
              type="danger"
              @click="
                operateRow = scope.row;
                operateDialog = 'delDialog';
                showDialog = true;
              "
              >{{ $t("receipt.delete") }}</el-button
            >
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>
    </div>

    <!-- 删除 -->
    <del-dialog
      v-if="operateDialog === 'delDialog'"
      :content="$t('receipt.delRoleConfirm')"
      v-model:show="showDialog"
      @confirm="onDel"
      @close="operateDialog = ''"
    ></del-dialog>

    <!-- 新增 -->
    <add-dialog
      v-if="operateDialog === 'addDialog'"
      :operateRow="operateRow"
      v-model:show="showDialog"
      @refresh="getList"
      @close="operateDialog = ''"
    ></add-dialog>

    <!-- 权限管理 -->
    <power-drawer
      v-if="operateDialog === 'powerDrawer'"
      v-model:show="showDialog"
      :operateRow="operateRow"
      @refresh="getList"
      @close="operateDialog = ''"
    ></power-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from "vue";
import { ElMessage } from "element-plus";
import delDialog from "@/components/dialog/delete.vue";
import addDialog from "./components/add-dialog.vue";
import emptyBox from "@/components/empty/index.vue";
import powerDrawer from "./components/power-drawer.vue";
import { delRoleApi, getRolePageApi } from "@/api/roleManage";
import store from "@/store";
import lang from "@/lang/index";
import website from "@/config/website";

const i18n = lang.global;
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = ref("");
const showDialog = ref(false);
const operateRow: any = ref({});
const pageObj = {
  pageNo: 1,
  pageSize: 20,
  total: 0,
};
const userInfo = computed(() => {
  return store.getters.userInfo;
});

onMounted(() => {
  window.addEventListener("resize", getTableHeight);
  getTableHeight();

  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

// 计算表格高度
const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 144;
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = {
    buId: userInfo.value.buId,
    moduleId: website.moduleId,
    page: pageObj.pageNo,
    pageSize: pageObj.pageSize,
  };
  getRolePageApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data?.records || [];
        tableData.value = list;
        pageObj.total = Number(res.data?.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 删除
const onDel = () => {
  delRoleApi({ id: operateRow.value.id })
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        getList();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
</script>

<style lang="scss" scoped></style>
