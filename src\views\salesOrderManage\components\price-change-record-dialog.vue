<template>
  <el-dialog
    :model-value="props.show"
    :before-close="onClose"
    :title="title"
    append-to-body
    class="common-dialog-center common_border_top_dialog price-dialog"
    width="550px"
    :footer="null"
  >
    <el-steps
      :active="-1"
      direction="vertical"
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
    >
      <template v-if="contentList && contentList.length">
        <el-step v-for="item in contentList" :key="item.id">
          <template #icon>
            <div
              class="border_radius4"
              style="width: 4px; height: 4px; background: #b7bac8"
            ></div>
          </template>
          <template #title>
            <div class="c_1D2330 fw500 fs14">
              {{ dayjs(item.ctime).format("YYYY/MM/DD HH:mm:ss") }}
            </div>
            <div class="pb8 pr16">
              <span class="c_989CAC lh18">
                {{ $t("receipt.modifiedByM") }}
                <span class="c_4F5363">
                  {{ item.createName || "" }}
                  （{{ item.createCode || "" }}）
                </span>
              </span>
            </div>
          </template>
          <template #description>
            <div class="ptb8 plr12 bgc_F5F7F9 border_radius4">
              <template v-for="(el, index2) in item.detailList" :key="el.id">
                <template v-if="el?.skuName?.value">
                  <div
                    class="c_1D2330 fw500 lh18"
                    :class="index2 !== 0 ? 'mt12' : ''"
                  >
                    {{ el?.skuName?.value || "" }}
                  </div>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.beforePriceM") }}
                    <span class="c_1D2330">
                      {{ formatMoney(Number(el.beforePrice || 0)) }}
                    </span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.afterPriceM") }}
                    <span class="c_1D2330 fw500">
                      {{ formatMoney(Number(el.afterPrice || 0)) }}
                    </span>
                  </span>
                </template>
                <template v-else>
                  <span
                    class="c_4F5363 lh18"
                    :class="index2 !== 0 ? 'mt12' : ''"
                  >
                    {{ $t("receipt.beforeTotalPriceM") }}
                    <span class="c_1D2330">
                      {{ formatMoney(Number(el.beforePrice || 0)) }}
                    </span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.afterTotalPriceM") }}
                    <span class="c_1D2330 fw500">
                      {{ formatMoney(Number(el.afterPrice || 0)) }}
                    </span>
                  </span>
                </template>
              </template>
            </div>
          </template>
        </el-step>
      </template>
      <template v-else>
        <empty-box></empty-box>
      </template>
    </el-steps>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { getOrderPriceHistory } from "@/api/salesOrderManage";
import { formatMoney } from "@/util/numberUtil";
import { nextTick } from "vue";
import emptyBox from "@/components/empty/index.vue";
import { dayjs } from "element-plus";

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
  },
  title: {
    type: String,
    required: true,
  },
  orderNo: {
    type: String,
    required: true,
  },
});
const contentList = ref([]);
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    nextTick(() => {
      if (val && props.orderNo) {
        loading.value = true;
        getOrderPriceHistory({
          orderNo: props.orderNo,
        }).then((res) => {
          loading.value = false;
          contentList.value = res.data || [];
        });
      }
    });
  },
  { immediate: true }
);

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-step {
    .el-step__head {
      display: flex;
    }

    .el-step__main {
      margin-top: -8px;
      margin-bottom: 40px;
    }

    .el-step__icon {
      height: 4px;
    }

    .el-step__line {
      width: 1px;
      left: 11.5px;
      background-color: #dfe2e7;
    }
  }
}
</style>
<style lang="scss" scoped>
:deep(.price-dialog) {
  .el-dialog__body {
    padding-bottom: 24px;

    .el-steps {
      border: 1px solid #dfe2e7;
      padding: 24px;
      border-radius: 4px;
    }
  }
}
</style>
