<template>
  <el-drawer
    v-model="drawerVisible"
    direction="rtl"
    :append-to-body="true"
    :before-close="onClose"
    size="100%"
    class="no_shadow_drawer left_menu_drawer_modal top_drawer_modal"
    :title="form.id ? $t('receipt.editAccount') : $t('receipt.addAccount')"
  >
    <div>
      <!-- <div class="common-crumbs-box">
        <span class="disable-span">{{ $t("receipt.powerManage") }}</span>
        <el-icon class="ml8 mr8 c_4F5363">
          <ArrowRight />
        </el-icon>
        <span class="hover-span" @click="onClose">{{
          $t("receipt.accountManage")
        }}</span>
        <el-icon class="ml8 mr8 c_4F5363">
          <ArrowRight />
        </el-icon>
        <span class="current-span">{{
          form.id ? $t("receipt.editAccount") : $t("receipt.addAccount")
        }}</span>
      </div> -->

      <div class="content_wrap">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :size="formSize"
          label-position="left"
          label-width="140"
        >
          <el-form-item :label="$t('receipt.employeeM')" prop="personId">
            <el-select
              v-model="form.personId"
              :placeholder="$t('receipt.pleaseChoose')"
              class="widthP100"
              filterable
              remote
              reserve-keyword
              :remote-method="getPersonList"
            >
              <el-option
                v-for="item in personOpts"
                :key="item.personId"
                :label="`${item.personName}（${item.personCode}）`"
                :value="item.personId"
                @click="onSelPerson(item)"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('receipt.accountM')" prop="account">
            <el-input
              v-model="form.account"
              :placeholder="$t('receipt.selEmployeeBringOut')"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('receipt.phoneNumberM')" prop="phone">
            <el-input
              v-model="form.phone"
              :placeholder="$t('receipt.selEmployeeBringOut')"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('receipt.enterpriseEmailM')" prop="email">
            <el-input
              v-model="form.email"
              :placeholder="$t('receipt.selEmployeeBringOut')"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('receipt.roleM')" prop="roleIds">
            <el-select
              v-model="form.roleIds"
              :placeholder="$t('receipt.pleaseChoose')"
              multiple
              class="widthP100"
            >
              <el-option
                v-for="item in roleOpts"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('receipt.customerM')" prop="storeCustomers">
            <div class="flex flex1 align_items_center">
              <el-tree-select
                clearable
                v-model="form.storeCustomers"
                :data="customerData.list"
                :props="treeProps"
                render-after-expand
                show-checkbox
                check-strictly
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                node-key="id"
                class="flex1"
                @change="customerChange"
                :loading="customerData.loading"
              />
              <div
                class="ml16 flex_shrink0 lh18 main-color pointer"
                @click="showDetail"
              >
                {{
                  $t("receipt.customerManageTip", {
                    count: customerStore.length,
                  })
                }}
              </div>
            </div>
          </el-form-item>
          <el-form-item :label="$t('receipt.storeManage')" prop="storeIds">
            <div class="flex_grow_1">
              <el-button @click="operateDialog = 'addDialog'">
                <i class="iconfont-flb icon-add mr2"></i>
                {{ $t("receipt.addTo") }}
              </el-button>
              <el-table
                :data="form.storeIds"
                size="small"
                border
                class="custom_radius4_table mt8"
                style="width: 100%; max-width: 620px"
              >
                <el-table-column prop="name" :label="$t('receipt.storeName')">
                  <template #default="scope">
                    <span class="c_1D2330 fw600">{{
                      scope.row.name?.value
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="code" :label="$t('receipt.storeCode')" />
                <el-table-column
                  fixed="right"
                  :label="$t('receipt.operation')"
                  width="80"
                >
                  <template #default="scope">
                    <el-button
                      link
                      type="danger"
                      @click="onDel(scope.$index)"
                      >{{ $t("receipt.delete") }}</el-button
                    >
                  </template>
                </el-table-column>

                <template #empty>
                  <empty-box></empty-box>
                </template>
              </el-table>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <el-button type="danger" @click="onClose">
        {{ $t("receipt.close") }}
      </el-button>
      <el-button
        type="primary"
        :loading="btnLoading"
        @click="onSubmit(formRef)"
        >{{ $t("receipt.submit") }}</el-button
      >
    </template>

    <!-- 添加仓库弹窗 -->
    <add-dialog
      v-if="operateDialog === 'addDialog'"
      :selectedData="form.storeIds"
      @refresh="(list) => (form.storeIds = list)"
      @close="operateDialog = ''"
    ></add-dialog>

    <!-- 门店列表详情 -->
    <detail-drawer v-model:show="detailDialog.show" :data="detailDialog.data" />
  </el-drawer>
</template>

<script setup lang="ts">
import {
  ref,
  defineEmits,
  defineProps,
  onMounted,
  reactive,
  computed,
} from "vue";
import type { ComponentSize, FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import store from "@/store";
import { getRolePageApi } from "@/api/roleManage";
import emptyBox from "@/components/empty/index.vue";
import { deepClone } from "@/util/util";
import addDialog from "./add-stash-dialog.vue";
import { saveHrUserApi, saveUserApi, updateUserApi } from "@/api/accountManage";
import { getTmsPersonApi } from "@/api/common";
import website from "@/config/website";
import lang from "@/lang/index";
import { getCustomerTreeApi } from "@/api/salesOrder";
import { getCustomStore } from "@/api/salesOrderManage";
import detailDrawer from "./detail-drawer.vue";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  operateRow: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const detailDialog = reactive({
  show: false,
  data: {},
});
const drawerVisible = ref(false);
const btnLoading = ref(false);
const formSize = ref<ComponentSize>("default");
const formRef = ref<FormInstance>();
const form = ref({
  account: "",
  buId: "",
  buIds: [],
  code: "",
  email: "",
  nickName: "",
  personId: "",
  phone: "",
  roleIds: [],
  storeIds: [],
  userId: "",
  id: null,
  moduleId: website.moduleId,
  isEditPassword: "",
  password: "123456",
  storeCustomers: [],
});
const customerData = reactive({
  list: [],
  loading: false,
});
const rules = reactive<FormRules>({
  personId: [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  // account: [
  //   {
  //     required: true,
  //     message: i18n.t("receipt.pleaseEnter"),
  //     trigger: "blur",
  //   },
  // ],
  // phone: [
  //   {
  //     required: true,
  //     message: i18n.t("receipt.pleaseEnter"),
  //     trigger: "blur",
  //   },
  // ],
  // email: [
  //   {
  //     required: true,
  //     message: i18n.t("receipt.pleaseEnter"),
  //     trigger: "blur",
  //   },
  // ],
  roleIds: [
    {
      type: "array",
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  // storeIds: [
  //   {
  //     type: "array",
  //     required: true,
  //     message: i18n.t("receipt.pleaseChoose"),
  //     trigger: "change",
  //   },
  // ],
});
const personOpts = ref([]);
const roleOpts = ref([]);
const operateDialog = ref("");
const customerStore = ref([]);
const treeProps = {
  label: (val) => val?.customerName?.value || "-",
  children: "children",
};
const storeList = computed(() => {
  return store.getters.storeList;
});

onMounted(() => {
  drawerVisible.value = true;
  getCustomerList();
  if (props.operateRow.userId) {
    const info = deepClone(props.operateRow);
    info.nickName = info.name;
    delete info.roleTitle;
    delete info.warehouseList;
    form.value = info;
    form.value.storeIds = info.storeList;
    customerChange();
    getPersonList(info.code);
  } else {
    form.value.buId = store.getters.userInfo.buId;
    form.value.buIds = [store.getters.userInfo.buId];
  }

  getRoleList();

  // 获取门店
  if (!storeList.value.length) {
    store.dispatch("options/getStoreList");
  }
});

const getCustomerList = () => {
  customerData.loading = true;
  getCustomerTreeApi({
    businessType: "DEALER",
    enabled: "YES",
  })
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        customerData.list = list;
      } else {
        ElMessage.error(res.msg);
      }
      customerData.loading = false;
    })
    .catch(() => {
      customerData.loading = false;
    });
};

// 获取人员
const getPersonList = (str: string) => {
  getTmsPersonApi({ searchPerson: str, page: 1, pageSize: 100 })
    .then((res) => {
      if (res.code == 200) {
        const list = res.data?.records || [];
        personOpts.value = list;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
// 获取角色
const getRoleList = () => {
  const params = {
    page: 1,
    pageSize: 100,
    moduleId: website.moduleId,
    buId: store.getters.userInfo.buId,
  };
  getRolePageApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data?.records || [];
        roleOpts.value = list;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 选择人员
const onSelPerson = (item) => {
  form.value.nickName = item.nickName;
  form.value.code = item.personCode;
  form.value.account = item.account;
  form.value.phone = item.phone;
  form.value.email = item.eMail;
  form.value.userId = item.userId;
};

// 删除
const onDel = (index: number) => {
  form.value.storeIds.splice(index, 1);
};

// 校验
const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      btnLoading.value = true;
      saveHr();
    }
  });
};
// 保存人资
const saveHr = () => {
  const params = deepClone(form.value);

  params.buIds = [params.buId];
  delete params.id;
  delete params.storeIds;
  saveHrUserApi(params)
    .then((res) => {
      if (res.code == 200) {
        saveFlb(res.data);
      } else {
        ElMessage.error(res.msg);
      }
      btnLoading.value = false;
    })
    .catch((err) => {
      console.log(err, "ssssssss");

      btnLoading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
// 新增菲律宾
const saveFlb = (info) => {
  let saveApi = saveUserApi;
  if (props.operateRow.id) {
    saveApi = updateUserApi;
  }
  console.log(info, "info");

  const params = {
    buId: info.buId,
    moduleId: form.value.moduleId,
    sysUserCode: form.value.code,
    sysUserId: info.id,
    sysUserPersonId: form.value.personId,
    sysUserName: info.nickName,
    id: form.value.id,
    storeIds: (form.value.storeIds || []).map((item) => item.id),
    customerIds: [],
    storeCustomers: form.value.storeCustomers,
    // warehouseIds: "",
    // costPermissions: [],
    // warehouseRegions: [],
  };
  saveApi(params)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        emit("refresh");
        onClose();
      } else {
        ElMessage.error(res.msg);
      }
      btnLoading.value = false;
    })
    .catch(() => {
      btnLoading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 关闭
const onClose = () => {
  drawerVisible.value = false;
  emit("close");
};

// 区域选择变化
const customerChange = () => {
  if (!(form.value.storeCustomers && form.value.storeCustomers.length)) {
    customerStore.value = [];
    return;
  }
  getCustomStore({
    customerIdList: form.value.storeCustomers,
  })
    .then((res) => {
      if (res.code == 200) {
        customerStore.value = res.data || [];
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
  // if (!form.value.warehouseRegions.length) {
  //   stashList.value = []
  //   return
  // }
  // getStashByRegionApi(form.value.warehouseRegions.join(',')).then((res) => {
  //   if (res.code == 200) {
  //     const list = res.data || []
  //     stashList.value = list
  //   } else {
  //     ElMessage.error(res.msg)
  //   }
  // }).catch(() => {
  //   ElMessage.error(i18n.t('inventory.networkError'))
  // })
};

const showDetail = () => {
  detailDialog.show = true;
  console.log(customerStore.value, "customerStore.value");
  detailDialog.data = {
    storeList: customerStore.value,
  };
};
</script>

<style lang="scss" scoped>
.content_wrap {
  width: 620px;
  margin: 0 auto 24px auto;
}
.custom_form_item {
  ::v-deep {
    .el-form-item__content {
      flex-wrap: nowrap;
    }
  }
}
</style>
