module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "@vue/typescript/recommended",
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "prettier/prettier": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    // 关闭驼峰命名规则
    "vue/multi-word-component-names": 0,
    "@typescript-eslint/ban-ts-comment": "off",
    // 解决require引入报错
    "@typescript-eslint/no-var-requires": 0,
  },
};
