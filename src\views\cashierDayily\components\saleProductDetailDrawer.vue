<template>
  <el-drawer
    :model-value="show"
    :append-to-body="true"
    :before-close="onClose"
    class="title-drawer2 radius8_drawer"
    modal-class="top_drawer_modal"
    direction="rtl"
    size="calc(100vw - 80px)"
  >
    <template #header>
      <div class="flex align_items_center">
        <div class="fs16 fw500 lh24 c_1D2330">{{ titleStr || "" }}</div>
        <div
          class="ml24 pt4 pb4 pl8 pr8 flex align_items_center border_radius4"
          style="background-color: #dcf0ff"
        >
          <span class="fs12 lh18 c_4F5363 fw400">
            <span>
              {{ $t("receipt.storeM") }}
              {{ orderData.storeName?.value ?? "-" }}
            </span>
            <span class="pl16">
              {{ $t("receipt.dateM") }}
              {{ orderData.date ?? "-" }}
            </span>
          </span>
        </div>
      </div>
    </template>
    <div
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
      class="flex flex_direction_column"
    >
      <div class="flex">
        <div
          class="flex align_items_end b_solid_default border_radius4 w_max_content pt2 pl2 pr2 pb2"
        >
          <div
            class="filter-code-item border_radius4 pointer select_none"
            :class="filterObj.isBarcode === item.value ? 'active' : ''"
            v-for="item in codeList"
            :key="item.value"
            @click="
              filterObj.isBarcode = item.value;
              getList();
            "
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="flex mt24">
        <search-box
          style="width: 320px"
          :modelValue="filterObj.searchStr"
          @onInput="(value) => (filterObj.searchStr = value)"
          :placeholder="$t('receipt.searchProduct')"
          @onClear="
            filterObj.searchStr = '';
            getList();
          "
          @onSearch="getList"
        ></search-box>
      </div>
      <!-- 表格 -->
      <div class="mt16" ref="tableRef">
        <el-table
          v-loading="loading"
          :element-loading-text="$t('receipt.loadingInProgress')"
          :data="tableData"
          :height="tableHeight"
          border
          class="custom_radius_table"
          size="small"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('receipt.productName2')"
            min-width="240"
            prop="skuName"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ scope.row.skuName?.value || "" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('receipt.productCode2')"
            min-width="120"
            prop="skuCode"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ scope.row?.skuCode || "-" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('receipt.relatedSalesOrder')"
            min-width="240"
            prop="orderNo"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span
                class="fw500 pointer main-color"
                @click="
                  operate('detailsDrawer', {
                    data: scope.row,
                  })
                "
              >
                {{ scope.row.orderNo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="filterObj.isBarcode === 'YES'"
            :label="$t('receipt.serialCode')"
            min-width="312"
            prop="barcodeList"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ (scope.row?.barcodeList || []).join("、") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('receipt.unitPrice')"
            min-width="100"
            prop="unitPrice"
            align="right"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ formatMoney(scope.row.unitPrice) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="filterObj.isBarcode === 'NO'"
            :label="$t('receipt.quantity')"
            min-width="138"
            prop="quantity"
            align="right"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ scope.row?.quantity ?? 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('receipt.payableAmount')"
            min-width="100"
            prop="actualPrice"
            align="right"
            :formatter="tableFormat"
            show-overflow-tooltip
            fixed="right"
          >
            <template #default="scope">
              <span class="c_1D2330">
                {{ formatMoney(scope.row.actualPrice) }}
              </span>
            </template>
          </el-table-column>
          <template #empty>
            <empty-box></empty-box>
          </template>
        </el-table>

        <pagination
          :pageObj="pageObj"
          @currentChange="currentChange"
          @onPageChange="onPageChange"
        ></pagination>
      </div>
    </div>

    <!-- 销售单详情 -->
    <details-drawer
      v-if="operateDialog.type === 'detailsDrawer'"
      :data="operateDialog.data"
      v-model:show="operateDialog.show"
      @refresh="getList"
      noButton
    ></details-drawer>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineExpose, reactive, onUnmounted, watch, nextTick } from "vue";
import searchBox from "@/components/filter/search.vue";
import pagination from "@/components/pagination/index.vue";
import { formatMoney } from "@/util/numberUtil";
import { tableFormat } from "@/util/util";
import { ElMessage } from "element-plus";
import lang from "@/lang/index";
import emptyBox from "@/components/empty/index.vue";
import DetailsDrawer from "@views/salesOrderManage/components/details-drawer.vue";
import { getCashierDayilySku } from "@/api/cashierDayily";

const i18n = lang.global;
const show = ref(false);
const orderData = ref({} as any);
const titleStr = ref("");
const loading = ref(false);
const filterObj = reactive({
  searchStr: "",
  isBarcode: "YES",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});
const codeList = [
  {
    name: i18n.t("receipt.barcodeProduct"),
    value: "YES",
  },
  {
    name: i18n.t("receipt.noBarcodeProduct"),
    value: "NO",
  },
];

watch(
  () => show.value,
  (val) => {
    if (val) {
      nextTick(() => {
        getTableHeight();
        getList();
        window.addEventListener("resize", getTableHeight);
      });
    }
  },
  { immediate: true }
);

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 187 - 58 - 58;
};

function openDrawer({ data, title }) {
  show.value = true;
  orderData.value = data;
  titleStr.value = title;
}

function onClose() {
  show.value = false;
}

function getList() {
  loading.value = true;
  const params = Object.assign(
    {
      storeIdList: [orderData.value.storeId],
      skuFlag: "SALE",
      timeList: [orderData.value.date, orderData.value.date],
    },
    pageObj,
    filterObj
  ) as any;
  getCashierDayilySku(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        tableData.value = list;
        pageObj.total = Number(res.data.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
}

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

const operate = (type, val) => {
  operateDialog.title = val.title || "";
  operateDialog.content = val.content || "";
  operateDialog.type = type;
  operateDialog.data = val.data || {};
  operateDialog.show = true;
};

defineExpose({
  openDrawer,
});
</script>

<style lang="scss">
.title-drawer2 {
  box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);

  .el-drawer__header {
    color: #1d2330;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
    padding: 14px 24px;
    border-bottom: 1px solid #dfe2e7;
    .el-drawer__close {
      font-size: 16px;
      color: #7781a1;
    }
  }
}

.filter-code-item {
  height: 28px;
  line-height: 28px;
  background: #f5f7f9;
  margin-right: 4px;
  padding: 0 12px;

  &:last-child {
    margin-right: 0;
  }

  &.active,
  &:hover {
    background: #eff4ff;
    color: #415fff;
  }
}
</style>
