import request from './axios'
import { baseUrl, hrBaseUrl } from '@/config/env'

// 登录-pc用户登录
export const loginByUsername = (paramData: any) => request({
  url: hrBaseUrl + '/core/sys/auth/oaLogin',
  method: 'post',
  meta: {
    isToken: false
  },
  params: paramData
})

//登录-菜单和权限信息
export const getAuthorInfo = (paramData: any) => request({
  url: hrBaseUrl + '/core/sys/auth/v2/author',
  method: 'get',
  meta: {
    isToken: true,
  },
  params: paramData
});

//登录-获取用户信息
export const getUserInfo = () => request({
  url: hrBaseUrl + '/core/sys/user/getUserInfo',
  method: 'post',
});

//登录-获取人员信息
export const getPersonInfo = () => request({
  url: baseUrl + '/guidePortalHr/hrsalaryapi/assets/user/getPersonInfo',
  method: 'get',
});

//找回密码
export const retrievePasswordApi = (paramData: any) => request({
  url: hrBaseUrl + '/empmng/biz/api/sys/user/retrievePassword',
  method: 'post',
  params: paramData
})

//发送短信验证码
export const sendSmsApi = (paramData: any) => request({
  url: hrBaseUrl + '/empmng/common/api/sms/sendSms',
  method: 'post',
  params: paramData
})

//验证短信验证码
export const verifyApi = (paramData: any) => request({
  url: hrBaseUrl + '/empmng/common/api/sms/verify',
  method: 'post',
  params: paramData
})

//修改用户密码
export const editPasswordApi = (paramData: any) => request({
  url: hrBaseUrl + '/empmng/biz/api/sys/user/editPassword',
  method: 'post',
  params: paramData
})