<template>
  <el-dialog
    :model-value="props.show"
    class="common-confirm-dialog"
    :show-close="false"
    width="400px"
    append-to-body
    :before-close="onClose"
  >
    <div class="confirm-dialog-title flex align_items_center">
      <i class="iconfont-flb icon-tishi title-icon"></i>
      <span class="c_1D2330 fs16 fw600 lh24">
        {{ props.title || $t("receipt.operateTips") }}
      </span>
    </div>
    <div class="confirm-dialog-content c_1D2330 fs14 lh22">
      {{ props.content }}
    </div>
    <template #footer>
      <el-button @click="onClose">
        {{ $t("receipt.cancel") }}
      </el-button>
      <el-button type="primary" @click="onConfirm" :loading="props.loading">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineEmits, defineProps } from "vue";

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "",
  },
  show: {
    retquired: true,
    type: Boolean,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 已读
const onConfirm = () => {
  emit("confirm");
  onClose();
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>
