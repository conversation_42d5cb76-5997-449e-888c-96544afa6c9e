<template>
  <div
    v-loading="loading"
    class="p24 bgc_white position_re"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <!-- 筛选 -->
    <div class="flex justify_content_between">
      <div class="flex_shrink0">
        <el-button
          type="primary"
          @click="
            operate('addDialog', {
              data: tableData[tableData.length - 1] || {},
            })
          "
        >
          <i class="iconfont-flb icon-add mr4" style="font-size: 12px"></i>
          {{ $t("receipt.addTo") }}
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="mt16" ref="tableRef">
      <el-table
        :data="tableData"
        :height="tableHeight"
        border
        class="custom_radius_table"
        size="small"
        style="width: 100%"
      >
        <!-- 序号 -->
        <el-table-column
          :label="$t('receipt.index')"
          min-width="60"
          prop="index"
        >
          <template #default="scope">
            <span class="c_4F5363">
              {{ scope.row.sort || 0 }}
            </span>
          </template>
        </el-table-column>
        <!-- 分组名称 -->
        <el-table-column
          :label="$t('receipt.groupName')"
          min-width="330"
          prop="name"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 C_1D2330">
              {{ scope.row?.name?.value || "-" }}
            </span>
          </template>
        </el-table-column>
        <!-- 编号 -->
        <el-table-column
          :label="$t('receipt.number')"
          min-width="168"
          prop="productGroupId"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
        </el-table-column>
        <!-- 上级分组 -->
        <el-table-column
          :label="$t('receipt.parentName')"
          min-width="330"
          prop="parentName"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class=" ">
              {{ scope.row?.parentName?.value || "-" }}
            </span>
          </template>
        </el-table-column>
        <!-- 主数据状态 -->
        <el-table-column
          :label="$t('receipt.mainStatus')"
          min-width="128"
          prop="enabled"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="common-status-box"
              :class="scope.row?.enabled?.value === 'YES' ? 'green' : 'gray'"
            >
              {{
                scope.row?.enabled?.value === "YES"
                  ? $t("receipt.enable")
                  : $t("receipt.stop")
              }}
            </span>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="134"
          class-name="operate_column"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="
                operate('sortDialog', {
                  data: scope.row,
                })
              "
            >
              {{ $t("receipt.changeSort") }}
            </el-button>
            <el-divider class="ml8 mr8" direction="vertical"></el-divider>
            <el-button
              link
              type="danger"
              @click="
                operate('delDialog', {
                  data: scope.row,
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <pagination
        :showPage="false"
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>

    <!-- 新增弹框 -->
    <addDialog
      v-if="operateDialog.type === 'addDialog'"
      v-model:show="operateDialog.show"
      :sort="operateDialog.data.sort"
      @refresh="getList"
    />

    <!-- 新增弹框 -->
    <sortDialog
      v-if="operateDialog.type === 'sortDialog'"
      v-model:show="operateDialog.show"
      :data="operateDialog.data"
      @refresh="getList"
      :sort="operateDialog.data.sort"
    />

    <!-- 删除 -->
    <del-dialog
      v-if="operateDialog.type === 'delDialog'"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
      @close="operateDialog.show = false"
    ></del-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  nextTick,
  onActivated,
  onMounted,
  onUnmounted,
  reactive,
  ref,
} from "vue";
import { ElMessage } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import pagination from "@/components/pagination/index.vue";
import { tableFormat } from "@/util/util";
import lang from "@/lang/index";
import { debounce } from "lodash";
import router from "@/router";
import { delProductGroup, getProductGroupList } from "@/api/naviConfig";
import addDialog from "./components/add-dialog.vue";
import sortDialog from "./components/sort-dialog.vue";
import delDialog from "@/components/dialog/delete.vue";

const i18n = lang.global;
const tableRef = ref(null);
const filterObj = reactive({
  searchStr: "",
  orderDate: "",
  examineState: "",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {} as any,
  title: "",
  content: "",
});

onActivated(() => {
  getList();
});

onMounted(() => {
  window.addEventListener("resize", getTableHeight);
  getTableHeight();
  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 132 - 58;
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, filterObj) as any;
  getProductGroupList(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        pageObj.total = Number(res.data.total || 0);
        tableData.value = list;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};

// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

const operate = (type, val: any = {}) => {
  switch (type) {
    case "addReturnSalesOrder":
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          data: val.data as any,
        })
      );
      router.push({
        path: "/returnOrderManage/add",
      });
      break;
    case "draftBox":
      router.push({
        path: "/returnOrderManage/draftBox",
      });
      break;
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = val.data || {};
      operateDialog.show = true;
      break;
  }
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "delDialog") {
    delProductGroup({ id: data.id })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};
</script>

<style lang="scss" scoped></style>
