<template>
  <div>
    <el-form
      class="login-form"
      :rules="loginRules"
      ref="loginFormRef"
      :model="loginForm"
      label-width="0"
      size="large"
    >
      <!-- 手机号 -->
      <el-form-item prop="account">
        <el-input
          @keyup.enter="handleLogin"
          v-model="loginForm.account"
          placeholder="请输入手机号"
        >
          <template #prefix><i class="icon-31dianhua c_0BB4B4"></i></template>
        </el-input>
      </el-form-item>

      <!-- 密码 -->
      <el-form-item prop="password">
        <el-input
          @keyup.enter="handleLogin"
          :type="passwordType"
          v-model="loginForm.password"
          placeholder="请输入密码"
        >
          <template #prefix><i class="icon-lock-on c_0BB4B4"></i></template>
          <template #suffix>
            <i
              :class="
                passwordType === 'password' ? 'icon-browse-off' : 'icon-browse'
              "
              class="iconfont-flb c_0BB4B4"
              @click="showPassword"
            ></i>
          </template>
        </el-input>
      </el-form-item>

      <!-- 验证码 -->
      <el-form-item prop="code">
        <el-row :span="24">
          <!-- <el-col :span="18"> -->
          <el-input
            @keyup.enter="handleLogin"
            :maxlength="code.len"
            v-model="loginForm.code"
            style="width: 244px"
            :placeholder="'请输入验证码'"
          >
            <template #prefix
              ><i class="icon-yanzhengma c_0BB4B4"></i
            ></template>
          </el-input>
          <!-- </el-col> -->
          <!-- <el-col :span="6"> -->
          <div class="login-code">
            <span
              class="login-code-img"
              @click="refreshCode"
              v-if="code.type == 'text'"
            >
              <span
                v-for="(item, index) in code.value"
                :key="index"
                :style="{ color: code.colorList[index] }"
              >
                {{ item }}</span
              ></span
            >
            <img
              :src="code.src"
              class="login-code-img"
              @click="refreshCode"
              v-else
            />
          </div>
          <!-- </el-col> -->
        </el-row>
      </el-form-item>

      <!-- 登录 -->
      <el-form-item style="margin-top: 40px; margin-bottom: 0">
        <el-button
          type="primary"
          @click.prevent="handleLogin"
          class="login-submit"
          >登录
        </el-button>
      </el-form-item>

      <div class="bottom-operation mt10">
        <el-checkbox v-model="isRemember">记住密码</el-checkbox>
        <span class="float-r main-color" @click="onForget">忘记密码</span>
      </div>
    </el-form>

    <!-- 切换Bu -->
    <el-dialog
      class="select-bu-dialog"
      title="切换登录地"
      append-to-body
      v-model="changeBuDialog"
      width="347px"
    >
      <el-radio-group v-model="changeBuId">
        <el-radio
          :label="item.buId"
          v-for="(item, index) in buIdList"
          :key="index"
          >{{ item.name }}
        </el-radio>
      </el-radio-group>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="changeBuDialog = false"
            >取消</el-button
          >
          <el-button type="primary" size="small" @click="onChangeConfirm"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { randomLenNum } from "@/util/util";
import { setStore, getStore } from "@/util/store";
import { setTheme } from "@/util/util";
import { FormInstance, ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { ref, reactive, onMounted, defineEmits } from "vue";
import store from "@/store";
import website from "@/config/website";

const emits = defineEmits(["changeActiveName"]);
const router = useRouter();
const changeBuDialog = ref(false);
const isRemember = ref(false);
const loginFormRef = ref<FormInstance>();
const loginForm = reactive({
  moduleId: website.moduleId,
  entityType: 1,
  isEmployee: 1,
  account: "",
  password: "",
  type: 2, // 1:账号 2：手机 3：邮箱
  code: "",
  redomStr: "",
  buId: "",
});
const code = reactive({
  src: "",
  value: "",
  len: 4,
  type: "text",
  colorList: [],
});
const validateCode = (rule, value, callback) => {
  if (code.value != value) {
    callback(new Error("请输入正确的验证码"));
  } else {
    callback();
  }
};
const loginRules = {
  account: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度最少为6位", trigger: "blur" },
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { min: 4, max: 4, message: "验证码长度为4位", trigger: "blur" },
    { required: true, trigger: "blur", validator: validateCode },
  ],
};
const passwordType = ref("password");
const buIdList = ref([]);
const changeBuId = ref("");

onMounted(() => {
  refreshCode();
  getLoginInfoLocal();
});
//得到随机的颜色值
const randomColor = () => {
  let r = Math.floor(Math.random() * 256);
  let g = Math.floor(Math.random() * 256);
  let b = Math.floor(Math.random() * 256);
  return "rgb(" + r + "," + g + "," + b + ")";
};
//确认切换Bu
const onChangeConfirm = () => {
  loginForm.buId = changeBuId.value;
  setTheme("theme-default"); //设置主题
  changeBuDialog.value = false;
};
//点击忘记密码
const onForget = () => {
  emits("changeActiveName", "forget");
};
//刷新验证码
const refreshCode = () => {
  loginForm.redomStr = randomLenNum(code.len, true);
  code.colorList = [];
  code.value = randomLenNum(code.len, false);
  for (let i = 0; i < code.len; i++) {
    code.colorList.push(randomColor());
  }
};
//密码显示隐藏切换
const showPassword = () => {
  passwordType.value == ""
    ? (passwordType.value = "password")
    : (passwordType.value = "");
};

//点击登录
const handleLogin = () => {
  loginFormRef.value.validate((valid) => {
    if (valid) {
      store
        .dispatch("user/LoginByUsername", loginForm)
        .then((resData) => {
          //将登录信息保存到本地
          setLoginInfoLocal();
          //跳转到主页
          // let menu = resData.menu[0].path;
          // if (resData.menu[0].children.length > 0) {
          //   menu = resData.menu[0].children[0].path;
          // }
          // router.push({ path: menu });
          router.push({ path: website.fistPage.value });
        })
        .catch((err) => {
          ElMessage.error(err);
        });
    }
  });
};
//从本地获取登录信息
const getLoginInfoLocal = () => {
  let loginInfo = getStore({ name: "loginInfo" });
  if (loginInfo) {
    isRemember.value = loginInfo.isRemember;
    changeBuId.value = loginInfo.belongBuId;
    buIdList.value = loginInfo.belongBuList;
    loginForm.buId = loginInfo.belongBuId;
    loginForm.account = loginInfo.account;
    loginForm.password = loginInfo.password;
  }
};

//将登录信息存储本地
const setLoginInfoLocal = () => {
  let loginInfo = {
    isRemember: isRemember.value,
    belongBuList: buIdList.value,
    belongBuId: loginForm.buId,
    account: "",
    password: "",
  };

  if (isRemember.value) {
    loginInfo.account = loginForm.account;
    loginInfo.password = loginForm.password;
  }
  setStore({ name: "loginInfo", content: loginInfo });
};
</script>

<style lang="scss" scoped>
.content-form {
  padding: 0 22px 10px 22px;

  .title-box {
    padding-bottom: 8px;
    line-height: 44px;
    height: 44px;
    font-size: 32px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
  }

  .title-tip {
    height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    line-height: 24px;
    padding-bottom: 40px;
  }

  .el-form-item {
    margin-bottom: 32px;
  }

  .button-item {
    padding-top: 8px;
  }

  .forgetPwd-box {
    width: 388px;
    height: 22px;
    font-size: 14px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #0054ff;
    line-height: 22px;
    text-align: right;
    margin-top: 24px;
    cursor: pointer;
  }

  .float-r {
    width: 388px;
    height: 46px;
    background: #0054ff;
    border-radius: 4px 4px 4px 4px;

    span {
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
    }
  }
}

.select-bu-dialog {
  :deep(.el-radio-group) {
    display: block;
    height: 204px;
    overflow-y: auto;
  }

  :deep(.el-radio) {
    display: block;
    margin-bottom: 16px;
  }

  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
  }

  :deep(.el-radio__label) {
    font-size: 14px;
    color: #666666;
  }
}
</style>
