import request from "./axios";
import { hrBaseUrl, baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 中台-用户-分页
export const getUserPageApi = (data: Params) =>
  request({
    url: baseUrl + "/guidePortalHr/hrsalaryapi/assets/user/page",
    method: "post",
    data,
  });

// 中台-用户-添加
export const saveHrUserApi = (data: Params) =>
  request({
    url: baseUrl + "/guidePortalHr/hrsalaryapi/assets/user/save",
    method: "post",
    data,
  });

// 中台-用户-模块事业线-删除
export const delHrUserApi = (data: Params) =>
  request({
    url: hrBaseUrl + "/core/sys/user/delBuMod",
    method: "post",
    params: data,
  });

// 根据user及module获取账户管理信息，批量
export const getUserInfoApi = (data: Params) =>
  request({
    url: baseUrl + "/sys/sysUserManage/getByUserAndModule",
    method: "post",
    data,
  });

// 菲律宾-用户-新增
export const saveUserApi = (data: Params) =>
  request({
    url: baseUrl + "/sys/sysUserManage/",
    method: "post",
    data,
  });

// 菲律宾-用户-修改
export const updateUserApi = (data: Params) =>
  request({
    url: baseUrl + "/sys/sysUserManage/",
    method: "PUT",
    data,
  });

// 菲律宾-用户-删除
export const delUserApi = (id: string) =>
  request({
    url: baseUrl + "/sys/sysUserManage/" + id,
    method: "DELETE",
  });
