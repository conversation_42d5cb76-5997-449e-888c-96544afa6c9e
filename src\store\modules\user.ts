import { setToken, getToken, removeToken } from "@/util/auth";
import { setStore, getStore } from "@/util/store";
import Cookies from "js-cookie";
import { isURL, validatenull } from "@/util/validate";
import { deepClone } from "@/util/util";
import website from "@/config/website";
import {
  loginByUsername,
  getAuthorInfo,
  getUserInfo,
  getPersonInfo,
} from "@/api/user";
import { mainStore } from "../../storePinia/index";

function addPath(ele: any, first?: boolean) {
  const menu = website.menu;
  const propsConfig = menu.props;
  const propsDefault = {
    label: propsConfig.label || "label",
    path: propsConfig.path || "path",
    icon: propsConfig.icon || "icon",
    children: propsConfig.children || "children",
  };
  const icon = ele[propsDefault.icon];
  ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon;
  const isChild =
    ele[propsDefault.children] && ele[propsDefault.children].length !== 0;
  if (isURL(ele[propsDefault.path])) {
    ele[propsDefault.path] = ele[propsDefault.path].replace(/&/g, "$");
  }
  if (!isChild && first && !isURL(ele[propsDefault.path])) {
    // ele[propsDefault.path] = ele[propsDefault.path] + '/index'
  } else {
    ele[propsDefault.children] &&
      ele[propsDefault.children].forEach((child: any) => {
        if (!isURL(child[propsDefault.path])) {
          // child[propsDefault.path] = `${ele[propsDefault.path]}/${child[propsDefault.path] ? child[propsDefault.path] : 'index'}`
        }
        addPath(child);
      });
  }
}

const user = {
  namespaced: true,
  state: {
    userInfo: getStore({ name: "userInfo" }) || {},
    personInfo: getStore({ name: "personInfo" }) || {},
    permission: {},
    roles: [],
    menuId: getStore({ name: "menuId" }) || [],
    menu: getStore({ name: "menu" }) || [],
    menuAll: getStore({ name: "menuAll" }) || [],
    menuChildren: getStore({ name: "menuChildren" }) || [],
    activeMenu: getStore({ name: "activeMenu" }) || {},
    token: getToken() || "",
    currencyInfo: getStore({ name: 'currencyInfo' }) || {}
  },
  actions: {
    //根据用户名登录
    LoginByUsername({ dispatch, commit }: any, userInfo: any) {
      return new Promise<void>((resolve, reject) => {
        const storePinia = mainStore();
        const shUserInfo = {
          userCode: userInfo.account,
          password: userInfo.pwd,
        };
        loginByUsername(userInfo)
          .then((res: any) => {
            if (res.code == 200) {
              const data = res.data;
              storePinia.setUserInfo(res.data.user);
              const userInfoObj = data.user;
              if (data.customerInfo && data.customerInfo.id) {
                userInfoObj.customerId = data.customerInfo.id;
                userInfoObj.resumeName = data.customerInfo.resumeName;
                userInfoObj.resumeUrl = data.customerInfo.resumeUrl;
              }
              commit("SET_TOKEN", data.token);
              commit("SET_USERIFNO", userInfoObj);
              commit("SET_MENUALL", data.menu);
              commit("tags/DEL_ALL_TAG", {}, { root: true });
              commit("common/CLEAR_LOCK", {}, { root: true });

              resolve(data);
            } else {
              reject(res.msg);
            }
          })
          .catch((err: any) => {
            reject(err);
          });
      });
    },
    //获取菜单权限信息
    GetAuthorInfo({ commit }: any) {
      const json_data = { moduleId: website.moduleId, menuType: 1 };
      return new Promise<void>((resolve, reject) => {
        getAuthorInfo(json_data)
          .then((res: any) => {
            const data = res.data;

            if (data.role) {
              commit("SET_ROLES", data.role);
            } else {
              commit("SET_ROLES", []);
            }

            if (data.button) {
              commit("SET_PERMISSION", data.button);
            } else {
              commit("SET_PERMISSION", []);
            }

            if (data.menu) {
              const menu = deepClone(data.menu);
              menu.forEach((ele: any) => {
                addPath(ele, true);
              });
              commit("SET_MENUALL", menu);
              const tempArr = JSON.parse(JSON.stringify(menu));
              commit("SET_MENU", tempArr);
            } else {
              commit("SET_MENUALL", []);
              commit("SET_MENU", []);
            }
            resolve(data);
          })
          .catch((err: any) => {
            reject(err);
          });
      });
    },

    //获取用户信息
    GetUserInfo({ dispatch, commit }: any) {
      return new Promise<void>((resolve, reject) => {
        getUserInfo()
          .then((res: any) => {
            const data = res.data;
            if (res.success) {
              commit("SET_USERIFNO", data);
              resolve();
            } else {
              commit("SET_USERIFNO", {});
              reject(res.msg);
            }
          })
          .catch((err: any) => {
            reject(err);
          });
      });
    },

    //获取人员信息(包括人员名称，编号，id等信息)
    GetPersonInfo({ commit }: any) {
      return new Promise((resolve, reject) => {
        getPersonInfo()
          .then((res: any) => {
            const data = res.data;
            if (res.success && data) {
              const personData = {
                personId: data.id,
                personCode: data.code,
                personName: data.name,
                departmentId: data.departmentId,
                departmentName: data.departmentName,
                jobId: data.jobId,
                jobName: data.jobName,
                jobTitle: data.jobTitle,
                entryDate: data.entryDate,
                picture: data.picture,
                phone: data.phone,
              };
              commit("SET_PERSONIFNO", personData);
            } else {
              commit("SET_PERSONIFNO", {});
            }
            resolve(data);
          })
          .catch((err: any) => {
            reject(err);
          });
      });
    },
    // 登出
    LogOut({ commit }: any) {
      return new Promise<void>((resolve) => {
        //和token相关的选项必须在此清除
        commit("SET_TOKEN", "");
        commit("SET_MENUID", {});
        commit("SET_MENUALL", []);
        commit("SET_MENU", []);
        commit("tags/SET_TAG_LIST", [], { root: true });
        commit("SET_ROLES", []);
        commit("tags/DEL_ALL_TAG", {}, { root: true });
        commit("common/CLEAR_LOCK", {}, { root: true });
        removeToken();

        //和token相关的options
        commit("options/SET_DEPART_OPTION", []);
        commit("SET_ACTIVE_MENU", {});

        resolve();
      });
    },
    //注销session
    FedLogOut({ commit }: any) {
      return new Promise<void>((resolve) => {
        //和token相关的选项必须在此清除
        commit("SET_TOKEN", "");
        commit("SET_MENUID", {});
        commit("SET_MENUALL", []);
        commit("SET_MENU", []);
        commit("tags/SET_TAG_LIST", [], { root: true });
        commit("SET_ROLES", []);
        commit("tags/DEL_ALL_TAG", {}, { root: true });
        commit("common/CLEAR_LOCK", {}, { root: true });
        removeToken();

        //和token相关的options
        commit("options/SET_DEPART_OPTION", []);
        commit("SET_ACTIVE_MENU", {});

        resolve();
      });
    },
    //获取顶部菜单
    GetTopMenu() {
      return new Promise((resolve) => {
        const topMenu = [];
        resolve(topMenu);
      });
    },
    //获取系统菜单
    GetMenu() {
      return new Promise((resolve) => {
        const menu = getStore({ name: "menuAll" })!.reverse(); // getStore取出来的值为相反的
        resolve(menu);
      });
    },
  },
  mutations: {
    SET_TOKEN: (state: { token: any }, token: string) => {
      setToken(token);
      state.token = token;
      setStore({ name: "token", content: state.token });
    },
    SET_MENUID(state: { menuId: any }, menuId: any) {
      state.menuId = menuId;
      setStore({ name: "menuId", content: state.menuId, type: "session" });
    },
    SET_USERIFNO: (state: { userInfo: any }, userInfo: any) => {
      state.userInfo = userInfo;
      setStore({ name: "userInfo", content: state.userInfo });
    },
    SET_PERSONIFNO: (state: { personInfo: any }, personInfo: any) => {
      state.personInfo = personInfo;
      setStore({ name: "personInfo", content: state.personInfo });
    },
    SET_MENUALL: (state: { menuAll: any }, menuAll: any) => {
      state.menuAll = menuAll;
      setStore({ name: "menuAll", content: state.menuAll });
    },
    SET_MENU: (state: { menu: any; menuAll: any[] }, menu: any) => {
      state.menu = menu;
      setStore({ name: "menu", content: state.menu });
      if (validatenull(menu)) return;
      //合并动态路由去重
      let menuAll = state.menuAll;
      menuAll = menuAll.concat(menu).reverse();
      const newMenu = [];
      for (const item1 of menuAll) {
        let flag = true;
        for (const item2 of newMenu) {
          if (item1.label == item2.label || item1.path == item2.path) {
            flag = false;
          }
        }
        if (flag) newMenu.push(item1);
      }
      state.menuAll = newMenu;
      setStore({ name: "menuAll", content: state.menuAll, type: "session" });
    },
    SET_ROLES: (state: { roles: any }, roles: any) => {
      state.roles = roles;
    },
    SET_PERMISSION: (
      state: { permission: { [x: string]: boolean } },
      permission: any[]
    ) => {
      const inFifteenMinutes = new Date(
        new Date().getTime() + website.tokenTime * 1000
      );
      state.permission = {};
      permission.forEach((ele: string | number) => {
        state.permission[ele] = true;
      });
      // setStore({ name: 'hr-salary-permission', content: state.permission })
      Cookies.set("hr-salary-permission", JSON.stringify(state.permission), {
        expires: inFifteenMinutes,
      });
    },
    SET_MENU_CHILD: (state: any, menuChildren: any) => {
      state.menuChildren = menuChildren;
      setStore({ name: "menuChildren", content: state.menuChildren });
    },
    SET_ACTIVE_MENU: (state: any, activeMenu: any) => {
      state.activeMenu = activeMenu;
      setStore({ name: "activeMenu", content: state.activeMenu });
    },
    SET_CURRENCYINFO: (state, currencyInfo) => {
      state.currencyInfo = currencyInfo;
      setStore({ name: 'currencyInfo', content: currencyInfo })
    },
  },
};

export default user;
