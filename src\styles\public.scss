$border-color: #dfe2e7;

// 定位
.position_re {
  position: relative;
}

.position_ab {
  position: absolute;
}

.position_fi {
  position: fixed;
}

// 布局
.block {
  display: block;
}

.inline_block {
  display: inline-block;
}

.display_no {
  display: none;
}

//grid 布局
.grid {
  display: grid;
}

// flex布局
.flex {
  display: flex;
}

.flex_inline {
  display: inline-flex;
}

// 自动换行
.flex_wrap {
  flex-wrap: wrap;
}

.flex_direction_column {
  flex-direction: column;
}

.flex_flow_column {
  flex-flow: column;
}

.flex_grow_1 {
  flex-grow: 1;
}

.flex1 {
  flex: 1;
}

.flex_shrink0 {
  flex-shrink: 0;
}

.justify_content_start {
  justify-content: flex-start;
}

.justify_content_center {
  justify-content: center;
}

.justify_content_end {
  justify-content: flex-end;
}

.justify_content_between {
  justify-content: space-between;
}

.align_items_center {
  align-items: center;
}

.align_items_start {
  align-items: flex-start;
}

.align_items_end {
  align-items: flex-end;
}

.align_items_baseline {
  align-items: baseline;
}

.align_middle {
  vertical-align: middle;
}

.align_bottom {
  vertical-align: bottom;
}

.gap8 {
  gap: 8px;
}

// 外边距
.m_auto {
  margin: 0 auto;
}

.m16 {
  margin: 16px;
}

.m24 {
  margin: 24px;
}

// 上外边距
.mt4 {
  margin-top: 4px;
}

.mt6 {
  margin-top: 6px;
}

.mt8 {
  margin-top: 8px;
}

.mt10 {
  margin-top: 10px;
}

.mt12 {
  margin-top: 12px;
}

.mt14 {
  margin-top: 14px;
}

.mt16 {
  margin-top: 16px;
}

.mt20 {
  margin-top: 20px;
}

.mt22 {
  margin-top: 22px;
}

.mt24 {
  margin-top: 24px;
}

.mt28 {
  margin-top: 28px;
}

.mt32 {
  margin-top: 32px;
}

.mt38 {
  margin-top: 38px;
}

.mt40 {
  margin-top: 40px;
}

// 右外边距
.mr2 {
  margin-right: 2px;
}

.mr4 {
  margin-right: 4px;
}

.mr6 {
  margin-right: 6px;
}

.mr8 {
  margin-right: 8px;
}

.mr10 {
  margin-right: 10px;
}

.mr12 {
  margin-right: 12px;
}

.mr16 {
  margin-right: 16px;
}

.mr20 {
  margin-right: 20px;
}

.mr24 {
  margin-right: 24px;
}

.mr32 {
  margin-right: 32px;
}

// 下外边距
.mb0 {
  margin-bottom: 0;
}

.mb2 {
  margin-bottom: 2px;
}

.mb4 {
  margin-bottom: 4px;
}

.mb6 {
  margin-bottom: 6px;
}

.mb8 {
  margin-bottom: 8px;
}

.mb10 {
  margin-bottom: 10px;
}

.mb12 {
  margin-bottom: 12px;
}

.mb16 {
  margin-bottom: 16px;
}

.mb20 {
  margin-bottom: 20px;
}

.mb22 {
  margin-bottom: 22px;
}

.mb24 {
  margin-bottom: 24px;
}

.mb32 {
  margin-bottom: 32px;
}

.mb38 {
  margin-bottom: 38px;
}

.mb40 {
  margin-bottom: 40px;
}

// 左外边距
.ml2 {
  margin-left: 2px;
}

.ml4 {
  margin-left: 4px;
}

.ml6 {
  margin-left: 6px;
}

.ml8 {
  margin-left: 8px;
}

.ml10 {
  margin-left: 10px;
}

.ml12 {
  margin-left: 12px;
}

.ml16 {
  margin-left: 16px;
}

.ml20 {
  margin-left: 20px;
}

.ml24 {
  margin-left: 24px;
}

.ml32 {
  margin-left: 32px;
}

.ml48 {
  margin-left: 48px;
}

// 内边距
.p16 {
  padding: 16px;
}

.p24 {
  padding: 24px;
}

// 上内边距
.pt2 {
  padding-top: 2px;
}
.pt3 {
  padding-top: 3px;
}

.pt4 {
  padding-top: 4px;
}
.pt6 {
  padding-top: 6px;
}

.pt8 {
  padding-top: 8px;
}

.pt10 {
  padding-top: 10px;
}

.pt12 {
  padding-top: 12px;
}

.pt14 {
  padding-top: 14px;
}

.pt16 {
  padding-top: 16px;
}

.pt20 {
  padding-top: 20px;
}

.pt24 {
  padding-top: 24px;
}

.pt32 {
  padding-top: 32px;
}

.pt40 {
  padding-top: 40px;
}

// 右内边距
.pr2 {
  padding-right: 2px;
}
.pr4 {
  padding-right: 4px;
}

.pr8 {
  padding-right: 8px;
}

.pr10 {
  padding-right: 10px;
}

.pr12 {
  padding-right: 12px;
}

.pr14 {
  padding-right: 14px;
}

.pr16 {
  padding-right: 16px;
}

.pr20 {
  padding-right: 20px;
}

.pr24 {
  padding-right: 24px;
}

.pr32 {
  padding-right: 32px;
}

// 下内边距
.pb2 {
  padding-bottom: 2px;
}
.pb3 {
  padding-bottom: 3px;
}

.pb4 {
  padding-bottom: 4px;
}
.pb6 {
  padding-bottom: 6px;
}

.pb8 {
  padding-bottom: 8px;
}

.pb10 {
  padding-bottom: 10px;
}

.pb12 {
  padding-bottom: 12px;
}

.pb14 {
  padding-bottom: 14px;
}

.pb16 {
  padding-bottom: 16px;
}

.pb20 {
  padding-bottom: 20px;
}

.pb24 {
  padding-bottom: 24px;
}

.pb32 {
  padding-bottom: 32px;
}

.pb40 {
  padding-bottom: 40px;
}

.pb48 {
  padding-bottom: 48px;
}

// 左内边距
.pl2 {
  padding-left: 2px;
}
.pl4 {
  padding-left: 4px;
}

.pl8 {
  padding-left: 8px;
}

.pl10 {
  padding-left: 10px;
}

.pl12 {
  padding-left: 12px;
}

.pl14 {
  padding-left: 14px;
}

.pl16 {
  padding-left: 16px;
}

.pl20 {
  padding-left: 20px;
}

.pl22 {
  padding-left: 22px;
}

.pl24 {
  padding-left: 24px;
}

.pl32 {
  padding-left: 32px;
}

// 上下内边距
.ptb8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.ptb12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.ptb16 {
  padding-top: 16px;
  padding-bottom: 16px;
}

//左右内边距
.plr16 {
  padding-left: 16px;
  padding-right: 16px;
}

.plr12 {
  padding-left: 12px;
  padding-right: 12px;
}

// 高度
.heightP100 {
  height: 100%;
}

.height12 {
  height: 12px;
}

.h28 {
  height: 28px;
}

// 宽度
.widthP100 {
  width: 100%;
}

.width3 {
  width: 3px;
}

.width168 {
  width: 168px;
}

// 字体大小
.fs6 {
  font-size: 6px;
}

.fs12 {
  font-size: 12px;
}

.fs14 {
  font-size: 14px;
}

.fs16 {
  font-size: 16px;
}

.fs18 {
  font-size: 18px;
}

.fs20 {
  font-size: 20px;
}

.fs22 {
  font-size: 22px;
}

.fs24 {
  font-size: 24px;
}

.fs26 {
  font-size: 26px;
}

.fs28 {
  font-size: 28px;
}

.fs30 {
  font-size: 30px;
}

.fs32 {
  font-size: 32px;
}

.fs36 {
  font-size: 36px;
}

// 行高
.lh_c {
  line-height: 1.5;
}
.lh16 {
  line-height: 16px;
}

.lh18 {
  line-height: 18px;
}

.lh24 {
  line-height: 24px;
}

.lh20 {
  line-height: 20px;
}

.lh22 {
  line-height: 22px;
}

.lh24 {
  line-height: 24px;
}

.lh28 {
  line-height: 28px;
}

.lh30 {
  line-height: 30px;
}

.lh32 {
  line-height: 32px;
}

.lh36 {
  line-height: 36px;
}

// 文字对齐方式
.t_left {
  text-align: left;
}

.t_right {
  text-align: right;
}

.t_center {
  text-align: center;
}

// 浮动
.f_left {
  float: left;
}

.f_right {
  float: right;
}

.clearFloat {
  clear: both;
}

// 文字加粗
.fw400 {
  font-weight: 400;
}

.fw500 {
  font-weight: 500;
}

.fw550 {
  font-weight: 550;
}

.fw600 {
  font-weight: 600;
}

.fw_bold {
  font-weight: bold;
}

.white_nowrap {
  white-space: nowrap;
}

// 文本截取
.text_over {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 圆角
.border_radius2 {
  border-radius: 2px;
}

.border_radius4 {
  border-radius: 4px;
}

.border_radius8 {
  border-radius: 8px;
}

.border_radius50 {
  border-radius: 50%;
}

//左下右下圆角
.border_radius4_bottom {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

// 鼠标手势
.pointer {
  cursor: pointer;
}

.float-right {
  float: right;
}

// 文字颜色
.c_white {
  color: #ffffff;
}

.c_1D2330 {
  color: #1d2330;
}

.c_4F5363 {
  color: #4f5363;
}

.c_989CAC {
  color: #989cac;
}

.c_EC2D30 {
  color: #ec2d30;
}

.c_FE9B0E {
  color: #fe9b0e;
}

.c_415FFF {
  color: #415fff;
}

.c_3D3D3D {
  color: #3d3d3d;
}

.c_172136 {
  color: #172136;
}

.c_9EA2A9 {
  color: #9ea2a9;
}

// 背景颜色
.bgc_white {
  background-color: #ffffff;
}

.bgc_FAFBFC {
  background-color: #fafbfc;
}

.bgc_EFF4FF {
  background-color: #eff4ff;
}

.bgc_F5F7F9 {
  background-color: #f5f7f9;
}

.bgc_EDF0F2 {
  background-color: #edf0f2;
}

.bgc_F3F6F8 {
  background-color: #f3f6f8;
}

.bgc_F3F4FB {
  background-color: #f3f4fb;
}
.bgc_FFF5E2 {
  background-color: #fff5e2;
}

// 边框
.border_none {
  border: none;
}

.border_top_none {
  border-top: none !important;
}

.border_bottom_none {
  border-bottom: none !important;
}

.bt_solid_default {
  border-top: 1px solid $border-color;
}

.bb_solid_default {
  border-bottom: 1px solid $border-color;
}

.bl_solid_default {
  border-left: 1px solid $border-color;
}

.br_solid_default {
  border-right: 1px solid $border-color;
}

.bt_dashed_default {
  border-top: 1px dashed $border-color;
}

.bb_dashed_default {
  border-bottom: 1px dashed $border-color;
}

.b_solid_default {
  border: 1px solid $border-color;
}

.b_dashed_default {
  border: 1px dashed $border-color;
}

// 滚动
.overflow_y_auto {
  overflow-y: auto;
}

.overflow_hidden {
  overflow: hidden;
}

//分割线
.dash-divide {
  height: 1px;
  border-bottom: 1px dashed #d6dce1;
}
