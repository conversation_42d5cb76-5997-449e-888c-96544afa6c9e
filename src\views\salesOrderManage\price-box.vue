<template>
  <div class="pt16 pb16 pl24 pr24 position_re bgc_white heightP100">
    <div class="heightP100 min-height flex flex_direction_column">
      <div>
        <div class="common-crumbs-box mb16">
          <span class="hover-span" @click="onClose">
            {{ $t("receipt.salesBilling") }}
          </span>
          <el-icon class="ml8 mr8 c_4F5363">
            <ArrowRight />
          </el-icon>
          <span class="current-span">
            {{ $t("receipt.priceChange") }}
          </span>
        </div>
      </div>
      <!-- 筛选 -->
      <!-- <div class="flex justify_content_between">
        <div class="flex_shrink0">
          <el-button type="primary" @click="operate('addSalesOrderPage')">
            <i class="iconfont-flb icon-add mr2"></i>
            {{ $t("receipt.addSalesOrder") }}
          </el-button>
        </div>
      </div> -->
      <!-- 表格 -->
      <div class="flex1" ref="tableRef">
        <el-table
          v-loading="loading"
          :element-loading-text="$t('receipt.loadingInProgress')"
          :data="tableData"
          :height="tableHeight"
          border
          class="custom_radius_table"
          size="small"
          style="width: 100%"
        >
          <!-- 订单编号 -->
          <el-table-column
            :label="$t('receipt.orderNo')"
            min-width="180"
            prop="orderNo"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span
                class="fw500 pointer main-color"
                @click="
                  operate('detailsDrawer', {
                    data: scope.row,
                  })
                "
              >
                {{ scope.row.orderNo }}
              </span>
            </template>
          </el-table-column>
          <!-- 开单日期 -->
          <el-table-column
            :label="$t('receipt.orderDate')"
            min-width="128"
            prop="orderDate"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
          </el-table-column>
          <!-- 会员 -->
          <el-table-column
            :label="$t('receipt.member')"
            min-width="128"
            prop="memberName"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ scope.row.memberName || "-" }}</span>
            </template>
          </el-table-column>
          <!-- 门店 -->
          <el-table-column
            :label="$t('receipt.store')"
            min-width="200"
            prop="storeName"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ scope.row.storeName?.value }}</span>
            </template>
          </el-table-column>
          <!-- 优惠前总价 -->
          <el-table-column
            :label="$t('receipt.beforeTotalDiscountPrice')"
            min-width="128"
            prop="originalPrice"
            align="right"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="fw500">
                {{ formatMoney(scope.row.originalPrice) }}
              </span>
            </template>
          </el-table-column>
          <!-- 优惠后总价 -->
          <el-table-column
            :label="$t('receipt.afterTotalDiscountPrice')"
            min-width="128"
            prop="actualPrice"
            align="right"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="fw500">
                {{ formatMoney(scope.row.actualPrice) }}
              </span>
            </template>
          </el-table-column>
          <!-- 销售人员 -->
          <el-table-column
            :label="$t('receipt.salePerson')"
            min-width="128"
            prop="salespersonName"
            :formatter="tableFormat"
            show-overflow-tooltip
          />
          <!-- 审核人员 -->
          <el-table-column
            :label="$t('receipt.reviewer')"
            min-width="128"
            prop="examineName"
            :formatter="tableFormat"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            :label="$t('receipt.remark')"
            min-width="188"
            prop="orderRemarks"
            :formatter="tableFormat"
            show-overflow-tooltip
          />
          <!-- <el-table-column
            fixed="right"
            :label="$t('receipt.operation')"
            width="120"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="
                  operate('addSalesOrderPage', {
                    data: scope.row,
                  })
                "
              >
                {{ $t("receipt.edit") }}
              </el-button>
              <el-divider class="ml8 mr8" direction="vertical"></el-divider>
              <el-button
                link
                type="danger"
                @click="
                  operate('delDialog', {
                    data: scope.row,
                  })
                "
              >
                {{ $t("receipt.delete") }}
              </el-button>
            </template>
          </el-table-column> -->
          <template #empty>
            <empty-box></empty-box>
          </template>
        </el-table>

        <pagination
          :pageObj="pageObj"
          @currentChange="currentChange"
          @onPageChange="onPageChange"
        ></pagination>
      </div>
    </div>
  </div>

  <!-- 删除提示 -->
  <stop-dialog
    v-if="operateDialog.type === 'delDialog'"
    :content="$t('receipt.deletePromptContent')"
    v-model:show="operateDialog.show"
    @confirm="stopDialogConfirm"
    :loading="deleteLoading"
  ></stop-dialog>

  <!-- 销售单详情 -->
  <details-drawer
    v-if="operateDialog.type === 'detailsDrawer'"
    :data="operateDialog.data"
    v-model:show="operateDialog.show"
    @refresh="getList"
  ></details-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, onUnmounted, onActivated, nextTick } from "vue";
import pagination from "@/components/pagination/index.vue";
import { ArrowRight } from "@element-plus/icons-vue";
import emptyBox from "@/components/empty/index.vue";
import DetailsDrawer from "@views/salesOrderManage/components/details-drawer.vue";
import { deleteOrder, getOrderInfo } from "@/api/salesOrderManage";
import { ElMessage } from "element-plus";
import { tableFormat } from "@/util/util";
import { formatMoney } from "@/util/numberUtil";
import { onMounted } from "vue";
import stopDialog from "@/components/dialog/delete.vue";
import lang from "@/lang/index";
import router from "@/router";

const i18n = lang.global;
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const operateDialog = reactive({
  show: false,
  type: "",
  data: null,
  title: "",
});
const tableHeight = ref(0);
const loading = ref(false);
const tableData = ref([]);
const deleteLoading = ref(false);
const tableRef = ref(null);

onActivated(() => {
  getList();
});

onMounted(() => {
  window.addEventListener("resize", getTableHeight);
  getTableHeight();
  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

// 计算表格高度
const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 120 - 58;
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, {
    examineState: "AUDIT_SUCCESS",
    isModify: "YES",
  });
  getOrderInfo(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        tableData.value = list;
        pageObj.total = Number(res.data.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// 关闭
const onClose = () => {
  router.back();
};

// 操作
const operate = (type, val: any = {}) => {
  switch (type) {
    case "addSalesOrderPage":
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          type: type === "addSalesOrderPage" ? "add" : "price",
          data: val.data as any,
        })
      );
      router.push({
        path: "/salesOrderManage/add",
      });
      break;
    default:
      operateDialog.type = type;
      operateDialog.data = val.data || {};
      operateDialog.show = true;
      break;
  }
};

const stopDialogConfirm = () => {
  deleteLoading.value = true;
  deleteOrder({
    id: operateDialog.data.id,
  }).then((res) => {
    deleteLoading.value = false;
    if (res.code == 200) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};
</script>

<style lang="scss" scoped>
:deep(.cell) {
  color: #4f5363 !important;
}
</style>
