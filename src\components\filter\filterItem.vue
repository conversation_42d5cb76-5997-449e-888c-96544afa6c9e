<template>
  <div class="flex align_items_start" v-if="filterArr.length > 0" style="">
    <div class="c_606171 fs12">{{ $t("receipt.filterM") }}</div>
    <div class="flex_expanded flex flex-wrap align_items_start">
      <div
        class="border_radius4 bgc_EFF4FF pl4 pr4 flex align_items_center ml4 hl16 mb4"
        v-for="(item, index) in filterArr"
        :key="index"
      >
        <div class="fs12">{{ item.value }}</div>
        <i
          class="iconfont-flb icon-guanbishaixuanxiang c_1D2330 pointer ml4"
          style="font-size: 12px !important"
          @click="onDel(item)"
        ></i>
      </div>
      <div class="flex pointer align_items_center ml8" @click="onDel({})">
        <i class="iconfont-flb icon-shanchu" style="font-size: 16px !important"></i>
        <div class="fs12 c_1D2330 ml4">{{ $t("receipt.clear") }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed, defineEmits } from "vue";

const props = defineProps({
  filterItemList: {
    type: Array,
    default: () => [],
  },
});
const filterArr: any = computed(() => {
  return props.filterItemList;
});

const emit = defineEmits(["onDel"]);
const onDel = (item) => {
  emit("onDel", item.key);
};
</script>

<style lang="scss" scoped></style>
