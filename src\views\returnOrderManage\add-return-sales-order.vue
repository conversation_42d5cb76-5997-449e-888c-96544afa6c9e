<template>
  <div class="pt16 pb16 pl24 pr24 position_re bgc_EDF0F2">
    <div
      class="heightP100 min-height flex flex_direction_column"
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
    >
      <div>
        <div class="common-crumbs-box bgc_white mb16">
          <span class="hover-span" @click="onClose">
            {{ $t("receipt.returnBilling") }}
          </span>
          <el-icon class="ml8 mr8 c_4F5363">
            <ArrowRight />
          </el-icon>
          <span class="current-span">{{ $t("receipt.addReturnOrder") }}</span>
        </div>
      </div>
      <div class="flex widthP100" style="height: calc(100% - 40px)">
        <div class="flex_grow_1 flex flex_direction_column mr16 position_re">
          <div class="bgc_white mb16 border_radius8 p24">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              :size="formSize"
              label-position="top"
            >
              <el-row :gutter="48">
                <el-col :span="8">
                  <!-- 退货单编码 -->
                  <el-form-item
                    :label="$t('receipt.returnOrderCodeM')"
                    prop="orderNo"
                  >
                    <el-input
                      v-model="form.orderNo"
                      :disabled="form.id"
                      maxlength="50"
                      :placeholder="$t('receipt.pleaseEnter')"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 退货日期 -->
                  <el-form-item
                    :label="$t('receipt.returnDateM')"
                    prop="orderDate"
                  >
                    <el-date-picker
                      v-model="form.orderDate"
                      :placeholder="$t('receipt.pleaseChoose')"
                      style="width: 100%"
                      type="date"
                      value-format="YYYY/MM/DD"
                      format="YYYY/MM/DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 销售单 -->
                  <el-form-item
                    :label="$t('receipt.salesOrderM')"
                    prop="saleOrderNo"
                  >
                    <el-select
                      v-model="form.saleOrderNo"
                      :remote-method="getSaleOrder"
                      class="widthP100"
                      filterable
                      :placeholder="$t('receipt.pleaseChoose')"
                      remote
                      reserve-keyword
                      remote-show-suffix
                      :loading="saleLoading"
                    >
                      <el-option
                        v-for="item in saleOrderList"
                        :key="item.orderNo"
                        :label="item.orderNo"
                        :value="item.orderNo"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 会员 -->
                  <el-form-item
                    :label="$t('receipt.memberM')"
                    prop="memberName"
                  >
                    <el-input
                      v-model="form.memberName"
                      disabled
                      :placeholder="$t('receipt.chooseSalesOrder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 门店 -->
                  <el-form-item
                    :label="$t('receipt.storeM')"
                    prop="storeName.value"
                  >
                    <el-input
                      v-model="form.storeName.value"
                      disabled
                      :placeholder="$t('receipt.chooseSalesOrder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 销售人员 -->
                  <el-form-item
                    :label="$t('receipt.salesmanM')"
                    prop="salespersonName"
                  >
                    <el-input
                      v-model="form.salespersonName"
                      disabled
                      :placeholder="$t('receipt.chooseSalesOrder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 审核人员 -->
                  <el-form-item
                    :label="$t('receipt.reviewerM')"
                    prop="examineName"
                  >
                    <el-input
                      v-model="form.examineName"
                      disabled
                      :placeholder="$t('receipt.chooseSalesOrder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <!-- 备注 -->
                  <el-form-item
                    :label="$t('receipt.remarksM')"
                    prop="orderRemarks"
                  >
                    <el-input
                      v-model="form.orderRemarks"
                      :maxlength="200"
                      :placeholder="$t('receipt.pleaseEnter')"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div
            class="flex flex_direction_column justify_content_between flex1 bgc_white border_radius8 overflow_hidden"
          >
            <!--  商品卡片-->
            <div class="overflow_y_auto flex_grow_1 pt24 pl24 pr24">
              <div
                v-for="(item, index) in contentList"
                class="b_solid_default border_radius8"
                :class="index !== contentList.length ? 'mb16' : 'mb22'"
                :key="item.id"
              >
                <div
                  class="pt12 pb8 pl16 pr16 bb_dashed_default flex align_items_center"
                >
                  <span class="c_1D2330 fs14 fw500 lh24 mr8">
                    <span
                      class="second-tag"
                      v-if="item?.stockType?.value === 'SECOND_HAND'"
                    >
                      {{ $t("receipt.secondHand") }}
                    </span>
                    {{ item?.skuName?.value || "" }}
                  </span>
                  <div class="pt8 pb8 pl16 pr16 fs12">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.codeM") }}
                      <span class="c_4F5363">
                        {{ item.skuCode || "-" }}
                      </span>
                    </span>
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.unitM") }}
                      <span class="c_4F5363">
                        {{ item?.unitName?.value || "-" }}
                      </span>
                    </span>
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.unitPriceM") }}
                      <span class="c_4F5363">
                        {{ formatMoney(item.originalUnitPrice) }}
                      </span>
                    </span>
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span class="c_989CAC lh18" v-if="!item.barcodeObj">
                      {{ $t("receipt.orderQuantityM") }}
                      <span class="c_4F5363">
                        {{ item.quantity || 0 }}
                      </span>
                    </span>
                    <span class="c_989CAC lh18" v-else>
                      {{ $t("receipt.serialCodeM") }}
                      <span class="c_4F5363">
                        {{ item.barcodeObj.barcode || "-" }}
                      </span>
                    </span>
                  </div>
                </div>
                <div class="pb8 pt16 pl16 pr16">
                  <span class="c_4F5363 fs12 lh18">
                    {{ $t("receipt.totalPriceProductM") }}
                    <span class="c_1D2330">
                      {{ formatMoney(item.originalTotalPrice) }}</span
                    >
                  </span>
                </div>
                <div class="pb8 pl16 pr16 fs12">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.exchangeQuantityM") }}
                    <span class="c_1D2330">
                      {{ getString("exchange", item) }}
                    </span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.fullDeductionM") }}
                    <span class="c_1D2330">{{ getString("max", item) }}</span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.rabateM") }}
                    <span class="c_1D2330">
                      {{ getString("discount", item) }}
                    </span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.pointsDeductionM") }}
                    <span class="c_1D2330">
                      {{ getString("point", item) }}
                    </span>
                  </span>
                </div>
                <div class="pb16 pl16 pr16">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.payAmountM") }}
                    <span class="c_1D2330">
                      {{
                        item.modifyTotalPrice !== null
                          ? formatMoney(
                              subtract(
                                Number(item.modifyTotalPrice),
                                Number(getString("point", item, true))
                              )
                            )
                          : formatMoney(
                              subtract(
                                Number(item.discountTotalPrice),
                                Number(getString("point", item, true))
                              )
                            )
                      }}
                    </span>
                  </span>
                </div>
                <div
                  class="pt8 pb8 pl16 pr16 bt_solid_default flex align_items_center"
                >
                  <div
                    class="c_4F5363 lh18 mr16"
                    v-if="item.isBarcode?.value === 'NO'"
                  >
                    <span class="lh18 c_EC2D30">*</span>
                    {{ $t("receipt.returnQuantityM") }}
                    <el-input-number
                      v-model="item.returnQuantity"
                      :max="getReturnMax(item)"
                      :min="1"
                      :precision="0"
                      :step="1"
                      step-strictly
                    />
                  </div>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.returnPointsM") }}
                    <span class="c_1D2330 lh22 fw500">
                      {{ getReturnPoint(item) }}
                    </span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.returnAmountM") }}
                    <span class="c_EC2D30 lh22 fw500">
                      {{ formatMoney(getReturnAmount(item)) }}
                    </span>
                  </span>
                  <Delete
                    @click="onDel(index)"
                    class="c_EC2D30 mr8 pointer"
                    style="width: 16px; height: 16px; margin-left: auto"
                  />
                </div>
              </div>
            </div>
            <!--          操作栏-->
            <div
              class="pt12 pb12 flex justify_content_between bt_solid_default widthP100"
            >
              <div class="pl24 flex align_items_center">
                <span class="c_4F5363 lh22 fs14">
                  {{ $t("receipt.refundAmountM") }}
                  <span class="c_EC2D30 fw500 lh22 fs14">
                    {{ formatMoney(getTotalAmount()) }}
                  </span>
                </span>
                <el-divider class="ml20 mr20" direction="vertical"></el-divider>
                <span class="c_4F5363 lh22 fs14">
                  {{ $t("receipt.returnPointsM") }}
                  <span class="c_1D2330 fw500 lh22 fs14">
                    {{ getTotalPoint() }}
                  </span>
                </span>
              </div>
              <div class="pr24">
                <el-button @click="onClose">
                  {{ $t("receipt.return") }}
                </el-button>
                <el-divider class="ml8 mr8" direction="vertical"></el-divider>
                <el-button :loading="btnLoading" @click="onSave('DRAFT')">
                  {{ $t("receipt.preservation") }}
                </el-button>
                <el-button
                  :loading="btnLoading"
                  type="primary"
                  @click="onSubmit(formRef)"
                >
                  {{ $t("receipt.submit") }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!--        右侧栏目-->
        <div
          v-if="selectSkuList.length"
          class="right-block bgc_white border_radius8 p24 position_re overflow_y_auto"
          v-loading="rightLoading"
          :element-loading-text="$t('receipt.loadingInProgress')"
        >
          <div
            v-for="item in selectSkuList"
            :key="item.id"
            class="b_solid_default border_radius8 mb24"
          >
            <div class="">
              <div class="pt12 pb8 pl16 pr16 flex justify_content_between">
                <span class="c_1D2330 fs14 fw500 lh24 mr8">
                  <span
                    class="second-tag"
                    v-if="item?.stockType?.value === 'SECOND_HAND'"
                  >
                    {{ $t("receipt.secondHand") }}
                  </span>
                  {{ item?.skuName?.value || "-" }}
                </span>
                <span class="c_4F5363 lh18">
                  {{ $t("receipt.unitPriceM") }}
                  <span class="c_1D2330">
                    {{ formatMoney(item.originalUnitPrice) }}
                  </span>
                </span>
              </div>
              <div
                class="pl16 pr16"
                :class="item?.isBarcode?.value === 'YES' ? 'pb4' : 'pb8'"
              >
                <span class="c_4F5363 lh18">
                  {{ $t("receipt.codeM") }}
                  <span class="c_1D2330">
                    {{ item.skuCode || "-" }}
                  </span>
                </span>
                <span
                  class="c_4F5363 lh18 float-right"
                  v-if="item?.isBarcode?.value === 'NO'"
                >
                  x{{
                    Number(item.quantity || 0) -
                    Number(item.orginReturnQuantity || 0)
                  }}
                  <span class="pl4">{{ item?.unitName?.value || "" }}</span>
                </span>
              </div>
              <div
                class="pb8 pl16 pr16"
                v-if="item?.isBarcode?.value === 'YES'"
              >
                <span class="c_4F5363 lh18">
                  {{ $t("receipt.serialCodeM") }}
                  <span class="c_1D2330">
                    {{ item.barcodeObj.barcode || "-" }}
                  </span>
                </span>
              </div>
            </div>
            <el-collapse v-model="item.activeNames">
              <el-collapse-item name="1">
                <template #title>
                  <div
                    class="flex justify_content_between widthP100 heightP100 align_items_center"
                  >
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.payAmountM") }}
                    </span>
                    <span class="c_EC2D30 fs14 fw500">
                      {{
                        item.modifyTotalPrice !== null
                          ? formatMoney(
                              subtract(
                                Number(item.modifyTotalPrice),
                                Number(getString("point", item, true))
                              )
                            )
                          : formatMoney(
                              subtract(
                                Number(item.discountTotalPrice),
                                Number(getString("point", item, true))
                              )
                            )
                      }}
                    </span>
                  </div>
                </template>
                <div class="mt4 pt12 pb4 ml16 mr16 bt_dashed_default">
                  <div class="flex justify_content_between">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.totalPriceProductM") }}
                    </span>
                    <span class="c_1D2330 fw500">
                      {{ formatMoney(item.originalTotalPrice) }}
                    </span>
                  </div>
                </div>
                <div class="pb4 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.exchangeQuantityM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("exchange", item) }}
                    </span>
                  </div>
                </div>
                <div class="pb4 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.fullDeductionM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("max", item) }}
                    </span>
                  </div>
                </div>
                <div class="pb4 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.rabateM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("discount", item) }}
                    </span>
                  </div>
                </div>
                <div class="pb12 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_989CAC lh18">
                      {{ $t("receipt.pointsDeductionM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("point", item) }}
                    </span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div
              class="pt8 pb8 bt_solid_default flex align_items_center justify_content_center pointer"
              @click="choose(item)"
            >
              <el-icon><Plus /></el-icon>
            </div>
          </div>
        </div>
        <div
          v-else
          class="right-block bgc_white border_radius8 p24 position_re flex align_items_center"
          v-loading="rightLoading"
          :element-loading-text="$t('receipt.loadingInProgress')"
        >
          <empty-box
            class="flex1"
            :text="form.saleOrderNo ? '' : $t('receipt.chooseSalesOrderTip')"
          ></empty-box>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  onBeforeMount,
  onUnmounted,
  onMounted,
  reactive,
  ref,
  watch,
} from "vue";
import type { ComponentSize, FormInstance, FormRules } from "element-plus";
import { dayjs, ElMessage } from "element-plus";
import { Delete } from "@element-plus/icons-vue";
import { usePort } from "@/hooks/usePort";
import { ArrowRight } from "@element-plus/icons-vue";
import { getOrderInfo, getOrderInfoDetail } from "@/api/salesOrderManage";
import emptyBox from "@cp/empty/index.vue";
import {
  add,
  divide,
  formatMoney,
  multiply,
  subtract,
  round,
} from "@/util/numberUtil";
import { nextTick } from "vue";
import { addOrderReturn, getOrderReturnDetail } from "@/api/returnOrderManage";
import store from "@/store";
import lang from "@/lang/index";
import router from "@/router";

const i18n = lang.global;
const { generateCode } = usePort();
const props = reactive({
  data: {} as any,
});
const loading = ref(false); //页面loading
const saleOrderList = ref([]); //销售单列表
const saleLoading = ref(false); //销售单列表loading
const rightLoading = ref(false); //右侧loading
const rightContent = ref<any>({}); //右侧内容
const contentList = ref<any>([]); //选中的商品列表
const isInit = ref(false);
const activeNames = ref([]);
const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const formSize = ref<ComponentSize>("default");
const form = reactive<any>({
  id: null,
  orderNo: "", //退货单号
  orderDate: dayjs().format("YYYY-MM-DD"), //开单日期
  saleOrderNo: "",
  examineCode: "", //	审核人编号
  examineId: "", //审核人id
  examineName: "", //审核人名称
  examineRemarks: "", //审核备注
  examineState: "", //审核状态（0待发起审核，1审核中，2审核通过，3审核不通过）,可用值:AUDIT_FAIL,AUDIT_NOW,AUDIT_SUCCESS,DRAFT
  memberCode: "", //会员编号
  memberId: "", //会员id
  memberName: "", //会员名称
  orderRemarks: "", //备注
  returnPrice: 0, //退货总金额
  returnSkuList: [], //退货商品列表
  salespersonCode: "", //销售人编号
  salespersonId: "", //销售人id
  salespersonName: "", //销售人名称
  // stockInNo: "", //入库单号
  storeCode: "", //门店编号
  storeId: "", //门店id
  storeName: { value: "" }, //门店名称
  warehouseId: "", //仓库id
});
const rules = reactive<FormRules>({
  orderNo: [
    { required: true, message: i18n.t("receipt.pleaseEnter"), trigger: "blur" },
  ],
  orderDate: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnter"),
      trigger: "change",
    },
  ],
  saleOrderNo: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnter"),
      trigger: "change",
    },
  ],
});

const selectSkuList = computed(() => {
  // 可选择的商品列表
  const hasChoose = contentList.value.map((item) => item.id);
  const chooseBarcodeList = contentList.value
    .map(
      (item) => item?.barcodeObj?.barcode + item?.barcodeObj?.stockType?.value
    )
    .filter((item) => item);

  const list = [];
  (rightContent.value?.skuList || []).forEach((el) => {
    if (el.isBarcode.value === "YES") {
      // 串码商品处理
      el.barcodeList.forEach((barcodeItem) => {
        list.push({
          ...el,
          barcodeObj: {
            ...barcodeItem,
            skuId: el.skuId,
            id: el.id,
          },
        });
      });
    } else {
      list.push(el);
    }
  });

  return list.filter(
    (item) =>
      item.isGiveaway.value === "NO" &&
      ((!hasChoose.includes(item.id) && item.isBarcode.value === "NO") ||
        (item.isBarcode.value === "YES" &&
          !chooseBarcodeList.includes(
            item?.barcodeObj?.barcode + item?.barcodeObj?.stockType?.value
          ))) &&
      Number(item.orginReturnQuantity || 0) < Number(item.quantity || 0)
  );
});

onBeforeMount(() => {
  //收起左侧菜单
  store.commit("common/SET_COLLAPSE");
});

onMounted(() => {
  let data = {} as any;
  try {
    data = JSON.parse(sessionStorage.getItem("addSaleData"));
  } catch (e) {
    console.log(e, "error");
    data = {};
  }
  props.data = data?.data;
  // 赋值
  activeNames.value = [];
  contentList.value = [];
  rightContent.value = {};
  formRef.value?.resetFields();
  if (props.data.orderNo) {
    loading.value = true;
    getOrderReturnDetail({
      orderNo: props.data.orderNo,
    }).then((res) => {
      if (res.code === 200) {
        loading.value = false;
        isInit.value = true;
        const data = res.data;
        rightContent.value = data;
        form.id = data.id;
        form.orderNo = data.orderNo;
        form.orderDate = data.orderDate;
        form.examineCode = data.examineCode;
        form.examineId = data.examineId;
        form.examineName = data.examineName;
        form.memberCode = data.memberCode;
        form.memberId = data.memberId;
        form.memberName = data.memberName;
        form.salespersonCode = data.salespersonCode;
        form.salespersonId = data.salespersonId;
        form.salespersonName = data.salespersonName;
        form.storeCode = data.storeCode;
        form.storeId = data.storeId;
        form.storeName = data.storeName;
        form.warehouseId = data.warehouseId;
        form.orderRemarks = data.orderRemarks;
        form.saleOrderNo = data.saleOrderNo;
      } else {
        ElMessage.error(res.msg);
        return;
      }
    });
  } else {
    // 获取编码
    form.saleOrderNo = props?.data?.saleOrderNo || "";
    generateCode("openOrderReturn").then((res) => {
      form.orderNo = res;
    });
  }
});

onUnmounted(() => {
  //打开左侧菜单
  store.commit("common/SET_COLLAPSE");
});

watch(
  () => form.saleOrderNo,
  (val) => {
    // 获取销售单详情
    if (val) {
      contentList.value = [];
      nextTick(() => {
        getOrderDetail();
      });
    }
  }
);

/**
 * 获取退货数量最大值
 *
 * @param {any} item
 * @return {number}
 */
const getReturnMax = (item: any) => {
  if (item.returnState?.value !== "NOT_RETURN") {
    return Number(item.quantity || 0) - Number(item.orginReturnQuantity || 0);
  }
  return Number(item.quantity || 0);
};

/**
 * 根据提供的搜索查询检索销售订单列表。
 *
 * @param {string} search - 用于过滤销售订单的搜索查询。
 * @return {void}
 */
const getSaleOrder = (search = "") => {
  saleLoading.value = true;
  getOrderInfo({
    pageNo: 1,
    pageSize: 50,
    searchStr: search,
    examineState: "AUDIT_SUCCESS",
    returnStateList: ["NOT_RETURN", "PART_RETURN"],
  }).then((res) => {
    saleLoading.value = false;
    if (res.code == 200) {
      saleOrderList.value = res.data.records || [];
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 根据销售订单号获取订单详情。
 *
 * @return {void}
 */
const getOrderDetail = () => {
  rightLoading.value = true;
  getOrderInfoDetail({
    orderNo: form.saleOrderNo,
  }).then((res) => {
    rightLoading.value = false;
    if (res.code == 200) {
      const data = res.data;
      data.skuList = (data.skuList || []).map((item) => {
        return { ...item, orginReturnQuantity: item.returnQuantity };
      });

      if (isInit.value) {
        rightContent.value.skuList = data.skuList || [];

        const ids1 = (rightContent.value.returnSkuList || [])
          .filter((el) => el?.isBarcode?.value === "NO")
          .map((el) => el.id);
        const ids2 = {};
        (rightContent.value.returnSkuList || [])
          .filter((el) => el?.isBarcode?.value === "YES")
          .forEach((el) => {
            ids2[el.id] = el?.barcodeList?.map((item) => item.barcode);
          });

        (selectSkuList.value || []).forEach((el) => {
          if (
            ids1.includes(el.id) ||
            ids2[el.id]?.includes(el?.barcodeObj?.barcode)
          ) {
            const origin = (rightContent.value.returnSkuList || []).find(
              (item) => item.id === el.id
            );
            el.orginReturnQuantity = origin.returnQuantity;
            el.returnQuantity = origin.quantity;
            el.quantity = origin.saleQuantity;
            choose(el);
          }
        });
        nextTick(() => {
          isInit.value = false;
        });
      } else {
        rightContent.value = data;
        form.examineCode = data.examineCode;
        form.examineId = data.examineId;
        form.examineName = data.examineName;
        form.memberCode = data.memberCode;
        form.memberId = data.memberId;
        form.memberName = data.memberName;
        form.salespersonCode = data.salespersonCode;
        form.salespersonId = data.salespersonId;
        form.salespersonName = data.salespersonName;
        form.storeCode = data.storeCode;
        form.storeId = data.storeId;
        form.storeName = data.storeName;
        form.warehouseId = data.warehouseId;
        form.salePrice = data.actualPrice;
      }

      // stockInNo: "", //入库单号
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 选中商品
 *
 * @param {any} item - 要添加到内容列表中的项
 * @return {void}
 */
const choose = (item: any) => {
  contentList.value.push({
    ...item,
    orginReturnQuantity: item.orginReturnQuantity || item.returnQuantity,
    returnQuantity: item.orginReturnQuantity
      ? Number(item.quantity) - Number(item.orginReturnQuantity)
      : item.returnQuantity ||
        (item.isBarcode.value === "NO" ? item.quantity : 1),
  });
};

/**
 * 删除选择的商品
 *
 * @param {number} index - 明细索引
 * @return {void}
 */
const onDel = (index: number) => {
  contentList.value.splice(index, 1);
};

/**
 * 根据类型返回优惠信息字符串。
 *
 * @param {string} type - 优惠类型（exchange、max、discount、point）
 * @param {any} product - 商品对象
 * @param {boolean} isNumber - 是否返回数字（用于计算）
 * @return {string} 优惠信息字符串（例如“1”、“10.00”、“8折”等）
 */
const getString = (type: string, product: any, isNumber?: boolean) => {
  // 0: "COUPON_EXCHANGE", //"兑换券"
  // 1: "COUPON_DISCOUNT", //"折扣券"
  // 2: "COUPON_THRESHOLD_DISCOUNT", //"满减券"
  // 3: "REDEEM_POINTS", //"积分抵扣"

  if (type === "exchange") {
    // 兑换数量
    let num = 0;
    (rightContent.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
    });
    return num ? num : "-";
  } else if (type === "max") {
    // 满减
    let num = 0;
    let price = 0;
    (rightContent.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
      if (item.discountType.value === "COUPON_THRESHOLD_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          price = round(
            subtract(
              Number(product.originalTotalPrice),
              Number(product.discountTotalPrice)
            )
          );
        }
      }
    });
    if (price) {
      price = round(price - multiply(num, Number(product.originalUnitPrice)));
    }
    return price ? `${formatMoney(price)}` : "-";
  } else if (type === "discount") {
    // 折扣
    let discountRate = 0;
    (rightContent.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          discountRate = item.discountRate;
        }
      }
    });
    // return discountRate ? `${discountRate}${i18n.t("receipt.fracture")}` : "-";
    return discountRate ? `${discountRate}%` : "-";
  } else if (type === "point") {
    // 积分抵扣钱
    let pointsPrice = 0;
    (rightContent.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "REDEEM_POINTS") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          pointsPrice = item.pointsPrice;
        }
      }
    });
    return pointsPrice || 0;
  } else if (type === "usePoints") {
    //积分
    let usePoints = 0;
    (rightContent.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "REDEEM_POINTS") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          usePoints = item.notReturnPoints;
        }
      }
    });
    return usePoints || 0;
  }
};

/**
 * 计算指定商品的退货积分。
 *
 * @param {Object} item - 商品信息。
 * @returns {number} 计算出的退货积分。
 */
const getReturnPoint = (item: any) => {
  try {
    const point = Number(getString("usePoints", item, true));
    const unitPoint =
      divide(
        point,
        subtract(Number(item.quantity), Number(item.orginReturnQuantity))
      ) || 0;
    return round(multiply(Number(item.returnQuantity || 0), unitPoint));
  } catch (e) {
    console.error(e);
    return 0;
  }
};

/**
 * 计算指定商品的退货金额。
 *
 * @param {Object} item - 商品信息。
 * @returns {number} 计算出的退货金额。
 */
const getReturnAmount = (item: any) => {
  try {
    // 总实付
    const total =
      item.modifyTotalPrice !== null
        ? subtract(
            Number(item.modifyTotalPrice),
            Number(getString("point", item, true))
          )
        : subtract(
            Number(item.discountTotalPrice),
            Number(getString("point", item, true))
          );
    // 单个实付
    const unitTotal = divide(Number(total), Number(item.quantity));
    return multiply(Number(item.returnQuantity || 0), Number(unitTotal));
  } catch (e) {
    console.error(e);
    return 0;
  }
};

/**
 * 计算总退货金额。
 *
 * @return {number} 总退回金额。
 */
const getTotalAmount = () => {
  if (!contentList.value.length) return 0;
  let total = 0;

  // 获取一个根据skuId 合并returnQuantity的数组
  const mergedList = contentList.value.reduce((acc, cur) => {
    const existingItem = acc.find((item) => item.id === cur.id);
    if (existingItem) {
      existingItem.returnQuantity += cur.returnQuantity;
    } else {
      acc.push({ ...cur });
    }
    return acc;
  }, []);

  mergedList.forEach((item: any) => {
    total = add(total, Number(getReturnAmount(item)));
  });
  return total;
};

/**
 * 计算总退回积分
 *
 * @return {number} 总退回积分
 */
const getTotalPoint = () => {
  if (!contentList.value.length) return 0;
  let total = 0;
  contentList.value.forEach((item: any) => {
    total = add(total, Number(getReturnPoint(item)));
  });
  return total;
};

/**
 * 格式化提交参数
 * @returns {Object} - 格式化后的优惠券/积分抵扣列表
 */
const formateParams = (type: string) => {
  const returnSkuList = [];
  const barCodeList = {};
  contentList.value.forEach((item: any) => {
    if (item.isBarcode?.value === "YES") {
      // 串码商品合并
      barCodeList[item.skuCode + item.stockType?.value] = barCodeList[
        item.skuCode + item.stockType?.value
      ]
        ? [
            ...barCodeList[item.skuCode + item.stockType?.value],
            {
              ...item,
            },
          ]
        : [
            {
              ...item,
            },
          ];
    } else {
      returnSkuList.push({
        ...item,
      });
    }
  });
  if (Object.keys(barCodeList).length) {
    for (const key in barCodeList) {
      returnSkuList.push({
        ...barCodeList[key][0],
        barcodeList: barCodeList[key].map((el) => ({
          ...el.barcodeObj,
          orderNo: form.orderNo,
          isGiveaway: el?.isGiveaway?.value,
          delFlag: el?.delFlag?.value,
          returnState: el?.returnState?.value,
          skuId: el.skuId,
        })),
        skuId: barCodeList[key][0].skuId,
        returnQuantity: barCodeList[key].length || 0,
      });
    }
  }
  return {
    ...form,
    examineState: type, //审核状态（0挂起，1审核中，2通过，3不通过）,可用值:AUDIT_FAIL,AUDIT_NOW,AUDIT_SUCCESS,DRAFT
    returnPrice: getTotalAmount(), //退货总金额
    returnSkuList: returnSkuList.map((el) => {
      const newObj = { ...el };
      delete newObj.returnState;
      delete newObj.delFlag;
      return {
        ...newObj,
        orderNo: form.orderNo,
        isBarcode: el.isBarcode?.value,
        isGiveaway: el.isGiveaway?.value,
        quantity: el.returnQuantity, //退货数量
        saleQuantity: el.quantity, //下单数量
        // saleTotalPrice: el.discountTotalPrice, //销售总价
        saleTotalPrice: subtract(
          Number(el.discountTotalPrice),
          Number(getString("point", el, true))
        ),
        saleUnitPrice: round(
          divide(
            subtract(
              Number(el.discountTotalPrice),
              Number(getString("point", el, true))
            ),
            el.quantity
          )
        ), //销售单价
        totalPrice: getReturnAmount(el), //退货总价（销售单优惠后总价）
        unitPrice: divide(getReturnAmount(el), el.returnQuantity), //退货单价（销售单优惠后单价）
        barcodeList: (el.barcodeList || []).map((item) => ({
          ...item,
          stockType: item.stockType?.value,
        })),
        stockType: el.stockType?.value,
        saleOrderSkuTableId: el.id,
      };
    }), //退货商品列表
  };
};

// 提交
const onSubmit = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      onSave("AUDIT_NOW");
    }
  });
};

// 保存
const onSave = (type?: string) => {
  btnLoading.value = true;
  addOrderReturn(formateParams(type))
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        onClose();
      } else {
        ElMessage.error(res.msg);
      }
      btnLoading.value = false;
    })
    .catch((res) => {
      btnLoading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// 关闭
const onClose = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.width620 {
  width: 620px;
}

.second-tag {
  font-size: 10px;
  color: #ff9900;
  padding: 1px 8px;
  border-radius: 4px;
  border: 1px solid #ff9900;
  margin-right: 4px;
  vertical-align: bottom;
}

::v-deep {
  .el-input-group__append {
    padding: 0 6px;
  }

  // 禁用的颜色
  .el-input {
    .el-input__inner {
      -webkit-text-fill-color: currentcolor;
      color: #4f5363;

      &::placeholder {
        color: #989cac !important;
      }
    }
  }
}

.top-block {
  height: 266px;
}

.right-block {
  min-width: 380px;
}

.min-height {
  min-height: 560px;
}

.fixed_bottom {
  position: absolute;
  bottom: 0;
}

::v-deep .el-collapse {
  margin-bottom: 2px;
  border: none;

  .el-collapse-item {
    display: flex;
    flex-direction: column-reverse;
  }

  .el-collapse-item__arrow {
    transform: rotate(90deg);
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(-90deg);
  }

  .el-collapse-item__wrap {
    border: none;

    .el-collapse-item__content {
      padding: 0;
    }
  }

  .el-collapse-item__header {
    height: 30px;
    border-radius: 4px;
    flex-direction: row-reverse;
    justify-content: flex-end;
    border: none;
    padding-left: 8px;
    padding-bottom: 16px;
    width: calc(100% - 16px);

    .el-collapse-item__arrow {
      margin-left: 10px;
    }
  }
}
</style>
