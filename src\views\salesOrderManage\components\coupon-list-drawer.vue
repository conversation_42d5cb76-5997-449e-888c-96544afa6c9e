<!--
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-02 14:49:28
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-05-06 09:58:01
 * @FilePath: \flb-receipt\src\views\salesOrderManage\components\coupon-list-drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    :model-value="show"
    :append-to-body="true"
    :before-close="onClose"
    :title="
      props.type == 'exchange'
        ? $t('receipt.exchangeCoupons')
        : $t('receipt.discount')
    "
    class="title_drawer radius8_drawer"
    modal-class="transparent_modal top_drawer_modal"
    direction="rtl"
    size="460px"
  >
    <div
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
      v-if="couponList && couponList.length"
    >
      <div>
        <el-checkbox-group
          class="flex flex_direction_column"
          :model-value="checkList"
          @change="(val) => onChange(val, true)"
        >
          <div
            v-for="(item, i) in couponList"
            :key="i"
            class="b_solid_default border_radius4 ptb12 plr16 flex mb16 pointer"
            :class="
              !disbaleIds.includes(item.id) && item.status.value === 'WAIT'
                ? ''
                : 'disabled'
            "
            @click="(e) => choose(e, item)"
          >
            <el-checkbox
              v-if="props.type == 'exchange'"
              :disabled="
                !(!disbaleIds.includes(item.id) && item.status.value === 'WAIT')
              "
              :value="item"
              class="block mt4"
              size="small"
            />
            <el-radio-group
              class="align_items_start"
              :model-value="checkList[0] && checkList[0].id"
              @change="
                () => {
                  checkList[0] = item;
                  onChange(checkList);
                }
              "
              v-else
            >
              <el-radio
                :disabled="
                  !(
                    !disbaleIds.includes(item.id) &&
                    item.status.value === 'WAIT'
                  )
                "
                @click="reClick(item)"
                :value="item.id"
              ></el-radio>
            </el-radio-group>
            <div class="flex flex_direction_column ml8">
              <div class="fs12 fw500 c_1D2330 mb4 lh18">
                {{ (item.name && item.name.value) || "" }}
              </div>
              <div class="c_4F5363 lh18 flex align_items_center">
                <div>
                  {{ $t("receipt.numberM") }}{{ item.couponCode || "" }}
                </div>
                <template v-if="props.type == 'coupon'">
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <div v-if="item.couponType.code == 2">
                    {{ $t("receipt.fullDeductionAmountM") }}
                    &nbsp;{{ $t("receipt.full") }}
                    {{ formatMoney(item.minPurchase) }}
                    &nbsp;{{ $t("receipt.substract") }}
                    {{ formatMoney(item.minPurchaseAmount) }}
                  </div>
                  <div v-if="item.couponType.code == 1">
                    {{ $t("receipt.rabateM") }}
                    {{ item.discountAmount }}
                    <!-- {{ $t("receipt.fracture") }} -->
                    %
                  </div>
                </template>
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>
    </div>
    <div
      v-else
      class="flex align_items_center heightP100 justify_content_center"
    >
      <empty-box
        :text="
          props.type == 'exchange'
            ? $t('receipt.noExchangeVoucher')
            : $t('receipt.noCoupon')
        "
      ></empty-box>
    </div>
    <template #footer>
      <div class="t_left">
        <el-button type="primary" @click="onSubmit">
          {{ $t("receipt.determine") }}
        </el-button>
        <el-button @click="onClose">
          {{ $t("receipt.cancel") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { dayjs, ElMessage } from "element-plus";
import { myCoupon } from "@/api/salesOrderManage";
import emptyBox from "@cp/empty/index.vue";
import { nextTick } from "vue";
import { formatMoney } from "@/util/numberUtil";
import lang from "@/lang/index";

const i18n = lang.global;
const emit = defineEmits(["close", "update:show", "confirm"]);
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  memberId: {
    type: [String, Number],
    required: true,
  },
  storeId: {
    type: [String, Number],
    required: true,
  },
  products: {
    // 所有商品
    type: Array,
    default: () => [],
  },
  couponProductIds: {
    // 能使用券的商品ids
    type: Array,
    default: () => [],
  },
  show: {
    type: Boolean,
    required: true,
  },
  type: {
    /* 优惠券类型
     * exchange:兑换券
     * coupon:优惠券
     */
    type: String,
    required: true,
  },
});

const couponList: any = ref([]); //优惠券列表
const checkList: any = ref([]); //选中的优惠券
const lastCheck: any = ref([]); //上次选中的优惠券
const loading = ref(false);
const disbaleIds = ref([]); //禁用的优惠券id
const maxExchangeNum = ref(0); //最多可用兑换券数量
watch(
  [
    () => props.memberId,
    () => props.storeId,
    () => props.show,
    () => props.type,
  ],
  (val) => {
    if (val[0] && val[1] && val[2]) {
      nextTick(() => {
        getMyCoupon();
      });
    }
  },
  { immediate: true }
);

/**
 * @description: 获取我的（会员）优惠券
 * @param {Object} data
 * @param {String} data.memberId 用户id会员
 * @param {String} data.storeId 门店id
 * @returns {Promise} promise对象
 */
const getMyCoupon = () => {
  loading.value = true;
  myCoupon({
    memberId: props.memberId, //用户id会员
    storeId: props.storeId, //门店id
  }).then((res) => {
    loading.value = false;
    couponList.value = [];
    if (res.code == 200) {
      disbaleIds.value = [];
      (res.data || []).forEach((el) => {
        if (
          el.couponType &&
          el.couponType.value == "EXCHANGE" &&
          props.type == "exchange"
        ) {
          // 兑换
          // (el.couponProductVoList || []).forEach((item) => {
          //   if (!item.quantity) {
          //     disbaleIds.value.push(el.id);
          //     return;
          //   }
          // });
          if (dayjs(el.expirationTime).isBefore(dayjs())) {
            disbaleIds.value.push(el.id);
          }

          if (!(el.couponProductVoList || []).length) {
            disbaleIds.value.push(el.id);
          }

          const productIds = props.products.map((item: any) => item.skuId);
          const ids = (el.couponProductVoList || []).map(
            (item) => item.productId
          );
          
          if (ids.length) {
            if (!ids.some((item) => productIds.includes(item))) {
              disbaleIds.value.push(el.id);
            }
          }

          console.log(productIds,'disbaleIds');
          
          couponList.value.push(el);
        } else if (
          el.couponType &&
          (el.couponType.value == "REDUCTION" ||
            el.couponType.value == "DISCOUNT") &&
          props.type != "exchange"
        ) {
          // 满减、折扣
          const ids = (el.couponProductVoList || []).map(
            (item) => item.productId
          );
          if (ids.length) {
            if (!ids.some((item) => props.couponProductIds.includes(item))) {
              disbaleIds.value.push(el.id);
            }
          }
          couponList.value.push(el);

          if (dayjs(el.expirationTime).isBefore(dayjs())) {
            disbaleIds.value.push(el.id);
          }

          if (!(el.couponProductVoList || []).length) {
            disbaleIds.value.push(el.id);
          }
        }
      });
      checkList.value = (couponList.value || []).filter((el) =>
        [...props.data].map((item: any) => item.discountCode).includes(el.id)
      );

      // 计算兑换券最多可用数量
      let maxNum = 0;
      let ids = [];
      (couponList.value || []).forEach((el) => {
        if (el.couponType && el.couponType.value == "EXCHANGE") {
          ids = ids.concat(
            (el.couponProductVoList || []).map((item) => item.productId)
          );
        }
      });
      ids = [...new Set(ids)];
      props.products.forEach((el: any) => {
        console.log(el, "eeeeeeeeeee");

        if (ids.includes(el.skuId)) {
          maxNum += Number(el.quantity || 0);
        }
      });
      maxExchangeNum.value = maxNum;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const onChange = (val, isChange = false) => {
  if (!isChange) {
    checkList.value = val.filter((el) => !lastCheck.value.includes(el.id));
  } else {
    // 兑换券
    if (val.length > maxExchangeNum.value) {
      ElMessage.warning(
        i18n.t("receipt.exchangeTips", {
          count: maxExchangeNum.value,
        })
      );
      checkList.value.map((el) => {
        return el.id;
      });
      return false;
    }
    checkList.value = val;
  }
  lastCheck.value = checkList.value.map((el) => {
    return el.id;
  });
};

const reClick = (item) => {
  if (checkList.value.length && checkList.value[0].id == item.id) {
    checkList.value = [];
  }
};

/**
 * @description: 关闭优惠券drawer
 * @return {void} 无返回值
 */
const onClose = () => {
  emit("update:show", false);
  emit("close");
};

/**
 * @description: 优惠券drawer确定
 * @return {void} 无返回值
 */
const onSubmit = () => {
  const list = (props.data || []).filter((el: any) => {
    if (props.type == "exchange") {
      return el.couponType && el.couponType.value != "EXCHANGE";
    } else {
      return el.couponType && el.couponType.value == "EXCHANGE";
    }
  });

  emit("confirm", [...list, ...checkList.value]);
};

const choose = (e: any, item: any) => {
  e.stopPropagation();
  e.preventDefault();
  if (props.type == "exchange") {
    if (!disbaleIds.value.includes(item.id) && item.status.value === "WAIT") {
      const isHas = checkList.value.some((el: any) => el.id == item.id);
      let list = [...checkList.value];
      if (isHas) {
        list = list.filter((el: any) => el.id != item.id);
      } else {
        list.push(item);
      }

      onChange(list, true);
    }
  } else {
    if (
      !(!disbaleIds.value.includes(item.id) && item.status.value === "WAIT")
    ) {
      {
        return;
      }
    }
    checkList.value[0] = item;
    onChange(checkList.value);
  }
};
</script>

<style lang="scss" scoped>
.disabled {
  * {
    color: #989cac !important;
  }
}

.el-radio {
  top: -6px;
}

.el-checkbox {
  border: none;
  padding: 0;
  height: min-content;
  margin-top: 4px;
}
</style>
