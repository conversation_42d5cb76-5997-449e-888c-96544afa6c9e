<!--
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-02 14:49:28
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-19 16:40:20
 * @FilePath: \flb-receipt\src\views\salesOrderManage\components\audit-dialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="title"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="400px"
  >
    <el-form ref="formRef" :model="form" label-position="top">
      <el-form-item
        prop="text"
        :rules="[
          {
            required: title === $t('receipt.auditReject'),
            message: $t('receipt.pleaseEnter'),
            trigger: 'blur',
          },
        ]"
      >
        <template #label>
          <span class="fs12 c_1D2330 lh18 fw400">{{
            $t("receipt.auditOpinionM")
          }}</span>
        </template>
        <el-input
          v-model="form.text"
          :rows="5"
          maxlength="200"
          :placeholder="$t('receipt.pleaseEnter')"
          show-word-limit
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm" :loading="props.loading">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps, watch } from "vue";

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  show: {
    type: Boolean,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const form = ref({
  text: "",
});
const formRef = ref();

watch(
  () => props.show,
  (val) => {
    if (val) {
      formRef.value.resetFields();
    }
  }
);

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    emit("confirm", form.value);
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped></style>
