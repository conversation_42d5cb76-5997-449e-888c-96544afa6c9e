<!--
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-02 14:49:28
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-17 14:45:47
 * @FilePath: \flb-receipt\src\views\salesOrderManage\components\audit-dialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="$t('receipt.changeSort')"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="300px"
  >
    <el-form ref="formRef" :model="form">
      <el-form-item
        prop="sort"
        :rules="[
          {
            required: true,
            message: $t('receipt.pleaseEnter'),
            trigger: 'blur',
          },
        ]"
      >
        <template #label>
          <span class="fs12 c_1D2330 lh18 fw400">{{
            $t("receipt.insert")
          }}</span>
        </template>
        <el-input
          v-model="form.sort"
          style="width: 148px"
          :placeholder="$t('receipt.pleaseEnter')"
        />
        <span class="fs12 c_1D2330 lh18 fw400 pl8">{{
          $t("receipt.intoRow")
        }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">
        {{ $t("receipt.submit") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { ref, defineEmits, defineProps, watch } from "vue";
import lang from "@/lang/index";
import { nextTick } from "vue";
import { sortProductGroup } from "@/api/naviConfig";

const i18n = lang.global;
const emit = defineEmits(["close", "confirm", "update:show", "refresh"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
const form = ref({
  sort: "",
});
const formRef = ref();

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        form.value.sort = props.data.sort || 1;
      });
    }
  },
  {
    immediate: true,
  }
);

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    sortProductGroup([
      {
        id: props.data.id,
        productGroupId: props.data.productGroupId,
        sort: form.value.sort,
      },
    ]).then((res) => {
      if (res.code === 200) {
        onClose();
        emit("refresh");
        ElMessage.success(i18n.t("receipt.operationSuccess"));
      } else {
        ElMessage.error(res.msg || i18n.t("receipt.networkError"));
      }
    });
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped>
:deep {
  .el-form-item__label {
    align-items: center !important;
    padding-right: 8px !important;
  }
}
</style>
