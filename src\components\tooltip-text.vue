<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-03-25 17:52:29
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-04 11:26:00
 * @FilePath: \flb-receipt\src\components\tooltip-text.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-tooltip
    :content="props.text.toString()"
    :disabled="!isOverFlow"
    effect="dark"
    placement="top"
    popper-class="pop_max_600"
  >
    <div
      @mouseover="onMouseOver()"
      ref="divRef"
      :class="props.textClass"
      style="
        width: min-content;
        white-space: nowrap;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      "
    >
      {{ props.text }}
    </div>
  </el-tooltip>
</template>

<script setup>
import { ref, defineProps } from "vue";
const props = defineProps({
  text: {
    type: [String, Number],
    default: "",
  },
  textClass: {
    type: String,
    default: "",
  },
});

const isOverFlow = ref(false);
const divRef = ref(null);

function onMouseOver() {
  // 内容超出，显示文字提示内容
  const tag = divRef.value;
  if (tag) {
    if (tag.clientWidth < tag.scrollWidth) {
      isOverFlow.value = true;
    } else {
      isOverFlow.value = false;
    }
  }
}
</script>

<style lang="scss" scoped></style>
