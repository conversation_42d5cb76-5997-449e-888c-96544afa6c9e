<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('receipt.addStore')"
    width="908"
    append-to-body
    :close-on-click-modal="false"
    :before-close="onClose"
    class="common-dialog-center common_border_dialog"
  >
    <div class="pt24 pb8 flex justify_content_between">
      <div class="width420 box_sizing_box border_radius4 b_solid_default">
        <!-- 头 -->
        <div
          class="flex justify_content_between align_items_center pt8 pb8 pl16 pr16 bgc_F3F6F8 bb_solid_default"
        >
          <span class="fs14 fw600 lh22 c_1D2330"
            >{{ $t("receipt.storeList") }}（{{ stashList.length }}）</span
          >
          <span
            class="lh18 main-color pointer"
            @click="checkList = stashList"
            >{{ $t("receipt.selectAll") }}</span
          >
        </div>

        <div class="pt16 pl16 pr16">
          <search-box
            :modelValue="searchStr"
            :placeholder="$t('receipt.inputStoreNameSearch')"
            inputWidth="100%"
            @onInput="(value) => (searchStr = value)"
            @onSearch="getList()"
            @onClear="
              searchStr = '';
              getList();
            "
          ></search-box>
        </div>
        <div class="height366 box_sizing_box p16 overflow_y_auto">
          <el-checkbox-group v-model="checkedIds">
            <el-checkbox
              v-for="item in stashList"
              :key="item.id"
              :label="item.name?.value"
              :value="item.id"
              @change="(state) => toggleCheck(item, state)"
            />
          </el-checkbox-group>
        </div>
      </div>

      <div class="width420 box_sizing_box border_radius4 b_solid_default">
        <div
          class="flex justify_content_between align_items_center pt8 pb8 pl16 pr16 bgc_F3F6F8 bb_solid_default"
        >
          <span class="fs14 fw600 lh22 c_1D2330"
            >{{ $t("receipt.selected") }}（{{ checkList.length }}）</span
          >
          <span class="lh18 main-color pointer" @click="checkList = []">{{
            $t("receipt.clear")
          }}</span>
        </div>

        <div class="height406 box_sizing_box p16 overflow_y_auto">
          <div
            class="pt10 pb10 pl16 pr16 flex justify_content_between"
            v-for="item in checkList"
            :key="item.id"
          >
            <span class="lh18 c_1D2330 text_over">{{
              `${item.name?.value}（${item.code}）`
            }}</span>
            <el-icon class="pointer" @click="toggleCheck(item, false)"
              ><CircleClose
            /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onSubmit">{{
        $t("receipt.submit")
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, defineEmits, defineProps, computed } from "vue";
import { ElMessage } from "element-plus";
import searchBox from "@/components/filter/search.vue";
import { deepClone } from "@/util/util";
import lang from "@/lang/index";
import { getNoPowerStoreSelectorApi } from "@/api/salesOrderManage";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  selectedData: {
    type: Array,
    default: () => [],
  },
});

const dialogVisible = ref(false);
const searchStr = ref("");
const stashList = ref([]);
const checkList = ref([]);
const checkedIds = computed(() => {
  const ids = checkList.value.map((el) => el.id);
  return ids;
});

onMounted(() => {
  dialogVisible.value = true;
  checkList.value = deepClone(props.selectedData);

  getList();
});

// 获取数据
const getList = () => {
  const params = {
    search: searchStr.value,
    enabled: "YES",
  };
  getNoPowerStoreSelectorApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        stashList.value = list.map((el) => {
          return {
            id: el.id,
            code: el.code,
            name: el.name,
          };
        });
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 选中
const toggleCheck = (row, state) => {
  if (state) {
    checkList.value.push(row);
  } else {
    checkList.value = checkList.value.filter((el) => el.id !== row.id);
  }
};

// 提交
const onSubmit = () => {
  if (!checkList.value.length) {
    ElMessage.error(i18n.t("receipt.pleaseSelectData"));
    return;
  }
  emit("refresh", checkList.value);
  onClose();
};

// 关闭
const onClose = () => {
  dialogVisible.value = false;
  emit("close");
};
</script>

<style lang="scss" scoped>
.width420 {
  width: 420px;
}
.height366 {
  height: 366px;
}
.height406 {
  height: 406px;
}
.box_sizing_box {
  box-sizing: border-box;
}
::v-deep {
  .el-checkbox {
    border: none;
    height: 38px;
    line-height: 38px;
    margin-right: 0;
    .el-checkbox__label {
      font-size: 12px;
      padding-left: 8px;
    }
  }

  .el-checkbox-group{
    display: flex;
    flex-direction: column;
  }
}
</style>
