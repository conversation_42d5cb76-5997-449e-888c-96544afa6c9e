import request from './axios'
import { baseUrl } from '@/config/env'

interface Params {
  [key: string]: any
}

// 主数据-商品表管理
// 列表(分页)
export const getGoodsPageApi = (data: Params) => request({
  url: baseUrl + '/sys/product/',
  method: 'get',
  params: data
})

// 根据Id获取详情
export const getGoodsInfoApi = (id: number) => request({
  url: baseUrl + '/sys/product/' + id,
  method: 'get'
})



// 商品分组表管理
// 获取分组树
export const getGroupTreeApi = (data: Params) => request({
  url: baseUrl + '/sys/productGroup/tree',
  method: 'get',
  params: data
})



// 商品分组类型
// 列表(分页)
export const getGroupTypePageApi = (data: Params) => request({
  url: baseUrl + '/sys/productGroupType/',
  method: 'get',
  params: data
})