<template>
  <div class="p24 bgc_white position_re">
    <!-- 筛选 -->
    <div class="flex flex_direction_column">
      <div>
        <search-box
          :modelValue="filterObj.searchStr"
          :placeholder="$t('receipt.searchPlaceholder')"
          @onClear="
            filterObj.searchStr = '';
            onFilter();
          "
          @onInput="(value) => (filterObj.searchStr = value)"
          @onSearch="onFilter"
        ></search-box>

        <div class="flex justify_content_between mt8">
          <div class="flex flex_wrap" style="gap: 8px">
            <!-- 单据状态 -->
            <el-select
              v-model="filterObj.examineState"
              class="width168"
              :placeholder="$t('receipt.orderStatus')"
              size="default"
              @change="filterChange('examineState', $t('receipt.orderStatus'))"
            >
              <el-option
                v-for="item in examineStateOpts.filter((el) => el.id !== 0)"
                :key="item.id"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
            <!-- 开单日期 -->
            <el-date-picker
              v-model="filterObj.orderDate"
              :placeholder="$t('receipt.orderDate')"
              size="default"
              style="width: 168px"
              type="date"
              value-format="YYYY-MM-DD"
              @change="filterChange('orderDate', $t('receipt.orderDate'))"
            />
            <!-- 门店 -->
            <el-select
              v-model="filterObj.storeCode"
              class="width168"
              filterable
              :placeholder="$t('receipt.store')"
              size="default"
              @change="filterChange('storeCode', $t('receipt.store'))"
            >
              <el-option
                v-for="item in storeList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
            <!-- <div class="radio-box">
              <div class="mr12 fs12 c_4F5363 lh_c">是否价格修订</div>
              <el-radio-group v-model="filterObj.isModify" @change="getList">
                <el-radio value="">全部</el-radio>
                <el-radio value="YES">是</el-radio>
                <el-radio value="NO">否</el-radio>
              </el-radio-group>
            </div> -->
          </div>
          <div class="flex_shrink0">
            <!-- <el-button @click="operate('priceBox')">
              <i class="iconfont-flb icon-caogao mr4"></i>
              {{ $t("receipt.priceChange") }}&nbsp;&nbsp; ({{
                allNum["price"] || 0
              }})
            </el-button> -->
            <el-button @click="operate('draftBox')">
              <i class="iconfont-flb icon-caogao mr4"></i>
              {{ $t("receipt.draft") }}&nbsp;&nbsp;({{ allNum[0] || 0 }})
            </el-button>
            <el-button @click="onExport">
              <i class="iconfont-flb icon-daochu-shangchuan mr2"></i>
              {{ $t("receipt.export") }}
            </el-button>
            <el-button
              type="primary"
              @click="operate('addSalesOrderPage', { data: {} })"
            >
              <i class="iconfont-flb icon-add mr2"></i>
              {{ $t("receipt.addSalesOrder") }}
            </el-button>
          </div>
        </div>
        <filter-item
          :filterItemList="filterList"
          class="mt8"
          @onDel="delFilterItem"
        ></filter-item>
      </div>
    </div>

    <!-- 表格 -->
    <div class="mt16" ref="tableRef">
      <el-table
        v-loading="loading"
        :element-loading-text="$t('receipt.loadingInProgress')"
        :data="tableData"
        :height="tableHeight"
        border
        class="custom_radius_table"
        size="small"
        style="width: 100%"
      >
        <el-table-column
          :label="$t('receipt.orderNo')"
          min-width="200"
          prop="orderNo"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="fw600 pointer main-color"
              @click="
                operate('detailsDrawer', {
                  data: scope.row,
                })
              "
            >
              {{ scope.row.orderNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.orderDate')"
          min-width="128"
          prop="orderDate"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          :label="$t('receipt.member')"
          min-width="128"
          prop="memberName"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.memberName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.store')"
          min-width="200"
          prop="storeName"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.storeName?.value }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.beforeTotalDiscountPrice')"
          min-width="128"
          prop="originalPrice"
          align="right"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 c_1D2330">
              {{ formatMoney(scope.row.originalPrice) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.afterTotalDiscountPrice')"
          min-width="128"
          prop="actualPrice"
          align="right"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 c_1D2330">
              {{
                scope.row.modifyActualPrice == null
                  ? formatMoney(scope.row.actualPrice)
                  : formatMoney(scope.row.modifyActualPrice)
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.salePerson')"
          min-width="128"
          prop="salespersonName"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('receipt.reviewer')"
          min-width="128"
          prop="examineName"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          :label="$t('receipt.isChangePrice')"
          min-width="188"
          prop="modifyActualPrice"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{
              scope.row.modifyActualPrice !== null
                ? $t("receipt.yes")
                : $t("receipt.no")
            }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.remark')"
          min-width="188"
          prop="orderRemarks"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('receipt.status')"
          min-width="95"
          prop="examineState"
          fixed="right"
        >
          <template #default="scope">
            <span
              :class="judgeStatusColorOpts[scope.row.examineState.code] || ''"
              class="common-status-box"
            >
              {{
                filterNameById(scope.row.examineState.code, examineStateOpts)
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="164"
          class-name="operate_column"
        >
          <template #default="scope">
            <!-- 审核中 -->
            <template v-if="scope.row.examineState.code === 1">
              <template v-if="checkAuth(scope.row)">
                <el-button
                  link
                  type="primary"
                  @click="
                    operate('auditDialog', {
                      data: scope.row,
                      title: $t('receipt.auditPass'),
                    })
                  "
                >
                  {{ $t("receipt.pass") }}
                </el-button>
                <el-divider class="ml8 mr8" direction="vertical"></el-divider>
                <el-button
                  link
                  type="danger"
                  @click="
                    operate('auditDialog', {
                      data: scope.row,
                      title: $t('receipt.auditReject'),
                    })
                  "
                >
                  {{ $t("receipt.reject") }}
                </el-button>
                <el-divider class="ml8 mr8" direction="vertical"></el-divider>
              </template>
              <el-button
                link
                type="danger"
                @click="
                  operate('returnDialog', {
                    data: scope.row,
                    title: $t('receipt.recallPrompt'),
                    content: $t('receipt.recallPromptContent'),
                  })
                "
              >
                {{ $t("receipt.recall") }}
              </el-button>
            </template>
            <!-- 已通过 -->
            <template v-else-if="scope.row.examineState.code === 2">
              <el-button
                v-if="scope.row.returnState.value !== 'ALL_RETURN'"
                link
                type="primary"
                @click="
                  operate('addReturnSalesOrder', {
                    data: { saleOrderNo: scope.row.orderNo },
                  })
                "
              >
                {{ $t("receipt.returnGoods") }}
              </el-button>
              <!-- <el-divider
                class="ml8 mr8"
                direction="vertical"
                v-if="
                  scope.row.returnState.value === 'NOT_RETURN' &&
                  checkPriceChange(scope.row)
                "
              ></el-divider> -->
              <span v-if="scope.row.returnState.value === 'ALL_RETURN'">-</span>
              <!-- <el-button
                v-if="
                  scope.row.returnState.value === 'NOT_RETURN' &&
                  checkPriceChange(scope.row)
                "
                link
                type="primary"
                @click="
                  operate('priceSalesOrderPage', {
                    data: scope.row,
                  })
                "
              >
                {{ $t("receipt.priceRevision") }}
              </el-button> -->
            </template>
            <!-- 已拒绝 -->
            <template v-else-if="scope.row.examineState.code === 3">
              <el-button
                link
                type="primary"
                @click="
                  operate('addSalesOrderPage', {
                    data: scope.row,
                  })
                "
              >
                {{ $t("receipt.resubmit") }}
              </el-button>
              <el-divider class="ml8 mr8" direction="vertical"></el-divider>
              <el-button
                link
                type="danger"
                @click="
                  operate('delDialog', {
                    data: scope.row,
                    title: $t('receipt.deletePrompt'),
                    content: $t('receipt.deletePromptContent'),
                  })
                "
              >
                {{ $t("receipt.delete") }}
              </el-button>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <pagination
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>

    <!-- 撤回提示 -->
    <stop-dialog
      v-if="
        operateDialog.type === 'delDialog' ||
        operateDialog.type === 'returnDialog'
      "
      :title="operateDialog.title"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
      :loading="confirmLoading"
    ></stop-dialog>

    <!-- 审核 -->
    <audit-dialog
      v-if="operateDialog.type === 'auditDialog'"
      :title="operateDialog.title"
      v-model:show="operateDialog.show"
      @confirm="onAudit"
      :loading="confirmLoading"
    ></audit-dialog>

    <!-- 销售单详情 -->
    <details-drawer
      v-if="operateDialog.type === 'detailsDrawer'"
      :data="operateDialog.data"
      v-model:show="operateDialog.show"
      @refresh="getList"
    ></details-drawer>
  </div>
</template>

<script lang="ts" setup>
import { onActivated, onMounted, onUnmounted, reactive, ref } from "vue";
import searchBox from "@/components/filter/search.vue";
import { dayjs, ElMessage } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import {
  filterNameById,
  splicingUrl,
  filterByKeyValue,
} from "@/hooks/publicMethod";
import pagination from "@/components/pagination/index.vue";
import {
  downloadExcel,
  getFormatCurDate,
  getObjType,
  tableFormat,
  isEmpty,
} from "@/util/util";
import { formatMoney } from "@/util/numberUtil";
import filterItem from "@/components/filter/filterItem.vue";
import stopDialog from "@/components/dialog/delete.vue";
import { baseUrl } from "@/config/env";
import auditDialog from "@views/salesOrderManage/components/audit-dialog.vue";
import { judgeStatusColorOpts, examineStateOpts } from "./ts/enum";
import DetailsDrawer from "@views/salesOrderManage/components/details-drawer.vue";
import {
  getExamineFail,
  getExamineSuccess,
  getOrderInfo,
  getAllStateNum,
  getExamineWithdraw,
  deleteOrder,
  getStoreSelectorApi,
  exportSaleOrder,
} from "@/api/salesOrderManage";
import lang from "@/lang/index";
import { debounce } from "lodash";
import router from "@/router";
import store from "@/store";

const i18n = lang.global;
const tableRef = ref(null);
const filterObj = reactive({
  searchStr: "",
  orderDate: "",
  examineState: "",
  storeCode: "",
  isModify: "",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const filterList = ref([]);
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});
const allNum = ref({});
const confirmLoading = ref(false);
const storeList = ref([]);

onActivated(() => {
  getList();
});

onMounted(() => {
  window.addEventListener("resize", debounce(getTableHeight, 0));
  getTableHeight();
  getList();
  getStore();
});

onUnmounted(() => {
  window.removeEventListener("resize", debounce(getTableHeight, 0));
});

const getTableHeight = () => {
  tableHeight.value =
    window.innerHeight - 168 - (filterList.value.length ? 28 : 0) - 58;
};

// 获取门店
const getStore = () => {
  getStoreSelectorApi({}).then((res: any) => {
    if (res.code !== 200) return;
    let data = res.data ? res.data : [];
    data = data.map((ele: any) => {
      return {
        ...ele,
        nameValue: ele.name,
        name: ele.name.value,
      };
    });
    storeList.value = data;
  });
};

const getAllNum = () => {
  getAllStateNum({}).then((res) => {
    // 审核状态（0挂起，1审核中，2通过，3不通过）
    (res.data || []).forEach((item) => {
      allNum.value[item.examineState] = item.num;
    });
  });
};

// 获取状态总数
// const getTotal = () => {
//   const params = Object.assign(
//     {
//       examineState: "AUDIT_SUCCESS",
//       isModify: "YES",
//     },
//     pageObj,
//     filterObj
//   );
//   getOrderInfo(params)
//     .then((res) => {
//       if (res.code == 200) {
//         allNum.value["price"] = res.data.total;
//       }
//       getAllNum();
//     })
//     .catch((res) => {
//       ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
//     });
// };

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, filterObj) as any;
  if (!params.examineState) {
    params.examineStateList = ["AUDIT_NOW", "AUDIT_SUCCESS", "AUDIT_FAIL"];
  }
  getOrderInfo(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        tableData.value = list;
        pageObj.total = Number(res.data.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
  getAllNum();
  // getTotal();
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

// 筛选
const onFilter = () => {
  getTableHeight();
  pageObj.pageNo = 1;
  getList();
};

// 筛选项变化
const filterChange = (key, label) => {
  const item = { key: key, value: "" };
  if (key === "storeCode") {
    item.value = `${label}：${filterByKeyValue({
      labelKey: "name",
      valueKey: "code",
      value: filterObj[key],
      option: storeList.value,
    })}`;
  } else if (key === "examineState") {
    item.value = `${label}：${filterByKeyValue({
      labelKey: "name",
      valueKey: "key",
      option: examineStateOpts,
      value: filterObj[key],
    })}`;
  } else if (key === "orderDate") {
    item.value = `${label}：${dayjs(filterObj.orderDate).format("YYYY/MM/DD")}`;
  }
  const index = filterList.value.findIndex((el) => el.key === key);
  if (index == -1) {
    filterList.value.push(item);
  } else {
    // 删除
    const valType = getObjType(filterObj[key]);
    if (
      (valType != "array" &&
        (filterObj[key] === null ||
          filterObj[key] === undefined ||
          filterObj[key] === "")) ||
      (valType === "array" && !filterObj[key].length)
    ) {
      filterList.value.splice(index, 1);
    } else {
      filterList.value.splice(index, 1, item);
    }
  }
  onFilter();
};

// 删除筛选项
const delFilterItem = (key) => {
  if (key) {
    const valType = getObjType(filterObj[key]);
    if (valType === "array") {
      filterObj[key] = [];
    } else {
      filterObj[key] = "";
    }
    filterList.value = filterList.value.filter((e) => e.key !== key);
  } else {
    for (let i = 0; i < filterList.value.length; i++) {
      const item = filterList.value[i];
      const valType = getObjType(filterObj[item.key]);
      if (valType === "array") {
        filterObj[item.key] = [];
      } else {
        filterObj[item.key] = "";
      }
    }
    filterList.value = [];
  }

  onFilter();
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "returnDialog") {
    confirmLoading.value = true;
    getExamineWithdraw([data.id])
      .then((res) => {
        confirmLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        confirmLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    confirmLoading.value = true;
    deleteOrder({ id: data.id })
      .then((res) => {
        confirmLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        confirmLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

/**
 * @description: 审核
 * @param {Object} form - 表单数据
 * @param {String} form.text - 审核备注
 * @return {void} 无返回值
 */
const onAudit = (form) => {
  const data: any = operateDialog.data;
  if (operateDialog.title === i18n.t("receipt.auditPass")) {
    confirmLoading.value = true;
    getExamineSuccess({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        confirmLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        confirmLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    confirmLoading.value = true;
    getExamineFail({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        confirmLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        confirmLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

const operate = (type, val: any = {}) => {
  switch (type) {
    case "addSalesOrderPage":
    case "priceSalesOrderPage":
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          type: type === "addSalesOrderPage" ? "add" : "price",
          data: val.data as any,
        })
      );
      router.push({
        path: "/salesOrderManage/add",
      });
      break;
    case "draftBox":
      router.push({
        path: "/salesOrderManage/draftBox",
      });
      break;
    case "priceBox":
      router.push({
        path: "/salesOrderManage/priceBox",
      });
      break;
    case "addReturnSalesOrder":
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          data: val.data as any,
        })
      );
      router.push({
        path: "/returnOrderManage/add",
      });
      break;
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = val.data || {};
      operateDialog.show = true;
      break;
  }
};

// 导出
const onExport = () => {
  exportSaleOrder(
    Object.assign({}, pageObj, {
      ...filterObj,
    })
  ).then((res: any) => {
    downloadExcel(
      res,
      `${i18n.t("receipt.salesOrder")}-${getFormatCurDate()}` + ".xlsx"
    );
  });
};

// 检验审核权限
const checkAuth = (row: any) => {
  const personInfo = store.getters.personInfo;
  return personInfo.personId === row.examineId;
};

// 判断是否可以价格修订（ 使用优惠的不可以）
const checkPriceChange = (item: any) => {
  return !(
    Number(item.actualPrice) !== Number(item.originalPrice) &&
    (item.skuList || []).some((el: any) => el.isGiveaway?.value === "YES")
  );
};
</script>

<style lang="scss" scoped>
.radio-box {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #dfe2e7;
  padding: 4px 8px;

  .el-radio {
    height: min-content;
    border: none;
    padding: 0 8px;
    margin-right: 0;
  }
}
</style>
