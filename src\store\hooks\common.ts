import { useStore } from "vuex"
import { computed } from 'vue'

interface Res {
  [keys: string]: any
}
const commonFn = (mapper: any, mapFn: any) => {
  const store = useStore()
  const storeStateObj = mapFn(mapper)
  const res: Res = {}
  Object.keys(storeStateObj).forEach(item => {
    const fn = storeStateObj[item].bind({ $store: store })
    res[item] = computed(fn)
  })
  return res
}

export default commonFn