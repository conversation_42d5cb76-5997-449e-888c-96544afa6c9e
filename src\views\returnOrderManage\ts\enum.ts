import lang from "@/lang/index";

const i18n = lang.global;
// 入库单据审核状态,可用值:AUDIT,DRAFT,FINISH,REJECT
const statusOpts = [
  { id: "DRAFT", name: i18n.t("receipt.draft") },
  { id: "AUDIT", name: i18n.t("receipt.waitAudit") },
  { id: "REJECT", name: i18n.t("receipt.reject") },
  { id: "FINISH", name: i18n.t("receipt.finish") },
];

// 入库类型,可用值:ALLOCATE_IN,CUSTOMIZE_IN,PURCHASE_IN,RETURN_IN,SURPLUS_IN
const typeOpts = [
  { id: "PURCHASE_IN", name: i18n.t("receipt.purchaseIn") },
  { id: "RETURN_IN", name: i18n.t("receipt.returnIn") },
  { id: "SURPLUS_IN", name: i18n.t("receipt.surplusIn") },
  { id: "ALLOCATE_IN", name: i18n.t("receipt.allocateIn") },
  { id: "CUSTOMIZE_IN", name: i18n.t("receipt.customize") },
];

export { statusOpts, typeOpts };
