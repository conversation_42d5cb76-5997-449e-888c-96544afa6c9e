import request from "./axios";
import { baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 收银日报表
export const getCashierDayily = (data: Params) =>
  request({
    url: baseUrl + "/order/cashierForms/day",
    method: "post",
    data: data,
  });

// 收银日报表-sku详情
export const getCashierDayilySku = (data: Params) =>
  request({
    url: baseUrl + "/order/cashierForms/sku",
    method: "post",
    data: data,
  });
