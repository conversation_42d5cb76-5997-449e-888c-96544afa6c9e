<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-24 16:37:45
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-03-25 17:01:36
 * @FilePath: \flb-receipt\src\views\bomConfig\operate.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    :model-value="props.show"
    direction="rtl"
    :append-to-body="true"
    :before-close="onClose"
    size="100%"
    class="no_shadow_drawer left_menu_drawer_modal top_drawer_modal"
    :title="form.id ? $t('receipt.editAccount') : $t('receipt.addAccount')"
  >
    <div class="position_re heightP100">
      <el-form
        class="heightP100 min-height flex flex_direction_column"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
      >
        <div class="flex">
          <div
            class="heightP100 min-height flex flex_direction_column"
            v-loading="loading"
            :element-loading-text="$t('receipt.loadingInProgress')"
          >
            <div class="common-crumbs-box bgc_F3F6F8 mb24 border_radius4">
              <span class="disable-span hover-span" @click="onClose">
                {{ $t("receipt.bomConfig") }}
              </span>
              <el-icon class="ml8 mr8 c_4F5363">
                <ArrowRight />
              </el-icon>
              <span class="current-span">
                {{ $t("receipt.addBom") }}
              </span>
            </div>
          </div>
        </div>
        <div class="common_title_with_flag mb20">
          {{ $t("receipt.basicInfo") }}
        </div>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 配方名称 -->
            <el-form-item :label="$t('receipt.bomNameM')" prop="orderNo">
              <el-input
                v-model="form.orderNo"
                :disabled="form.id"
                maxlength="50"
                :placeholder="$t('receipt.pleaseEnter')"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 退货单编码 -->
            <el-form-item :label="$t('receipt.commodityM')" prop="orderNo">
              <el-select
                v-model="form.saleOrderNo"
                :remote-method="getGoodData"
                class="widthP100"
                filterable
                :placeholder="$t('receipt.pleaseChoose')"
                remote
                reserve-keyword
                remote-show-suffix
                :loading="goodData.loading"
              >
                <el-option
                  v-for="item in goodData.list"
                  :key="item.orderNo"
                  :label="item.orderNo"
                  :value="item.orderNo"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="common_title_with_flag mt48 mb20">
          {{ $t("receipt.material") }}
        </div>
        <div class="mb16">
          <el-button icon="Plus" @click="operate('addProductDialog')">
            {{ $t("receipt.addProduct") }}
          </el-button>
        </div>
        <!--添加商品列表-->
        <div ref="tableRef" class="flex1">
          <el-table
            class="custom_radius_table"
            border
            :data="productList"
            size="small"
            style="width: 100%"
            :height="tableHeight"
          >
            <!-- 商品 -->
            <el-table-column
              :label="$t('receipt.commodity')"
              min-width="582"
              prop="skuName"
            >
              <template #header>
                {{ $t("receipt.commodity") }}
                <span class="c_EC2D30">*</span>
              </template>
              <template #default="scope">
                <div class="fw500 c_1D2330">12312321</div>
              </template>
            </el-table-column>
            <!-- 消耗数量 -->
            <el-table-column
              :label="$t('receipt.consumeQty')"
              min-width="502"
              prop="skuName"
            >
              <template #header>
                {{ $t("receipt.consumeQty") }}
                <span class="c_EC2D30">*</span>
              </template>
              <template #default="scope">
                <el-input
                  v-model="scope.row.quantity"
                  type="number"
                  :placeholder="$t('receipt.pleaseEnter')"
                  :formatter="
                    (val) =>
                      numberFormat(val, 0, Number(scope.row.stockQuantity))
                  "
                />
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column
              min-width="55"
              fixed="right"
              :label="$t('receipt.operation')"
              prop="operate"
              class-name="operate_column"
            >
              <template #default="scope">
                <el-button link type="danger" @click="del(scope.$index)">
                  {{ $t("receipt.delete") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="height: 80px; flex-shrink: 0"></div>
        <div class="flex justify_content_end footer">
          <el-button @click="onClose">{{ $t("receipt.return") }}</el-button>
          <el-button
            :loading="btnLoading"
            type="primary"
            @click="onSubmit(formRef)"
          >
            {{ $t("receipt.submit") }}
          </el-button>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script setup>
import {
  ref,
  defineEmits,
  defineProps,
  onMounted,
  reactive,
  onUnmounted,
  nextTick,
} from "vue";
import { debounce } from "lodash";
import store from "@/store";
import { tableFormat, numberFormat } from "@/util/util";

const emit = defineEmits(["close", "refresh", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const btnLoading = ref(false);
const formRef = ref();
const tableHeight = ref(0);
const tableRef = ref(null);
const loading = ref(true);
const form = reactive({});
const rules = reactive({});
const goodData = reactive({
  // 商品数据
  loading: false,
  list: [],
});
const productList = ref([{}]);

onMounted(() => {
  loading.value = false;
  let data = {};
  try {
    data = JSON.parse(sessionStorage.getItem("addSaleData"));
  } catch (e) {
    console.log(e, "error");
    data = {};
  }
  formRef.value?.resetFields();
  // if (props.data.id) {
  //   getDetail();
  // } else {
  //   generateCode("openOrder").then((res) => {
  //     form.value.orderNo = res;
  //   });
  // }
  getTableHeight();
  window.addEventListener("resize", debounce(getTableHeight, 0));
});

onUnmounted(() => {
  window.removeEventListener("resize", debounce(getTableHeight, 0));
});

const getGoodData = (search = "") => {
  console.log("11111111111");
};

// 关闭
const onClose = () => {
  emit("update:show", false);
};

const getTableHeight = () => {
  if (tableRef.value) {
    const wrapperEl = tableRef.value; // 获取封装层容器元素
    const wrapperHeight = wrapperEl.parentElement.clientHeight; // 获取封装层的高度
    // 遍历封装层的子元素，累加非 tableRef 元素的高度
    let occupiedHeight = 0;
    Array.from(wrapperEl.parentElement.children).forEach((child) => {
      if (child !== wrapperEl) {
        // 排除 tableRef 元素本身
        occupiedHeight += child.getBoundingClientRect().height;
      }
    });
    // 计算表格的可用高度
    tableHeight.value = wrapperHeight - occupiedHeight - 32;
  }
};
</script>

<style lang="scss" scoped>
:deep .el-form-item__label {
  color: #1d2330;
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-top: 1px solid #dfe2e7;
  box-shadow: 0px -2px 4px 0px rgba(27, 30, 35, 0.06);
  padding: 14px 16px;
}
</style>
