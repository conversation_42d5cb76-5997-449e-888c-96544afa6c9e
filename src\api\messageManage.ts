import request from './axios'
import { baseUrl } from '@/config/env'

///消息管理列表
export const getMessageManageList = (data) => request({
    url: baseUrl + '/message/messageConfig/',
    method: 'get',
    params: data
})

// 删除
export const deleteMessageApi = (id) => request({
    url: baseUrl + '/message/messageConfig/' + id,
    method: 'delete'
})


// 根据Id获取详情
export const getMessageManageDetailsApi = (id) => request({
    url: baseUrl + '/message/messageConfig/' + id,
    method: 'get'
})

// 新增
export const addMsgConfigApi = (data) => request({
    url: baseUrl + '/message/messageConfig/',
    method: 'post',
    data: data
})

// 修改
export const editMsgConfigApi = (data) => request({
    url: baseUrl + '/message/messageConfig/',
    method: 'put',
    data: data
})