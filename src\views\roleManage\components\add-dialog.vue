<template>
  <el-dialog
    v-model="dialogVisible"
    :title="form.id ? $t('receipt.editRole') : $t('receipt.addRole')"
    width="448"
    append-to-body
    :close-on-click-modal="false"
    :before-close="onClose"
    class="common-dialog-center common_border_dialog"
  >
    <div class="pt24 pb4">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :size="formSize"
        label-position="top"
      >
        <el-form-item :label="$t('receipt.roleName')" prop="title">
          <el-input
            v-model="form.title"
            :placeholder="$t('receipt.pleaseEnter')"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item :label="$t('receipt.roleDescription')" prop="descp">
          <el-input
            v-model="form.descp"
            :rows="4"
            type="textarea"
            :placeholder="$t('receipt.pleaseEnter')"
            maxlength="200"
            show-word-limit
            class="widthP100"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onSubmit(formRef)">{{
        $t("receipt.submit")
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, defineEmits, defineProps, reactive } from "vue";
import type { ComponentSize, FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { deepClone } from "@/util/util";
import { saveRoleApi } from "@/api/roleManage";
import store from "@/store";
import lang from "@/lang/index";
import website from "@/config/website";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  operateRow: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const dialogVisible = ref(false);
const formSize = ref<ComponentSize>("default");
const formRef = ref<FormInstance>();
const form = ref({
  title: "",
  descp: "",
  buId: "",
  role: "",
  moduleId: website.moduleId,
  id: 0,
});
const rules = reactive<FormRules>({
  title: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnter"),
      trigger: "blur",
    },
  ],
  descp: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnter"),
      trigger: "blur",
    },
  ],
});

onMounted(() => {
  dialogVisible.value = true;
  if (props.operateRow.id) {
    form.value = deepClone(props.operateRow);
  } else {
    form.value.buId = store.getters.userInfo.buId;
  }
});

// 校验
const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      save();
    }
  });
};
// 保存
const save = () => {
  saveRoleApi(form.value)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        emit("refresh");
        onClose();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

const onClose = () => {
  dialogVisible.value = false;
  emit("close");
};
</script>

<style lang="scss" scoped></style>
