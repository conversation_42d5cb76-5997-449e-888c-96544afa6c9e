<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="title"
    append-to-body
    class="common-dialog-center common_border_top_dialog add-product-dialog"
    width="978px"
  >
    <div
      v-loading="loading"
      class="bgc_white overflow_hidden flex1"
      :element-loading-text="$t('receipt.loadingInProgress')"
    >
      <div class="flex overflow_hidden heightP100">
        <div class="flex1 flex flex_direction_column overflow_hidden">
          <search-box
            style="width: 100%"
            :modelValue="searchStr"
            :placeholder="$t('receipt.pleaseInputProdcut')"
            @onClear="searchStr = ''"
            @onInput="(value) => (searchStr = value)"
            @onSearch="onFilter"
          ></search-box>

          <div
            class="b_solid_default border_radius4 flex overflow_hidden flex1 mt16"
          >
            <div class="left_box bgc_FAFBFC br_solid_default flex_shrink0">
              <div class="p16 flex flex_direction_column heightP100">
                <div class="pb12 mb12 bb_dashed_default flex_shrink0">
                  <span class="c_1D2330 fw600 lh18">
                    {{ $t("receipt.categoryList") }}
                  </span>
                </div>
                <!-- <el-select
                  v-model="treeSearchkey"
                  class="widthP100 mb12 flex_shrink0"
                  clearable
                  :placeholder="$t('receipt.pleaseChoose')"
                  size="default"
                  @change="getProductGroupTree"
                >
                  <el-option
                    v-for="item in searchkeyList"
                    :key="item.id"
                    :label="item.name?.value"
                    :value="item.id"
                  />
                </el-select> -->
                <div class="overflow_y_auto flex1">
                  <el-tree
                    ref="groupTreeRef"
                    :data="productGroupTreeData"
                    :default-expand-all="true"
                    :expand-on-click-node="false"
                    :props="{
                      label: 'name',
                      children: 'children',
                    }"
                    node-key="productGroupId"
                    style="background: transparent"
                    @node-click="handleNodeClick"
                  >
                    <template #default="{ data }">
                      <span class="text_over">{{ data.name.value }}</span>
                    </template>
                  </el-tree>
                </div>
              </div>
            </div>
            <div
              class="right_box p16 flex1 flex flex_direction_column overflow_hidden"
            >
              <p class="c_1D2330 fw600 lh18">
                <tooptip-text
                  :text="selectTree?.name?.value || $t('receipt.productList')"
                ></tooptip-text>
              </p>
              <!-- 表格 -->
              <div class="mt16 heightP100">
                <el-table
                  v-loading="tableLoading"
                  ref="tableRef"
                  :data="tableData"
                  :height="407"
                  :tree-props="{ children: 'children' }"
                  border
                  class="custom_radius4_table heightP100"
                  row-key="id"
                  current-row-key="id"
                  size="small"
                  style="width: 100%"
                  @select="selectedChange"
                  @select-all="selectedChange"
                >
                  <el-table-column type="selection" width="60" />
                  <el-table-column
                    :label="$t('receipt.productName')"
                    min-width="120"
                    prop="productName"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <span>{{ scope.row.productName?.value }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('receipt.productCode')"
                    min-width="120"
                    prop="productCode"
                    show-overflow-tooltip
                  />
                  <template #empty>
                    <empty-box></empty-box>
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <div
          class="flex flex_direction_column selected-box ml24"
          style="height: 520px"
        >
          <div
            class="flex align_items_center pt8 pb8 pl16 pr16 bgc_F3F6F8"
            style="border-bottom: 1px solid #dfe2e7"
          >
            <div class="flex1 text_over c_1D2330 fs14">
              {{ $t("receipt.selected") }}（{{ selectedRow.length }}）
            </div>
            <div class="ml8 c_415FFF pointer" @click="clear">
              {{ $t("receipt.clear") }}
            </div>
          </div>
          <div class="flex1 list">
            <div
              v-for="item in selectedRow"
              :key="item.id"
              class="flex align_items_center pt8 pb8 pl16 pr16"
            >
              <div class="flex1 fs12 c_1D2330 overflow_hidden">
                <tooptip-text :text="item.productName?.value"></tooptip-text>
              </div>
              <i
                @click="deleteSelected(item)"
                class="iconfont-flb icon-kuaisuqingkong pointer"
                style="margin-left: 8px; font-size: 16px; color: #b7bac8"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">
        {{ $t("receipt.submit") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, nextTick, onMounted, ref, watch } from "vue";
import { ElMessage, ElTree } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import searchBox from "@/components/filter/search.vue";
import tooptipText from "@/components/tooltip-text.vue";
import {
  productGroupTreeNew,
  productGroupTypeNew,
  queryProduct,
} from "@/api/salesOrderManage";
import lang from "@/lang/index";
import { getProductGroupList } from "@/api/naviConfig";

const i18n = lang.global;
const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  show: {
    type: Boolean,
    required: true,
  },
  selected: {
    type: Array,
    default: () => [],
  },
  storeId: {
    type: [String, Number],
    required: true,
  },
});
const loading = ref(true);
const treeSearchkey = ref(""); // 选中分类
const searchkeyList = ref([]); // 分类数据
const tableData = ref([]); // 表格数据
const selectedRow = ref([]); // 选中数据
const tableRef = ref(null); // 表格ref
const productGroupTreeData = ref([]); // 分组树
const selectTree = ref(null); // 选中分组树
const tableLoading = ref(false); // 表格loading
const searchStr = ref("");
const groupTreeRef = ref(null);

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        getProductGroupType();
        getProductGroupTree();
        resetTableSelect();
      });
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => props.selected,
  (val) => {
    selectedRow.value = val;
  },
  {
    immediate: true,
  }
);

watch([() => props.storeId, selectTree], (val) => {
  if (val[0] && val[1]) {
    // 更新商品数据
    searchStr.value = "";
    getProduct();
  }
});

const onFilter = () => {
  selectTree.value = null;
  groupTreeRef.value?.setCurrentKey(null);
  getProduct();
};

/**
 * 获取商品列表分类
 */
const getProductGroupType = () => {
  productGroupTypeNew({
    pageNo: 1,
    pageSize: 9999,
  }).then((res) => {
    if (res.code === 200) {
      searchkeyList.value = res.data.records;
    } else {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    }
  });
};

/**
 * 获取商品分组树
 */
const getProductGroupTree = () => {
  // productGroupTreeNew({ groupTypeId, enabled: "YES" }).then((res) => {
  //   if (res.code === 200) {
  //     productGroupTreeData.value = res.data;
  //     loading.value = false;
  //   } else {
  //     loading.value = false;
  //     ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
  //   }
  // });
  getProductGroupList({ pageNo: 1, pageSize: 99999999 }).then((res) => {
    if (res.code === 200) {
      productGroupTreeData.value = res.data?.records || [];
      if (!selectTree.value) {
        selectTree.value = productGroupTreeData.value[0];
        groupTreeRef.value?.setCurrentKey(selectTree.value?.productGroupId);
      }
      loading.value = false;
    } else {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    }
  });
};

/**
 * 点击tree node,获取对应的商品列表
 * @param {Object} val tree node的value
 */
const handleNodeClick = (val) => {
  selectTree.value = val;
};

/**
 * 根据商品分组id获取对应的商品
 * @param {string} id 商品分组id
 */
const getProduct = () => {
  tableLoading.value = true;
  queryProduct({
    searchStr: searchStr.value,
    storeId: props.storeId,
    groupId: selectTree.value?.productGroupId,
    isGift: "NO",
  }).then((res) => {
    tableLoading.value = false;
    if (res.code === 200) {
      tableData.value = res.data || [];
      resetTableSelect();
    } else {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    }
  });
};

/**
 * 重置表格选中
 */
const resetTableSelect = () => {
  tableRef.value?.clearSelection();
  nextTick(() => {
    if (selectedRow.value && selectedRow.value.length) {
      selectedRow.value.forEach((item: any) => {
        const row = tableData.value.find((i) => i.id == item.id);
        if (row) {
          tableRef.value?.toggleRowSelection(row, true);
        }
      });
    }
  });
};

function deleteSelected(item) {
  selectedRow.value = selectedRow.value.filter((i) => i.id !== item.id);
  tableRef.value?.toggleRowSelection(
    tableData.value.find((i) => i.id == item.id),
    false
  );
}

function clear() {
  selectedRow.value = [];
  tableRef.value?.clearSelection();
}

/**
 * 确定
 */
const onConfirm = () => {
  emit("confirm", selectedRow.value);
  onClose();
};

/**
 * 选中数据
 * @param {Array} val
 */
const selectedChange = (val, row) => {
  // 如果有相同的不覆盖

  if (row) {
    // 单选
    const el = selectedRow.value.find((item) => item.id == row.id);
    if (el) {
      selectedRow.value = selectedRow.value.filter(
        (item) => item.id !== row.id
      );
    } else {
      selectedRow.value.push(row);
    }
  } else {
    // todo
    // 全选
    if (val.length) {
      // 选中
      // 1、获取val中有，selectedRow中没有的
      const arr = val.filter((item) => {
        return !selectedRow.value.find((i) => i.id == item.id);
      });

      selectedRow.value = selectedRow.value.concat(arr);
    } else {
      // 取消全选
      // 吧table中所有的选中数据删除
      selectedRow.value = selectedRow.value.filter((item) => {
        return !tableData.value.find((i) => i.id == item.id);
      });
    }
  }
};

/**
 * 关闭
 */
const onClose = () => {
  emit("update:show", false);
  emit("close");
};

// /**
//  * 不可取消选中
//  * @param {Object} row
//  * @param {Number} index
//  * @returns {Boolean}
//  */
// const selectable = (row, index) => {
//   if (props.selected && props.selected.length) {
//     return !props.selected.find((item: any) => item.skuId == row.id);
//   }
//   return true;
// };
</script>

<style lang="scss" scoped>
.left_box {
  width: 232px;
  box-sizing: border-box;
}

.selected-box {
  width: 276px;
  border-radius: 4px;
  flex-shrink: 0;
  background: #ffffff;
  border: 1px solid #dfe2e7;
  overflow: hidden;

  .list {
    display: flex;
    flex-direction: column;
    padding: 16px 0;
    overflow-y: auto;
  }
}
</style>
<style lang="scss">
.add-product-dialog {
  .el-dialog__body {
    height: 100%;
    padding: 24px;
  }

  .el-dialog__body {
    display: flex;
  }

  .el-dialog__footer {
    padding: 4px 24px 24px 24px;
  }
}
</style>
