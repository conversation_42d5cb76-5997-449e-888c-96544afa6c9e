<!--
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-02 14:49:28
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-17 15:38:53
 * @FilePath: \flb-receipt\src\views\salesOrderManage\components\audit-dialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="$t('receipt.addGroup')"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="400px"
  >
    <el-form ref="formRef" :model="form" label-position="top">
      <el-form-item
        prop="productGroupId"
        :rules="[
          {
            required: true,
            message: $t('receipt.pleaseChoose'),
            trigger: 'change',
          },
        ]"
      >
        <template #label>
          <span class="fs12 c_1D2330 lh18 fw400">
            {{ $t("receipt.productGroup") }}
          </span>
        </template>
        <el-tree-select
          filterable
          node-key="id"
          :props="treeProps"
          default-expand-all
          v-model="form.productGroupId"
          :data="productGroupTreeData"
          check-strictly
          :loading="treeLoading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { productGroupTree } from "@/api/salesOrderManage";
import { ElMessage } from "element-plus";
import { ref, defineEmits, defineProps, watch } from "vue";
import lang from "@/lang/index";
import { nextTick } from "vue";
import { addProductGroup } from "@/api/naviConfig";

const i18n = lang.global;
const treeProps = {
  label: (val) => val?.name?.value || "-",
};
const emit = defineEmits(["close", "confirm", "update:show", "refresh"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  sort: {
    type: Number,
    default: () => 1,
  },
});
const form = ref({
  productGroupId: "",
});
const formRef = ref();
const productGroupTreeData = ref([]);
const treeLoading = ref(false);

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        getProductGroupTree();
        formRef.value.resetFields();
      });
    }
  },
  {
    immediate: true,
  }
);

/**
 * 获取商品分组树
 * @param {string} [groupTypeId = '']  商品分组id
 */
const getProductGroupTree = (groupTypeId = "") => {
  treeLoading.value = true;
  productGroupTree({ groupTypeId, enabled: "YES" }).then((res) => {
    if (res.code === 200) {
      productGroupTreeData.value = res.data;
      treeLoading.value = false;
    } else {
      treeLoading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    }
  });
};

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    addProductGroup({
      sort: props.sort ? Number(props.sort) + 1 : 1,
      productGroupId: form.value.productGroupId,
    }).then((res) => {
      if (res.code === 200) {
        onClose();
        emit("refresh");
        ElMessage.success(i18n.t("receipt.operationSuccess"));
      } else {
        ElMessage.error(res.msg || i18n.t("receipt.networkError"));
      }
    });
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped></style>
