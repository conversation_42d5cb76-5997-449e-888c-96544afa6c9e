<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-13 11:38:15
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-19 16:36:37
 * @FilePath: \flb-receipt\src\components\dialog\stop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="props.show"
    class="common-confirm-dialog"
    :show-close="false"
    width="400px"
    append-to-body
    :before-close="onClose"
  >
    <div class="confirm-dialog-title flex align_items_center">
      <i class="iconfont-flb icon-tishi title-icon orange"></i>
      <span class="c_1D2330 fs16 fw600 lh24">
        {{ props.title || $t("receipt.operateTips") }}
      </span>
    </div>
    <div class="confirm-dialog-content c_1D2330 fs14 lh22">
      {{ props.content }}
    </div>
    <template #footer>
      <el-button @click="onClose">
        {{ $t("receipt.cancel") }}
      </el-button>
      <el-button type="primary" @click="onConfirm" :loading="props.loading">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineEmits, defineProps } from "vue";

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "",
  },
  show: {
    retquired: true,
    type: Boolean,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 已读
const onConfirm = () => {
  emit("confirm");
  onClose();
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>
