:root {
  --el-font-size-base: 12px;
  --el-border-color: #dfe2e7;
  --el-text-color-regular: #1d2330;
  --el-text-color-primary: #1d2330;
}

.el-table {
  --el-table-border-color: #dfe2e7;
}

.el-dropdown-menu__item {
  font-size: 12px !important;
  line-height: 28px !important;
}

.el-card.is-always-shadow {
  box-shadow: none;
  border: none !important;
}

.el-scrollbar__view {
  height: 100%;
}

.el-menu--horizontal {
  border-bottom: none !important;
}

.el-menu {
  border-right: none !important;
}

.el-menu--display,
.el-menu--display + .el-submenu__icon-arrow {
  display: none;
}

.el-message__icon,
.el-message__content {
  display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  height: auto;
  overflow: hidden;
}

.el-dialog__wrapper {
  z-index: 2048;
}

.el-main {
  padding: 0 !important;
}

.el-dropdown-menu__item--divided:before,
.el-menu,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal > .el-submenu .el-sub-menu__title:hover {
  background-color: transparent;
}

.el-dropdown-menu__item--divided:before,
.el-menu,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal > .el-submenu .el-sub-menu__title:hover {
  background-color: transparent !important;
}

.el-form-item__label,
.el-radio__label,
.el-form-item__content,
.el-tabs__item {
  font-size: 12px;
}

.el-tabs__item {
  font-weight: 400;
}

.el-dialog {
  --el-dialog-padding-primary: 0;
  border-radius: 8px;
  max-height: 680px;
}
.el-dialog__header {
  padding: 24px;
  font-weight: 550;
  position: relative;
}
.el-dialog__body {
  padding: 0 24px;
}
.el-dialog__footer {
  padding: 8px 24px 24px;
}
.el-dialog__headerbtn {
  top: 50%;
  transform: translateY(-50%);

  .el-dialog__close {
    font-size: 16px;
    color: #1d2330;
  }
}

.el-message {
  min-width: 95px;

  i {
    font-size: 16px;
  }
  .el-message__content {
    color: #1d2330;
  }
  --el-color-warning: #fe9b0e;
}
.el-message--success,
.el-message--warning,
.el-message--error {
  display: flex;
  align-items: flex-start;
  top: 45vh !important;
  z-index: 2000;
  min-height: 40px;
  background-color: #fff;
  border-color: #fff;
  color: #000000;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
}

.el-button + .el-button {
  margin-left: 8px;
}
.el-button:focus-visible {
  outline: none;
}

.el-form--label-top .el-form-item__label {
  line-height: 22px;
  padding: 0 0 4px;
}

.el-form--default.el-form--label-top .el-form-item .el-form-item__label {
  margin: 0;
}

.el-form--inline .el-form-item {
  margin-right: 16px;
}

.el-form-item__label {
  padding: 0 4px 0 0;
}

.el-table {
  .cell {
    line-height: 18px;
  }

  .el-checkbox:last-of-type {
    margin-right: 0;
  }

  .el-table__cell {
    height: min-content;
    line-height: normal;
  }

  // 滚动条部分移除border
  // .el-table__row {
  //   td {
  //     vertical-align: top;
  //   }
  // }

  &:before {
    height: 0;
  }

  .opt-wrap-div {
    div.el-divider--vertical:last-child {
      display: none;
    }
  }

  tfoot {
    tr:last-child {
      td.el-table__cell {
        border-bottom: none;
      }
    }

    td.el-table__cell {
      background-color: #fff;
      border-top: 1px solid #dfe2e7;
    }
  }
}

// 表格样式重写
.el-table th > .cell {
  font-size: 12px;
  color: #4f5363;
  font-weight: 400;
}

.el-table td .cell {
  color: #4f5363;
}

.el-table--border td,
.el-table--border th,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #e8e8e8;
}

.el-table td,
.el-table th.is-leaf {
  border-bottom: 1px solid #e8e8e8;
}

.el-table th.el-table__cell {
  background-color: #f3f6f8;
}

.el-table.is-scrolling-none th.el-table-fixed-column--left {
  background-color: #f3f6f8;
}

.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background-color: #f3f6f8;
}

.el-table.is-scrolling-none th.el-table-fixed-column--right {
  background-color: #f3f6f8;
}

.el-table th.el-table__cell.is-leaf {
  background-color: #f3f6f8;
}
.el-table--small .el-table__cell {
  padding: 0;
}
.el-table--small .cell {
  &:has(.common-status-box) {
    padding: 0 16px;
    text-overflow: clip;
  }
}

.custom_radius_table.el-table {
  border-radius: 4px 4px 0 0;

  border: 1px solid #dfe2e7;
  border-bottom: none;

  .el-table__row {
    .el-table__cell:last-child {
      border-right: 0;
    }
  }

  tr {
    th:last-child {
      border-right: 0;
    }
  }

  // .el-table__inner-wrapper:before {
  //   background-color: transparent;
  // }

  .el-table__inner-wrapper:after {
    background-color: transparent;
  }

  &:after {
    background-color: transparent;
  }
  .el-table__border-left-patch {
    background-color: transparent;
  }
}
.custom_radius4_table.el-table {
  border-radius: 4px;
  border-bottom: 1px;
  border: 1px solid #dfe2e7;

  .el-table__row {
    .el-table__cell:last-child {
      border-right: 0;
    }
  }

  tr {
    th:last-child {
      border-right: 0;
    }
  }

  .el-table__inner-wrapper:before {
    background-color: transparent;
  }

  .el-table__inner-wrapper:after {
    background-color: transparent;
  }

  &:after {
    background-color: transparent;
  }
  .el-table__border-left-patch {
    background-color: transparent;
  }
}

.el-input__suffix .el-button {
  color: #666666;
}

.el-backtop {
  background-color: rgba(0, 0, 0, 0.45);

  .icon-back-top {
    font-size: 24px;
    color: #fff;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.65);
  }
}

//抽屉样式修改
.el-drawer {
  .el-drawer__body {
    padding: 24px;
  }
  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #dfe2e7;
  }
}
.full_drawer_modal {
  left: 252px !important;
}
.no_shadow_drawer {
  box-shadow: none;
}
.radius8_drawer {
  border-radius: 8px 0 0 8px;
}
.title_drawer {
  box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);

  .el-drawer__header {
    color: #1d2330;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #dfe2e7;
    .el-drawer__close {
      font-size: 16px;
      color: #7781a1;
    }
  }
}

//按钮样式重置
.el-button--text {
  font-weight: 400;
}

.headerConfig .el-dialog__body {
  padding: 16px 24px !important;
  border-top: 1px solid #e7e7e7;
}

// 输入框高度
.el-form-item--default {
  margin-bottom: 20px;

  .el-form-item__content {
    line-height: 28px;
  }
}
.el-button {
  height: 28px;
  padding: 6px 12px;
}
.el-pagination {
  --el-pagination-button-width: 28px;
  --el-pagination-button-height: 28px;
}
// 时间选择器高度
.el-input--default {
  --el-component-size: 28px;
}

.el-radio-button__inner {
  padding: 6px 15px;
  --el-font-size-base: 12px;
}
.el-radio {
  .el-radio__label {
    --el-color-primary: #1d2330;
    --el-text-color-placeholder: #1d2330;
    color: #4f5363;
    line-height: 18px;
    padding-left: 8px;
  }
}

// 表格下拉按钮样式
.table_btn_dropdown {
  .el-dropdown-menu {
    padding: 8px 4px;
    border-radius: 8px 2px 8px 8px;
    background-color: #1d2330;
  }

  .el-dropdown-menu__item {
    color: #ffffff;
    text-align: center;
    padding: 6px 24px;
  }

  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    color: #ffffff;
    background-color: #2e374a;
  }
  .popper__arrow::after {
    border-bottom-color: transparent;
  }
  .el-popper__arrow::before {
    border: transparent !important;
    background: transparent !important;
  }
}

// 限制下拉最大宽度
// .el-cascader__dropdown {
//   max-width: 480px;
// }

// tree长文字处理
.el-tree {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .el-tree-node:focus > .el-tree-node__content {
    background: #dfeaff;
  }

  .el-tree-node.is-current > .el-tree-node__content {
    color: #1d2330;
    background: #dfeaff;
  }

  .el-tree-node__content {
    padding: 8px 16px;
    height: min-content;
    line-height: normal;
    color: #1d2330;
    border-radius: 4px;
  }

  .el-tree-node__label {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .el-radio {
    height: 26px;
  }
}
.el-tree-select__popper {
  .el-tree {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .el-tree-node__expand-icon {
      color: #1d2330 !important;
    }

    .el-tree-node__content {
      padding: 9px 8px 9px 16px;
      height: min-content;
      line-height: normal;
    }

    .el-select-dropdown__item {
      color: #3d3d3d;
      padding: 0 !important;
    }
  }
}

// .el-overlay{
//   background-color: transparent;
// }

:deep(.el-form-item__label) {
  pointer-events: none;
}

.el-input .el-input__count .el-input__count-inner {
  line-height: 1.5;
}

.el-input-group__append,
.el-input-group__prepend {
  padding: 0 8px !important;
  color: #1d2330;
  background-color: #edf0f2;
}

.el-input-number__decrease,
.el-input-number__increase {
  width: 24px !important;

  .el-icon {
    font-size: 10px;
    color: #1d2330;
  }
}

.el-pager {
  .number {
    border: 1px solid #dfe2e7;
    border-radius: 4px;
    font-weight: normal;
    font-size: 12px;
    margin-right: 8px;

    &.is-active {
      border-color: #415fff;
    }
  }

  .number:last-child {
    margin-right: 0;
  }
}

.el-radio {
  height: 28px;
  .el-radio__input.is-checked .el-radio__inner {
    background-color: #fff;
  }

  .el-radio__inner::after {
    width: 8px;
    height: 8px;
    background-color: #415fff;
  }
}
.el-table--fit .el-popper {
  max-width: 600px !important;
  left: 360px;
}

.pop_max_600 {
  max-width: 600px;
}

.el-table .el-table__cell {
  height: 33px;
  line-height: 33px;

  .cell {
    padding: 5px 16px;
  }
}

// 页脚
.el-pagination {
  .el-pagination__sizes {
    .el-select__selected-item {
      color: #1d2330;
    }

    .el-select__suffix .el-icon {
      color: #1d2330;
    }
  }

  .el-pager {
    .is-active {
      font-weight: normal;
    }
  }

  .btn-prev,
  .btn-next {
    color: #646878;
  }
}

// 按钮

.el-button {
  color: #1d2330;
  border: 1px solid #dfe2e7;
  background-color: #fff;

  &:active {
    background-color: #fff;
    color: #2a4bff;
    border: 1px solid #2a4bff;
  }
  &:hover {
    color: #5e77ff;
    background: #ffffff;
    border: 1px solid #5e77ff;
  }
  &.is-disabled {
    color: #989cac;
    background: #edf0f2;
    border: 1px solid #dfe2e7;
  }
}

.el-button--danger {
  color: #ec2d30;
  border: 1px solid #dfe2e7;
  background-color: #fff;

  &:active {
    background: #fff;
    color: #ec2d30;
    border: 1px solid #ec2d30;
  }
  &:hover {
    color: #ec2d30;
    background: #ffffff;
    border: 1px solid #ec2d30;
  }
  &.is-disabled {
    color: #989cac;
    background: #edf0f2;
    border: 1px solid #dfe2e7;
  }
}

.el-button--primary {
  background: #415fff;
  color: #ffffff;
  border: 1px solid #415fff;

  &:active {
    background-color: #2a4bff;
    color: #fff;
  }
  &:hover {
    color: #ffffff;
    background: #5e77ff;
  }
  &.is-disabled {
    color: #ffffff;
    background: #c5d8ff;
  }
}

.el-button--primary.is-link {
  color: #415fff;

  &:active {
    background: #edf0f2;
    color: #2a4bff;
  }
  &:hover {
    color: #415fff;
    background: #f3f6f8;
  }
  &.is-disabled {
    color: #c5d8ff;
    background: transparent;
  }
}

// 必填提示
.el-form-item__error {
  padding-top: 4px;
  color: #ec2d30;
}
.el-form-item.is-error {
  .el-input {
    border: 1px solid #ec2d30;

    .el-input__wrapper {
      box-shadow: none;
    }
  }

  .el-textarea {
    border: 1px solid #ec2d30;

    .el-textarea__inner {
      box-shadow: none;
    }
  }

  .el-select {
    border: 1px solid #ec2d30;

    .el-select__wrapper {
      box-shadow: none;
    }
  }
}

// input-number带后缀
.suf_input {
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #dfe2e7;
  display: flex;
  transition: all 0.3s;

  .el-input-number {
    border: none;
    box-shadow: none;
    .el-input {
      border: none;
      height: 26px;
      box-shadow: none;
    }
  }

  .suf {
    padding: 0 8px;
    height: 26px;
    line-height: 26px;
    border-left: 1px solid #dfe2e7;
    background: #edf0f2;
    color: #1d2330;
    font-size: 12px;
    white-space: nowrap;
  }

  .pre {
    padding: 0 8px;
    height: 26px;
    line-height: 26px;
    border-right: 1px solid #dfe2e7;
    background: #edf0f2;
    color: #1d2330;
    font-size: 12px;
    white-space: nowrap;
  }

  .inline_pre {
    display: flex;
    align-items: center;
    padding-left: 8px;
  }

  // 禁用
  &:has(.is-disabled) {
    background: #edf0f2;
    border: 1px solid #dfe2e7;
  }

  // 聚焦
  &:has(.is-focus) {
    border: 1px solid #415fff;
    box-shadow: 0px 0px 0px 1px #c9d6ff;
  }

  // 悬浮
  &:hover {
    border: 1px solid #5e77ff;
  }
}

.el-input {
  height: 28px;
  border: 1px solid #dfe2e7;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;

  .el-input-group__append,
  .el-input-group__prepend {
    box-shadow: none;
  }

  .el-input__wrapper {
    border: none;
    box-shadow: none;
    padding: 4px 8px;
  }

  input {
    font-size: 12px;
    color: #1d2330;
    line-height: normal;
    height: min-content;

    &::placeholder {
      color: #989cac;
    }
  }

  // 禁用
  &.is-disabled {
    background: #edf0f2;
    border: 1px solid #dfe2e7;

    .el-input__wrapper {
      background-color: transparent;
      box-shadow: none;
    }

    input {
      color: #989cac;
    }
  }

  // 聚焦
  &:has(.is-focus) {
    border: 1px solid #415fff;
    box-shadow: 0px 0px 0px 1px #c9d6ff;
  }

  // 悬浮
  &:hover {
    border: 1px solid #5e77ff;
  }

  // 统计数
  .el-input__count {
    color: #989cac;
  }
}

.el-textarea {
  border: 1px solid #dfe2e7;
  transition: all 0.3s;
  background: #ffffff;
  border-radius: 4px;

  &.is-disabled {
    background: #edf0f2;
    border: 1px solid #dfe2e7;

    .el-textarea__inner {
      box-shadow: none;
      background-color: transparent;
      color: #989cac;

      &::placeholder {
        color: #989cac;
      }
    }
  }

  &:hover {
    border: 1px solid #5e77ff;
  }

  &:has(.is-focus) {
    border: 1px solid #415fff;
    box-shadow: 0px 0px 0px 1px #c9d6ff;
  }

  .el-textarea__inner {
    padding: 5px 8px;
    box-shadow: none;
    background-color: transparent;
    border-radius: 0;
    color: #1d2330;

    &::placeholder {
      color: #989cac;
    }
  }
}

.el-select {
  border: 1px solid #dfe2e7;
  background-color: #fff;
  border-radius: 4px;
  transition: all 0.3s;
  height: 28px;
  line-height: 28px;

  .el-select__wrapper {
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0 8px;
    height: 100%;
    min-height: min-content;

    &.is-hovering {
      box-shadow: none;
    }

    .el-select__selection {
      height: 100%;
      flex-wrap: nowrap;
    }

    // .el-select__selected-item {
    //   position: absolute;
    //   top: 50%;
    //   transform: translateY(-50%);
    // }

    .is-transparent {
      color: #989cac;
    }

    .el-select__selected-item:not(.is-transparent):not(.el-select__placeholder) {
      color: #1d2330;
    }
  }

  &:hover {
    border: 1px solid #5e77ff;
  }

  &:has(.is-focused) {
    border: 1px solid #415fff;
    box-shadow: 0px 0px 0px 1px #c9d6ff;
  }

  &:has(.is-disabled) {
    background: #edf0f2;
    border: 1px solid #dfe2e7;

    .el-select__placeholder {
      color: #989cac;
    }
  }
}

.el-select-dropdown {
  .el-scrollbar__view {
    padding: 4px;
  }

  .el-select-dropdown__item {
    border-radius: 2px;
    background: #ffffff;
    padding: 10px 16px !important;
    color: #1d2330;
    height: min-content;
    line-height: normal;
    font-weight: normal;

    &.is-hovering {
      background: #f3f6f8;
    }

    &.is-selected {
      background: #eff4ff;
    }
  }
}

// 时间轴
.el-timeline-item__node--normal {
  width: 4px;
  height: 4px;
  background-color: #dfe2e7;
  border-color: #dfe2e7;
  left: 0px;
}

.el-timeline-item__tail {
  border-left: 1px solid #dfe2e7;
  left: 2px;
  transform: translateX(-50%);
}

.el-timeline-item__wrapper {
  padding-left: 16px;
}

// 时间选择器
.el-date-editor {
  height: 28px !important;
  line-height: 28px;
  box-shadow: none !important;
  border: 1px solid #dfe2e7;
  background-color: #fff;

  .el-range__icon {
    color: #646878;
  }

  .el-range-input {
    height: 100%;
    line-height: normal;
    color: #1d2330;
    background-color: transparent !important;

    &::placeholder {
      color: #989cac;
    }
  }

  &:hover {
    border: 1px solid #5e77ff;
    box-shadow: none !important;
  }

  &.is-active {
    border: 1px solid #415fff;
    box-shadow: 0px 0px 0px 1px #c9d6ff !important;
  }

  &.is-disabled {
    box-shadow: none !important;
    border: 1px solid #dfe2e7;
    background: #edf0f2;
  }
}

.el-select-dropdown
  .el-scrollbar
  .el-scrollbar__view
  .el-select-dropdown__item
  > span {
  padding: 0;
}

.lc_radio {
  margin: 0;
  gap: 8px;
  .el-radio {
    border: 1px solid #dfe2e7;
    border-radius: 4px;
    padding: 5px 8px;
    display: flex;
    align-items: center;
    margin-right: 0;

    .el-radio__label {
      padding-left: 8px;
      font-size: 12px;
      line-height: 1.5;
    }
  }
}

.el-popper.is-dark {
  border-radius: 4px;
  opacity: 0.9;
  background: #222933;
}

.el-checkbox-group {
  display: flex;
  gap: 8px;

  .el-checkbox {
    border-radius: 4px;
    background: #ffffff;
    border: 1px solid #dfe2e7;
    display: flex;
    align-items: center;
    padding: 0 8px;
    margin: 0;
    height: 28px;
    line-height: 28px;

    .el-checkbox__label {
      font-size: 12px;
      color: #4f5363;
      padding-left: 4px;
    }
  }
}

.el-input-number.is-without-controls {
  .el-input__wrapper {
    padding: 4px 8px !important;
    box-shadow: none;
  }

  .el-input__inner {
    text-align: left;
  }

  .el-input__inner {
    height: 18px;
    line-height: 18px;
  }
}

.el-input-number {
  .el-input__wrapper {
    box-shadow: none !important;

    input {
      height: 18px;
      line-height: 18px;
    }
  }
}

.el-cascader {
  .el-input.is-focus .el-input__wrapper {
    box-shadow: none;
  }

  &:not(.is-disabled):hover .el-input__wrapper {
    box-shadow: none;
  }
}

.el-cascader__dropdown {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

  .el-cascader-menu__list {
    padding: 0 8px;
    background: #ffffff;
    border-radius: 4px;
    gap: 4px;
    display: flex;
    flex-direction: column;

    .el-cascader-node {
      padding: 0 16px;
      overflow: hidden;
      font-weight: normal;

      &:not(.is-disabled):hover {
        background: #f3f6f8;
      }
    }

    .el-cascader-node.in-active-path,
    .el-cascader-node.is-active,
    .el-cascader-node.is-selectable.in-checked-path {
      background: #eff4ff;
      color: #415fff;
    }
    // .el-cascader-node:not(.is-disabled):focus,
  }
  .el-cascader-menu {
    border-right: 1px solid #dfe2e7;
    margin: 8px 0;
  }
}

.table_no_bborder {
  .el-table__inner-wrapper:before {
    background-color: #fff !important;
  }
}

.el-select__placeholder {
  font-weight: 400;
}

.table_footer_no_border {
  .el-table__footer {
    td {
      border-right: none !important;
    }
  }
}
