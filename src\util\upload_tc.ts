import COS from "cos-js-sdk-v5";
import { apiGetTcOssSTS } from "@/api/common";
import { getObjType } from "@/util/util";

interface Currency {
  [key: string]: any;
}

/**
 * fileObj
 * {
 *  [moduleId] {String}：所开发项目的moduleId，后端提供给前端（必传）
 *  [uploadType] {String}：上传的类型，后端提供给前端（必传）
 *  [file] {object}：file文件对象或者是elementUi的文件对象（必传）
 *  [packageName] {String}：传apk的时候需要传包名
 *  [fileType] {array}：可以上传的文件类型
 *  [maxSize] {Number}：文件大小限制（KB)
 *  [buId] {Number}：事业线
 *  [isMultipart] {Boolean}：是否使用分片上传
 *  [progressCallBack] {Function}:获取上传进度的回调
 * }
 */

//腾讯云上传
export async function upload_tc(fileObj: Currency) {
  console.log(fileObj);
  // debugger
  if (!fileObj) {
    throw new Error("请传入要上传的文件！");
  }

  if (!fileObj.moduleId) {
    throw new Error("请传入moduleId！");
  }

  if (!fileObj.uploadType) {
    throw new Error("请传入uploadType！");
  }

  if (!fileObj.file) {
    throw new Error("请传入要上传的文件！");
  }

  try {
    //获取文件对象和文件名称
    const fileData = fileObj.file;
    const fileName = fileObj.file.name;

    const sginParam: Currency = {
      moduleId: fileObj.moduleId,
      type: fileObj.uploadType,
    };
    if (fileObj.packageName) {
      sginParam.packageName = fileObj.packageName;
    }

    //获取上传的配置信息
    const stsResult: any = await apiGetTcOssSTS(sginParam);

    //校验获取签名是否成功
    if (stsResult.code != 200) {
      throw new Error(stsResult.msg);
    }

    //构建上传对象
    const cos = new COS({
      getAuthorization: function (options: any, callback: any) {
        callback({
          TmpSecretKey: stsResult.data.tmpSecretKey,
          TmpSecretId: stsResult.data.tmpSecretId,
          SecurityToken: stsResult.data.sessionToken,
          StartTime: stsResult.data.startTime, // 时间戳，单位秒，如：1580000000
          ExpiredTime: stsResult.data.expiredTime, // 时间戳，单位秒，如：1580000900
          XCosSecurityToken: stsResult.data.sessionToken,
        });
      },
      //可选参数
      FileParallelLimit: 3, // 控制文件上传并发数
      ChunkParallelLimit: 3, // 控制单个文件下分片上传并发数
      ProgressInterval: 1000, // 控制上传的 onProgress 回调的间隔
    });

    //拼接文件路径+文件名称
    const newFileName = Date.parse(new Date().toString()) + "/" + fileName;

    //构建上传参数
    const uploadOption: any = {
      Bucket: stsResult.data.bucketName,
      Region: stsResult.data.regionName,
      Key: newFileName,
      Body: fileData,
      onProgress: function (progressData: any) {
        if (fileObj.progressCallBack) {
          fileObj.progressCallBack(
            (progressData.loaded / progressData.total).toFixed(2)
          );
        }
      },
    };

    let putResult: Currency = {};
    if (fileObj.isMultipart && fileObj.isMultipart == true) {
      //分片上传
      putResult = await cos.multipartUpload(uploadOption);
    } else {
      //普通上传
      putResult = await cos.putObject(uploadOption);
    }
    //校验是否上传成功
    if (putResult.statusCode != 200) {
      throw new Error("上传失败！");
    }

    //上传成功返回上传结果
    putResult.fileName = fileName;
    putResult.fileUrl = newFileName;
    putResult.temporaryUrl = stsResult.data.baseUrl + "/" + newFileName;

    return putResult;
  } catch (e) {
    console.log(e);
    throw new Error("网络请求超时！");
  }
}

/**
 *  [fileUrl] {String}：要删除的文件的文件地址
 *  [moduleId] {String}：所开发项目的moduleId，后端提供给前端（必传）
 *  [type] {String}：上传的类型，后端提供给前端（必传）
 */
//腾讯云删除文件
export async function fileDelete_tc(
  fileUrl: any,
  moduleId: string,
  type: string
) {
  try {
    //获取oss签名
    const stsResult = await apiGetTcOssSTS({ moduleId, type });

    //构建上传对象
    const cos = new COS({
      getAuthorization: function (options: any, callback: any) {
        callback({
          TmpSecretId: stsResult.data.tmpSecretId,
          TmpSecretKey: stsResult.data.tmpSecretKey,
          SecurityToken: stsResult.data.token,
          StartTime: stsResult.data.StartTime, // 时间戳，单位秒，如：1580000000
          ExpiredTime: stsResult.data.ExpiredTime, // 时间戳，单位秒，如：1580000900
          XCosSecurityToken: stsResult.data.token,
        });
      },
      //可选参数
      FileParallelLimit: 3, // 控制文件上传并发数
      ChunkParallelLimit: 3, // 控制单个文件下分片上传并发数
      ProgressInterval: 1000, // 控制上传的 onProgress 回调的间隔
    });

    let result = {};

    if (getObjType(fileUrl) == "array") {
      //批量删除
      const keyArr: any = [];
      fileUrl.forEach((item: Currency) => {
        keyArr.push({ Key: item.replace(stsResult.data.cdnDomain, "") });
      });
      result = await cos.deleteMultipleObject({
        Bucket: stsResult.data.bucketName,
        Region: stsResult.data.region,
        Objects: keyArr,
      });
    } else {
      //单个删除
      result = await cos.deleteObject({
        Bucket: stsResult.data.bucketName,
        Region: stsResult.data.region,
        Key: fileUrl.replace(stsResult.data.cdnDomain, ""),
      });
    }

    return result;
  } catch (e) {
    throw new Error("网络请求超时！");
  }
}
