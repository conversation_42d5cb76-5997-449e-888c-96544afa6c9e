<template>
  <div class="avue-tags pt12"
       v-if="showTag"
       @click="contextmenuFlag = false">
    <!-- tag盒子 -->
    <div v-if="contextmenuFlag"
         class="avue-tags__contentmenu"
         :style="{ left: contentmenuX + 'px', top: contentmenuY + 'px' }">
      <div class="item"
           @click="closeOthersTags">{{ $t('tagsView.closeOthers') }}</div>
      <div class="item"
           @click="closeAllTags">{{ $t('tagsView.closeAll') }}</div>
    </div>
    <!-- 第一页设置不能关闭 -->
    <div class="avue-tags__box page_tags_box_wrap"
         :class="{ 'avue-tags__box--close': !website.isFirstPage }">
      <el-tabs v-model="active"
               type="card"
               @contextmenu="handleContextmenu"
               @tab-click="openTag"
               @tab-remove="menuTag">
        <el-tab-pane v-for="(item, index) in tagList"
                     :key="index"
                     :name="item.value"
                     :closable="item.value !== '/home/<USER>'">
          <template #label>
            {{ generateTitle(item) }}
            <!-- <i class="icon-shuaxin1"
               @click="freshRouter"
               style="font-size:12px !important"></i> -->
            <i class="icon-pin" v-if="item.value === '/home/<USER>'"
               style="font-size:12px !important"></i>
            <div class="tags-left-corner">
              <div class="tags-left-corner-inner"></div>
            </div>
            <div class="tags-right-corner">
              <div class="tags-right-corner-inner"></div>
            </div>
            <!-- <el-divider class="divider-vertical custom-divider-vertical"
                        direction="vertical"></el-divider> -->
          </template>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-dropdown class="avue-tags__menu">
        <el-button type="primary"
                   size="small">
          {{$t('tagsView.menu')}}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="$parent.isSearch=true">{{$t('tagsView.search')}}</el-dropdown-item>
          <el-dropdown-item @click.native="closeOthersTags">{{$t('tagsView.closeOthers')}}</el-dropdown-item>
          <el-dropdown-item @click.native="closeAllTags">{{$t('tagsView.closeAll')}}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
    <div :class="{ 'model-wrap': modelOn }">

    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from "vuex";
import { generateTitle, getPath } from "@/util/util"
export default {
  name: "tags",
  data () {
    return {
      active: "",
      contentmenuX: "",
      contentmenuY: "",
      contextmenuFlag: false,
      modelOn: false,
    };
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  created () { },
  mounted () {
    this.setActive();
  },
  watch: {
    tag () {
      this.setActive();
    },
    contextmenuFlag () {
      window.addEventListener("mousedown", this.watchContextmenu);
    }
  },
  computed: {
    ...mapGetters(["tagWel", "tagList", "tag", "website", "menu",]),
    ...mapState({
      showTag: state => state.common.showTag
    }),
    tagLen () {
      return this.tagList.length || 0;
    }
  },
  methods: {
    generateTitle (item) {
      return generateTitle(item, (item.meta || {}).i18n)
    },
    watchContextmenu (event) {
      if (!this.$el.contains(event.target) || event.button !== 0) {
        this.contextmenuFlag = false;
      }
      window.removeEventListener("mousedown", this.watchContextmenu);
    },
    handleContextmenu (event) {
      let target = event.target;
      // 解决 https://github.com/d2-projects/d2-admin/issues/54
      let flag = false;
      if (target.className.indexOf("el-tabs__item") > -1) flag = true;
      else if (target.parentNode.className.indexOf("el-tabs__item") > -1) {
        target = target.parentNode;
        flag = true;
      }
      if (flag) {
        event.preventDefault();
        event.stopPropagation();
        this.contentmenuX = event.clientX;
        this.contentmenuY = event.clientY;
        this.tagName = target.getAttribute("aria-controls").slice(5);
        this.contextmenuFlag = true;
      }
    },
    //激活当前选项
    setActive () {
      this.active = this.tag.value;
    },
    menuTag (value, action) {
      let { tag, key } = this.findTag(value);
      this.$store.commit("tags/DEL_TAG", tag);
      if (tag.value === this.tag.value) {
        tag = this.tagList[key === 0 ? key : key - 1]; //如果关闭本标签让前推一个
        tag.props = {
          closable: false,
          disabled: false,
          label: "",
          lazy: false,
          name: tag.value,
        }
        this.openTag(tag);
      }
    },
    openTag (item) {
      let tag;
      if (item.props.name) {
        tag = this.findTag(item.props.name).tag;
      } else {
        tag = item;
      }
      //若是薪酬项目 需要触发外层方法 修改iframe的路径
      if (tag.value.indexOf('salaryPerformance') != -1) {
        let tempData = {
          path: tag.value
        }
        this.$emit('setIframeUrl', tempData)
      } else {
        this.$emit('setIframeShowFalse')
      }
      //触发设置左侧边栏的data数据
      let urlHeader = item.props.name.split('/')[1]
      // let isRecruitUrl = false
      // if (urlHeader === 'recruitPlan' ||
      //   urlHeader === 'recruitConfig' ||
      //   urlHeader === 'recruitJob' ||
      //   urlHeader === 'jobHunter' ||
      //   urlHeader === 'recruitSession' ||
      //   urlHeader === 'questionnaire' ||
      //   urlHeader === 'talentPool' ||
      //   urlHeader === 'blacklist' ||
      //   urlHeader === 'recruitForm'
      // ) {
      //   isRecruitUrl = true
      // }
      // for (let i = 0; i < this.menu.length; i++) {
      //   if (isRecruitUrl) {
      //     if (this.menu[i].path.indexOf('personRecruit') != -1) {
      //       this.$emit("setChildrenMenu", this.menu[i].children);
      //     }
      //   }
      //   if (this.menu[i].path.indexOf(urlHeader) != -1) {
      //     this.$emit("setChildrenMenu", this.menu[i].children);
      //   }
      // }
      for (let i = 0; i < this.menu.length; i++) {
        if (this.menu[i].children && this.menu[i].children.length) {
          const childMenu = this.menu[i].children
          for (let j = 0; j < childMenu.length; j++) {
            if (childMenu[j].path.indexOf(urlHeader) != -1) {
              this.$emit("setChildrenMenu", this.menu[i].children)
              this.$emit("setActiveMenu", this.menu[i])
            }
          }
        }
      }
      this.$router.push({
        path: getPath({
          name: tag.label,
          src: tag.value
        }, tag.meta),
        query: tag.query
      });
      //同步更新顶部菜单栏的状态
      this.$emit("setTopMenuHighlight")
    },
    closeOthersTags () {
      this.contextmenuFlag = false;
      this.$store.commit("tags/DEL_TAG_OTHER");
    },
    findTag (value) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.value === value) {
          tag = item;
          key = index;
        }
      });
      return { tag: tag, key: key };
    },
    closeAllTags () {
      this.contextmenuFlag = false;
      this.$store.commit("tags/DEL_ALL_TAG");
      this.$router.push({
        path: getPath({
          src: '/home/<USER>'
        })
      });
    },
    //点击刷新当前tag下的页面
    // freshRouter () {
    //   console.log('refreshRouter')
    //   this.$emit('refreshRouter')
    // }
  }
};
</script>
<style lang="scss">
.page_tags_box_wrap {
  padding-right: 4px !important;

  .el-tabs__nav-next,
  .el-tabs__nav-prev {
    top: -4px;
  }

}
</style>
<style lang="scss" scoped>
.avue-tags {
  height: 44px
}

.custom-divider-vertical {
  position: relative;
  right: -39px;
  top: -2px;
}

.avue-tags__contentmenu .item {
  font-size: 12px;
}

.model-wrap {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background: rgba(0, 0, 0, 0.5)
}
</style>


