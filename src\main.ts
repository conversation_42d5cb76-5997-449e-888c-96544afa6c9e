import "./public-path";
import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persist"; //pinia数据持久化插件
import App from "./App.vue";
import router from "./router";
import "./permission";
import store from "./store";
import ElementPlus from "element-plus";
import VueI18n from "./lang";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import "element-plus/dist/index.css";
import "./styles/common.scss";
// import zhCn from "element-plus/es/locale/lang/zh-cn";
import "dayjs/locale/zh-cn";
import { loadStyle,setLang} from "./util/util";
import { iconfontUrl, iconfontVersion } from "@/config/env";

// 菲乾坤环境-动态加载阿里云字体库（乾坤环境下直接使用工作台图标库）
if (!(window as any).__POWERED_BY_QIANKUN__) {
  iconfontVersion.forEach((ele) => {
    loadStyle(iconfontUrl.replace("$key", ele));
  });
}

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

//挂载节点
let instance = null;
function render(props = { container: null }) {
  const { container } = props;
  instance = createApp(App);
  instance
    .use(ElementPlus)
    .use(store)
    .use(router)
    .use(VueI18n)
    .use(pinia)
    .mount(
      container ? container.querySelector("#receipt-app") : "#receipt-app"
    );
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    instance.component(key, component);
  }
}

// 非乾坤环境
if (!(window as any).__POWERED_BY_QIANKUN__) {
  render();
}
export async function bootstrap() {
  console.log("开单管理初次注册================");
}

export async function mount(props: any) {
  console.log("开单管理挂载子应用==============");
  
  store.commit("user/SET_TOKEN", props.token)
  store.commit("user/SET_USERIFNO", props.userInfo)
  store.commit("user/SET_PERSONIFNO", props.personInfo)
  store.commit("user/SET_CURRENCYINFO", props.currencyInfo)

  //设置多语言
  setLang(props.language, props.langIndex).then(()=>{

    //多语言设置后再进行挂载
    render(props)

    //判断跳转页面
    if(props.appJumpPath){
      router.push(props.appJumpPath)
    }

    //关闭子应用加载动效
    if (props.appLoading) {
      props.appLoading.close()
    }
    
  })
}

export async function unmount() {
  console.log("开单管理子应用卸载==========");
  instance.unmount();
  instance._container.innerHTML = "";
  instance = null;
}
