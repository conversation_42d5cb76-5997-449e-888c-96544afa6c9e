<template>
  <el-drawer
    :model-value="show"
    direction="rtl"
    :append-to-body="true"
    :before-close="onClose"
    size="720px"
    :title="$t('receipt.giftGiveaway')"
    class="title_drawer radius8_drawer"
    modal-class="transparent_modal top_drawer_modal"
  >
    <div>
      <el-table
        :data="goodsData"
        size="small"
        border
        class="custom_radius4_table"
        style="width: 100%"
      >
        <el-table-column
          prop="skuId"
          :label="$t('receipt.commodity')"
          min-width="140"
        >
          <template #header>
            {{ $t("receipt.giftGiveaway") }}
            <span class="c_EC2D30">*</span>
          </template>
          <template #default="scope">
            <el-select
              @change="(val) => onGiftsChange(val, scope.row)"
              v-model="scope.row.skuId"
              :placeholder="$t('receipt.pleaseChoose')"
              class="widthP100"
              filterable
              reserve-keyword
              :loading="selectLoading"
            >
              <el-option
                v-for="item in giftsList"
                :key="item.skuId"
                :label="item.productName.value"
                :value="item.skuId"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="stockQuantity"
          :label="$t('receipt.inventoryQuantity')"
          min-width="100"
          :formatter="tableFormat"
        >
        </el-table-column>
        <el-table-column
          prop="quantity"
          :label="$t('receipt.giftNumber')"
          min-width="100"
        >
          <template #header>
            {{ $t("receipt.giftNumber") }}
            <span class="c_EC2D30">*</span>
          </template>
          <template #default="scope">
            <el-input
              v-model="scope.row.quantity"
              type="number"
              :placeholder="$t('receipt.pleaseEnter')"
              :formatter="
                (val) => numberFormat(val, 0, Number(scope.row.stockQuantity))
              "
              :parser="
                (val) => numberFormat(val, 0, Number(scope.row.stockQuantity))
              "
              @change="changeNum(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="$t('receipt.operation')" width="80">
          <template #default="scope">
            <el-button link type="danger" @click="del(scope.$index)">
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <div
        style="margin-top: -1px"
        class="c_415FFF bgc_FAFBFC flex justify_content_center align_items_center border_radius4_bottom b_solid_default border_top_none pointer lh28"
        @click="add"
      >
        <el-icon class="mr4"><Plus /></el-icon>
        {{ $t("receipt.addGift") }}
      </div>
    </div>

    <template #footer>
      <div class="t_left">
        <el-button type="primary" @click="submit">
          {{ $t("receipt.determine") }}
        </el-button>
        <el-button @click="onClose">
          {{ $t("receipt.cancel") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from "vue";
import { ElMessage } from "element-plus";
import { getGiftListByProductIds } from "@/api/salesOrderManage";
import emptyBox from "@cp/empty/index.vue";
import { tableFormat, numberFormat } from "@/util/util";
import { round } from "@/util/numberUtil";
import { multiply } from "lodash";
import lang from "@/lang/index";
import { nextTick } from "vue";

const i18n = lang.global;
const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  productIds: {
    type: Array,
    required: true,
  },
  storeId: {
    type: [String, Number],
    required: true,
  },
  show: {
    type: Boolean,
    required: true,
  },
});

const giftsList = ref([]);
const goodsData = ref([{}]);
const selectLoading = ref(false);

watch(
  [() => props.productIds, () => props.storeId, () => props.show],
  (val) => {
    if (val[0] && val[1] && val[2]) {
      nextTick(() => {
        getGiftsList();
      });
    }
  },
  {
    immediate: true,
  }
);

// 获取数据
const getGiftsList = () => {
  selectLoading.value = true;
  getGiftListByProductIds({
    productIdList: props.productIds,
    storeId: props.storeId,
  })
    .then((res) => {
      selectLoading.value = false;

      if (res.code == 200) {
        for (const key in res.data) {
          giftsList.value = [
            ...giftsList.value,
            ...res.data[key].map((el) => ({
              ...el,
              mainSkuId: key,
              isGiveaway: "YES",

              skuId: el.id,
              skuCode: el.productCode,
              skuName: el.productName,
              isBarcode: "NO",
              unitId: el.unitId,
              unitName: el.unitName,
              quantity: Number(el.stockQuantity || 0) ? 1 : 0, //货品数量
              originalUnitPrice: round(Number(el.price)), //货品原价
              discountUnitPrice: 0, //货品优惠后价格
              originalTotalPrice: round(Number(el.price)), //货品优惠前总价
              discountTotalPrice: 0, //货品优惠后总价
              stockQuantity: el.stockQuantity, //库存
            })),
          ];
          // skuId相同的保留一个
          giftsList.value = giftsList.value.filter(
            (item, index) =>
              index ===
              giftsList.value.findIndex((el) => el.skuId === item.skuId)
          );
        }
      } else {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      }
    })
    .catch((res) => {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// 删除
const del = (index: number) => {
  goodsData.value.splice(index, 1);
};

// 添加
const add = () => {
  goodsData.value.push({});
};

// 选择赠品
const onGiftsChange = (val, row) => {
  const value = giftsList.value.find((item) => item.skuId == val);
  if (value) {
    goodsData.value = goodsData.value.map((item: any) => {
      if (item.skuId == val) {
        return { ...item, ...value };
      } else {
        return item;
      }
    });
  }
};

// 修改数量 计算总价
const changeNum = (row) => {
  row.originalTotalPrice = round(
    multiply(Number(row.originalUnitPrice), Number(row.quantity))
  );
};

/**
 * 关闭
 */
const onClose = () => {
  emit("update:show", false);
  emit("close");
};

const submit = () => {
  let isBreak = false;
  goodsData.value.forEach((item: any) => {
    if (!item.skuId) {
      isBreak = true;
      ElMessage.warning(i18n.t("receipt.pleaseChooseGift"));
      return;
    }
    if (!item.quantity) {
      isBreak = true;
      ElMessage.warning(i18n.t("receipt.pleaseEnterGiftQuantity"));
      return;
    }
  });
  if (isBreak) return;
  emit("confirm", goodsData.value);
  emit("update:show", false);
};
</script>
