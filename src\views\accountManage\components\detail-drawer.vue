<template>
  <el-drawer
    :model-value="props.show"
    direction="rtl"
    :append-to-body="true"
    :modal="false"
    @close="onClose"
    size="100%"
    class="no_shadow_drawer"
    modal-class="detail_drawer_modal top_drawer_modal"
    :title="`${$t('receipt.storeDetail')}`"
  >
    <div
      class="flex flex_direction_column overflow_hidden"
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
    >
      <!-- 表格 -->
      <div class="mt8">
        <el-table
          :data="data || []"
          size="small"
          border
          :height="tableHeight"
          class="table_no_bborder"
          :class-name="
            props.isAll ? 'custom_radius_table' : 'custom_radius4_table'
          "
          style="width: 100%"
        >
          <el-table-column
            prop="storeCode"
            :label="$t('receipt.storeCode')"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.storeCode || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="storeName"
            :label="$t('receipt.storeName')"
            min-width="258"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.storeName?.value || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="customerName"
            :label="$t('receipt.belongCustom')"
            min-width="100"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.customerName?.value || "-" }}
            </template>
          </el-table-column>
          <template #empty>
            <empty-box :title="$t('receipt.noDocuments')"></empty-box>
          </template>
        </el-table>
      </div>
      <pagination
        v-if="props.isAll"
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>
  </el-drawer>
</template>

<script setup>
import {
  ref,
  defineEmits,
  defineProps,
  onMounted,
  onUnmounted,
  watch,
  reactive,
  nextTick,
} from "vue";
import lang from "@/lang/index";
import emptyBox from "@/components/empty/index.vue";
import { ElMessage } from "element-plus";
import { getCustomerStoreApi } from "@/api/salesOrder";
import website from "@/config/website";
import pagination from "@/components/pagination/index.vue";

const i18n = lang.global;

const emit = defineEmits(["refresh", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  isAll: {
    type: Boolean,
    default: false,
  },
});
const loading = ref(false);
const tableHeight = ref(0);
const data = ref([]);
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        pageObj.pageNo = 1;
        getTableHeight();
        if (props.isAll) {
          getList();
        } else {
          data.value = props.data.storeList;
        }
      });
    }
  },
  { immediate: true }
);
onMounted(() => {
  window.addEventListener("resize", getTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

const getList = () => {
  loading.value = true;
  getCustomerStoreApi({
    moduleId: website.moduleId,
    pageNo: pageObj.pageNo,
    pageSize: pageObj.pageSize,
    sysUserIdList: [props.data?.userId],
  })
    .then((res) => {
      loading.value = false;
      if (res.code == 200) {
        data.value = res.data?.records || [];
        pageObj.total = Number(res.data?.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      loading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 计算表格高度
const getTableHeight = () => {
  tableHeight.value = window.innerHeight - (props.isAll ? 149 : 110) - 58;
};

function onClose() {
  emit("update:show", false);
}

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};
</script>

<style lang="scss" scoped></style>
