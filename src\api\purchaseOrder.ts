import request from "./axios";
import { baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 采购订单管理
// 列表(分页)
export const getPurchasePageApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/getPage",
    method: "get",
    params: data,
  });

// 根据Id获取详情
export const getPurchaseInfoByIdApi = (id: string) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/getById/" + id,
    method: "get",
  });

// 根据采购单号获取详情
export const getPurchaseInfoByNoApi = (purchaseOrderNo: string) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/getByPurchaseOrderNo/" + purchaseOrderNo,
    method: "get",
  });

// 提交(新增、修改、保存草稿)
export const submitPurchaseApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/submit",
    method: "post",
    data: data,
  });

// 删除
export const delPurchaseApi = (idList: string) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/deleteByIds/" + idList,
    method: "DELETE",
  });

// 状态修改、撤销
export const updateStatusApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/updateStatusById",
    method: "put",
    data: data,
  });

// 批量状态修改、撤销
export const batchUpdateStatusApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/updateStatusByIds",
    method: "put",
    data: data,
  });

// 采购单信息导入
export const importPurchaseApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseOrder/import",
    method: "post",
    data: data,
  });

// 采购货品串码明细管理
// 列表(分页)
export const getPurchaseCodeApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseBarcode/",
    method: "get",
    params: data,
  });

// 采购货品明细管理
// 列表(分页)
export const getPurchaseSkuApi = (data: Params) =>
  request({
    url: baseUrl + "/erp/purchaseSku/getPage",
    method: "get",
    params: data,
  });

// 计算最优惠优惠券
export const countMaxCouponPrice = (data: Params) =>
  request({
    url: baseUrl + "/order/orderDiscount/countMaxCouponPrice",
    method: "post",
    data: data,
  });
