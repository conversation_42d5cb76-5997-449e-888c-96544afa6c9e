<!--
 * @Author: spanull <EMAIL>
 * @Date: 2024-11-14 09:33:17
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-06 09:57:49
 * @FilePath: \flb-receipt\src\page\loading\jumpLoading.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="avue-home" v-loading="true">
    <!-- loading -->
  </div>
</template>

<script>
import website from "@/config/website";
import { workbenchUrl, logOutPage } from "@/config/env";
import { getToken } from "@/util/auth";
import base64 from "@/util/base64.ts";
import lang from "@/lang/index";

const i18n = lang.global;
export default {
  name: "jumpLoading",
  components: {},
  data() {
    return {};
  },
  computed: {},
  created() {
    let jumpParam = this.$route.query;
    if (JSON.stringify(jumpParam) != "{}") {
      jumpParam = base64.decodeQuery(jumpParam);
      localStorage.setItem("buId", jumpParam.buId);
    }
    let token;
    if (jumpParam.loginCode) {
      token = jumpParam.loginCode;
      //设置token
      this.$store.commit("user/SET_TOKEN", token);
    } else {
      token = getToken();
    }
    if (token) {
      //设置userInfo
      this.$store.dispatch("user/GetUserInfo");

      //设置personInfo
      this.$store.dispatch("user/GetPersonInfo");

      // 获取菜单信息
      this.$store
        .dispatch("user/GetAuthorInfo")
        .then(() => {
          if (this.$store.getters.menu) {
            //跳转到页面
            let pathJson = {};
            if (jumpParam.routerPath) {
              pathJson.path = jumpParam.routerPath;
            } else {
              pathJson.path = website.fistPage.value;
            }
            if (jumpParam.dataParam) {
              pathJson.query = JSON.parse(jumpParam.dataParam);
            }
            this.$router.push(pathJson);
          } else {
            this.$router.push({ path: "403" });
          }
        })
        .catch(() => {
          this.$message.error(i18n.t("receipt.networkError"));
        });
    } else {
      this.$store.dispatch("user/LogOut").then(() => {
        console.log(logOutPage());
        window.location.href = workbenchUrl + "/#/login" + logOutPage();
      });
    }
  },
  methods: {},
};
</script>
<style lang="scss" scoped></style>
