<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-07-21 09:57:32
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-23 11:03:55
 * @FilePath: \flb-receipt\src\views\cashierDayily\list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="p24 bgc_white overflow_hidden flex flex_direction_column position_re"
    v-loading="loading"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <div class="flex gap8">
      <el-select
        v-model="filterObj.storeIdList"
        class="width168"
        :placeholder="$t('receipt.store')"
        size="default"
        :loading="storeData.loading"
        clearable
        multiple
        collapse-tags-tooltip
        collapse-tags
        @blur="getList"
      >
        <el-option
          v-for="item in storeData.list"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <!-- 开始时间 -->
      <el-date-picker
        v-model="filterObj.timeList[0]"
        :placeholder="$t('receipt.startTime')"
        size="default"
        style="width: 156px"
        type="date"
        value-format="YYYY-MM-DD"
        :clearable="false"
        :disabled-date="
          (time) => {
            return (
              filterObj.timeList[1] &&
              time.getTime() > new Date(filterObj.timeList[1]).getTime()
            );
          }
        "
      />
      <!-- 结束时间 -->
      <el-date-picker
        v-model="filterObj.timeList[1]"
        :placeholder="$t('receipt.endTime')"
        size="default"
        style="width: 156px"
        type="date"
        value-format="YYYY-MM-DD"
        :clearable="false"
        :disabled-date="
          (time) => {
            return (
              filterObj.timeList[0] &&
              time.getTime() <
                new Date(filterObj.timeList[0]).getTime() - 86400000
            );
          }
        "
      />
    </div>
    <div
      class="flex1 flex flex_direction_column overflow_y_auto mt24 mb12"
      :class="tableData.length ? '' : 'justify_content_center'"
      style="gap: 16px"
    >
      <template v-if="tableData.length">
        <div
          v-for="(item, index) in tableData"
          :key="index"
          class="flex flex_direction_column p16 border_radius4"
          style="border: 1px solid #dfe2e7"
        >
          <div class="flex align_items_center">
            <i
              class="iconfont-flb icon-lansemendianicon c_415FFF mr8 align_bottom"
              style="font-size: 24px"
            ></i>
            <span class="fs16 fw500 lh24 c_1D2330">
              {{ item?.storeName?.value ?? "" }}
            </span>
          </div>
          <div class="flex mt16">
            <div
              @click="
                saleDrawerRef.openDrawer({
                  data: item,
                  title: $t('receipt.salesOrderData'),
                })
              "
              class="flex flex1 align_items_center pt12 pb12 pl16 pr16 position_re pointer mr16 border_radius2"
              style="
                background: linear-gradient(270deg, #e8f6ff 3%, #f4f7ff 98%);
              "
            >
              <div class="flex flex_direction_column flex1">
                <div class="fs12 lh18" style="color: #5c616b">
                  {{ $t("receipt.salesOrder2") }}
                </div>
                <div class="mt4 fs20 fw500" style="color: #072137">
                  {{ item.orderQty ?? "-" }}
                </div>
              </div>
              <i
                class="iconfont-flb icon-jinru c_415FFF mr8 align_bottom"
                style="font-size: 16px; z-index: 2"
              ></i>
              <div class="position_ab back-img"></div>
            </div>
            <div
              @click="
                saleProductDrawerRef.openDrawer({
                  data: item,
                  title: $t('receipt.salesProductData'),
                })
              "
              class="flex flex1 align_items_center pt12 pb12 pl16 pr16 position_re pointer mr16 border_radius2"
              style="
                background: linear-gradient(270deg, #e8f6ff 3%, #f4f7ff 98%);
              "
            >
              <div class="flex flex_direction_column flex1">
                <div class="fs12 lh18" style="color: #5c616b">
                  {{ $t("receipt.salesProduct") }}
                </div>
                <div class="mt4 fs20 fw500" style="color: #072137">
                  {{ item.orderSkuQty ?? "-" }}
                </div>
              </div>
              <i
                class="iconfont-flb icon-jinru c_415FFF mr8 align_bottom"
                style="font-size: 16px; z-index: 2"
              ></i>
              <div class="position_ab back-img"></div>
            </div>
            <div
              @click="
                returnOrderDrawerRef.openDrawer({
                  data: item,
                  title: $t('receipt.returnOrderData'),
                })
              "
              class="flex flex1 align_items_center pt12 pb12 pl16 pr16 position_re pointer mr16 border_radius2"
              style="
                background: linear-gradient(270deg, #e8f6ff 3%, #f4f7ff 98%);
              "
            >
              <div class="flex flex_direction_column flex1">
                <div class="fs12 lh18" style="color: #5c616b">
                  {{ $t("receipt.returnOrder") }}
                </div>
                <div class="mt4 fs20 fw500" style="color: #072137">
                  {{ item.returnOrderQty ?? "-" }}
                </div>
              </div>
              <i
                class="iconfont-flb icon-jinru c_415FFF mr8 align_bottom"
                style="font-size: 16px; z-index: 2"
              ></i>
              <div class="position_ab back-img"></div>
            </div>
            <div
              @click="
                returnProductDrawerRef.openDrawer({
                  data: item,
                  title: $t('receipt.returnProductData'),
                })
              "
              class="flex flex1 align_items_center pt12 pb12 pl16 pr16 position_re pointer border_radius2"
              style="
                background: linear-gradient(270deg, #e8f6ff 3%, #f4f7ff 98%);
              "
            >
              <div class="flex flex_direction_column flex1">
                <div class="fs12 lh18" style="color: #5c616b">
                  {{ $t("receipt.returnProduct") }}
                </div>
                <div class="mt4 fs20 fw500" style="color: #072137">
                  {{ item.returnOrderSkuQty ?? "-" }}
                </div>
              </div>
              <i
                class="iconfont-flb icon-jinru c_415FFF mr8 align_bottom"
                style="font-size: 16px; z-index: 2"
              ></i>
              <div class="position_ab back-img"></div>
            </div>
          </div>
          <div class="flex align_items_center mt16">
            <div class="flex1 fs12 lh18 c_4F5363">
              {{ $t("receipt.statisticsDateM") }}
              <span>
                {{ item.date ?? "-" }}
              </span>
            </div>
            <div class="flex_shrink0 flex align_items_center">
              <div class="fs12 fw400 lh18 c_4F5363 mr4">
                {{ $t("receipt.collectionM") }}
              </div>
              <div
                class="fs16 fw500 lh24 c_172136 t_right"
                style="min-width: 100px"
              >
                {{ formatMoney(item.orderCashSum || 0, true) }}
              </div>
              <el-tooltip
                effect="dark"
                placement="top"
                popper-class="pt8 pb8 pl14 pr14"
              >
                <i
                  class="iconfont-flb icon-caozuotishi ml4 align_bottom"
                  style="font-size: 14px"
                ></i>
                <template #content>
                  <div class="flex flex_direction_column gap4">
                    <template
                      v-for="(el, index2) in item.paymentChannelSumList || []"
                      :key="index2"
                    >
                      <div class="flex">
                        <div class="fs12 lh18">
                          {{ $t("receipt.payType") }}{{ index2 + 1 }}：
                        </div>
                        <div class="flex1 fs12 lh18" style="white-space: wrap">
                          {{ el?.terminalName?.value ?? "-" }}
                          -
                          {{ el?.bankName?.value ?? "-" }}
                          -
                          {{ el?.payWay ?? "-" }}
                        </div>
                        <div class="fs12 lh18 pl24">
                          {{ formatMoney(el.paymentPrice || 0, true) }}
                        </div>
                      </div>
                    </template>
                  </div>
                </template>
              </el-tooltip>
              <el-divider class="ml24 mr24" direction="vertical" />
              <div class="fs12 fw400 lh18 c_4F5363 mr4">
                {{ $t("receipt.returnAmountM2") }}
              </div>
              <div
                class="fs16 fw500 lh24 c_EC2D30 t_right"
                style="min-width: 100px"
              >
                {{ formatMoney(item.returnOrderCashSum || 0, true) }}
              </div>
              <el-divider class="ml24 mr24" direction="vertical" />
              <div class="fs12 fw400 lh18 c_4F5363 mr4">
                {{ $t("receipt.totalM") }}
              </div>
              <div
                class="fs16 fw500 lh24 c_172136 t_right"
                style="min-width: 100px"
              >
                {{ formatMoney(item.cashSum || 0, true) }}
              </div>
            </div>
          </div>
        </div>
      </template>
      <empty-box v-else></empty-box>
    </div>
    <pagination
      style="z-index: 10"
      :showShadow="false"
      :pageObj="pageObj"
      @currentChange="currentChange"
      @onPageChange="onPageChange"
    ></pagination>
    <!-- <div
      class="position_ab"
      style="
        height: 100px;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0) 0%,
          #ffffff 36%,
          #ffffff 100%
        );
      "
    ></div> -->

    <sale-order-detail-drawer ref="saleDrawerRef" />
    <sale-product-detail-drawer ref="saleProductDrawerRef" />
    <return-order-detail-drawer ref="returnOrderDrawerRef" />
    <return-product-detail-drawer ref="returnProductDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import lang from "@/lang/index";
import { dayjs, ElMessage } from "element-plus";
import { formatMoney } from "@/util/numberUtil";
import pagination from "@/components/pagination/index.vue";
import { getStoreSelectorApi } from "@/api/salesOrderManage";
import { getCashierDayily } from "@/api/cashierDayily";
import emptyBox from "@/components/empty/index.vue";
import saleOrderDetailDrawer from "./components/saleOrderDetailDrawer.vue";
import saleProductDetailDrawer from "./components/saleProductDetailDrawer.vue";
import returnOrderDetailDrawer from "./components/returnOrderDetailDrawer.vue";
import returnProductDetailDrawer from "./components/returnProductDetailDrawer.vue";

const i18n = lang.global;
const loading = ref(false);
const filterObj = reactive({
  storeIdList: [],
  timeList: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const storeData = reactive({
  list: [],
  loading: false,
});
const tableData = ref([]);
const saleDrawerRef = ref(null);
const saleProductDrawerRef = ref(null);
const returnOrderDrawerRef = ref(null);
const returnProductDrawerRef = ref(null);

watch(
  [() => filterObj.timeList],
  () => {
    getList();
  },
  {
    deep: true,
  }
);

onMounted(() => {
  getStore();
  getList();
});

// 获取门店
const getStore = () => {
  storeData.loading = true;
  getStoreSelectorApi({}).then((res: any) => {
    storeData.loading = false;
    if (res.code !== 200) return;
    storeData.list = (res.data || []).map((ele: any) => {
      return {
        ...ele,
        nameValue: ele.name,
        name: ele.name.value,
      };
    });
  });
};

const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, filterObj) as any;
  getCashierDayily(params).then((res: any) => {
    loading.value = false;
    if (res.code !== 200) {
      ElMessage.error(res.msg);
      return;
    }
    tableData.value = res.data.records || [];
    pageObj.total = Number(res.data.total || 0);
  });
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};
</script>

<style scoped>
.back-img {
  background: url("../../assets/img/data-item-back.png");
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
}
</style>
