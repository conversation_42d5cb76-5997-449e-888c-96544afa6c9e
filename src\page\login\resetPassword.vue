<template>
  <div>
    <el-form
      class="login-form resetPassword"
      :rules="loginRules"
      ref="loginForm"
      :model="loginForm"
      label-width="0"
      size="large"
    >
      <!-- 手机号 -->
      <el-form-item prop="phone">
        <el-input v-model="loginForm.phone" placeholder="请输入手机号">
          <template #prefix><i class="icon-31dianhua c_rgba0004"></i></template>
        </el-input>
      </el-form-item>
      <!-- 验证码 -->
      <el-form-item prop="verificationCode">
        <el-row :span="24">
          <el-input
            @keyup.enter.native="handleLogin"
            :maxlength="code.len"
            v-model="loginForm.verificationCode"
            style="width: 280px"
            placeholder="请输入验证码"
          >
            <template #prefix
              ><i class="icon-yanzhengma c_rgba0004"></i
            ></template>
          </el-input>
          <div class="login-num-code">
            <span class="login-num">
              <el-button
                type="primary"
                text
                class="send-btn"
                plain
                :disabled="!isClick"
                @click="onGetCode"
                >{{ sendStr }}</el-button
              >
            </span>
          </div>
        </el-row>
      </el-form-item>
      <!-- 密码 -->
      <el-form-item prop="oldPassword">
        <el-input
          v-model="loginForm.oldPassword"
          type="password"
          autocomplete="new-password"
          placeholder="请输入新密码（英文+数字组合，8～16位）"
        >
          <template #prefix> <i class="icon-lock-on c_rgba0004"></i></template>
        </el-input>
      </el-form-item>
      <!-- 密码 -->
      <el-form-item prop="newPassword">
        <el-input
          v-model="loginForm.newPassword"
          type="password"
          autocomplete="new-password"
          placeholder="请再次输入新密码"
        >
          <template #prefix> <i class="icon-lock-on c_rgba0004"></i></template>
        </el-input>
      </el-form-item>
      <!-- 重置密码 -->
      <el-form-item style="margin-top: 60px; margin-bottom: 0">
        <el-button
          type="primary"
          @click.prevent="handleLogin"
          class="login-submit"
        >
          重置密码</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { randomLenNum } from "@/util/util";
import { workbenchUrl, logOutPage } from "@/config/env";
import { mapGetters } from "vuex";
import website from "@/config/website";
import { setStore, getStore } from "@/util/store";
import { setTheme } from "@/util/util";
import { retrievePasswordApi, sendSmsApi, verifyApi } from "@/api/user";
import { getHourNum, setHourNum, getDayNum, setDayNum } from "@/util/auth";
export default {
  name: "userlogin",
  data() {
    const validateCode = (rule, value, callback) => {
      if (this.code.value != value) {
        callback(new Error("请输入正确的验证码"));
      } else {
        callback();
      }
    };
    // const validatePassword = (rule, value, callback) => {
    //     var re = /^(?=.*[a-z])(?=.*\d)[^]{8,16}$/;
    //     if (!value || value && !re.test(value)) {
    //         callback(new Error('请输入新密码(英文+数字组合,8～16位)'));
    //     }
    //     else {
    //         callback();
    //     }
    // };
    const validatePwd = (rule, value, callback) => {
      if (value !== this.loginForm.oldPassword) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    const validatePass = (rule, value, callback) => {
      //必须包含⼤⼩写字母、数字长度再8-16位之间
      let regex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{8,16}");
      if (value === "") {
        callback(new Error("请输⼊密码"));
      } else if (value.length < 8 || value.length > 16) {
        callback(new Error("请输⼊8~16位密码"));
      } else if (!regex.test(value)) {
        callback(new Error("密码（英文+数字组合，8～16位）"));
      } else {
        callback();
      }
    };
    return {
      sendStr: "发送验证码",
      isClick: true, // 发送按钮点击状态
      loginForm: {
        oldPassword: "",
        phone: "",
        newPassword: "",
        verificationCode: "", //验证码
      },
      isRemember: false,
      code: {
        src: "",
        value: "",
        len: 6,
        type: "text",
      },
      loginRules: {
        phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        oldPassword: [
          { required: true, validator: validatePass, trigger: "blur" },
          { min: 8, message: "密码长度最少为6位", trigger: "blur" },
          { required: true, message: "请输入", trigger: "blur" },
        ],
        newPassword: [
          { required: true, validator: validatePwd, trigger: "blur" },
          { min: 8, message: "密码长度最少为6位", trigger: "blur" },
        ],
        verificationCode: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { min: 6, max: 6, message: "验证码长度为6位", trigger: "blur" },
        ],
      },
      passwordType: "password",

      buIdList: [],
    };
  },
  created() {
    // this.refreshCode();
    // this.getLoginInfoLocal();
  },
  computed: {
    ...mapGetters(["tagWel"]),
  },
  methods: {
    //得到随机的颜色值
    randomColor() {
      let r = Math.floor(Math.random() * 256);
      let g = Math.floor(Math.random() * 256);
      let b = Math.floor(Math.random() * 256);
      return "rgb(" + r + "," + g + "," + b + ")";
    },
    // 获取验证码
    onGetCode() {
      if (getDayNum() >= 20) {
        this.$message.error("今日验证码发送次数已达上限，请明日再试");
        return;
      }
      if (getHourNum() >= 5) {
        this.$message.error("一小时内验证码发送次数已达上限，请稍后再试");
        return;
      }
      const params = {
        phone: this.loginForm.phone,
      };

      sendSmsApi(params)
        .then((res) => {
          if (res.success) {
            this.validateBtn();
            this.$message.success("验证码获取成功！");
            const hourNum = getHourNum() | 0;
            const dayNum = getDayNum() | 0;
            setHourNum(hourNum + 1);
            setDayNum(dayNum + 1);
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((res) => {
          this.$message.error(res?.msg || i18n.t("receipt.networkError"));
        });
    },
    // 验证-码发送倒计时
    validateBtn() {
      //倒计时
      let time = 59;
      let timer = setInterval(() => {
        if (time == 0) {
          clearInterval(timer);
          this.isClick = true;
          this.sendStr = "发送验证码";
        } else {
          if (this.language === "en") {
            this.sendStr = `try again after ${time} seconds`;
          } else {
            if (this.language === "es") {
              this.sendStr = `inténtalo despues de ${time} segunodos`;
            } else {
              this.sendStr = time + "秒后重试";
            }
          }
          this.isClick = false;
          time--;
        }
      }, 1000);
    },

    //点击重置
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          retrievePasswordApi(this.loginForm)
            .then((res) => {
              if (res.code == 200) {
                window.location.href = workbenchUrl + "/#/login" + logOutPage();
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch((res) => {
              this.$message.error(
                res?.msg || i18n.t("receipt.networkError")
              );
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.select-bu-dialog {
  :deep(.el-radio-group) {
    display: block;
    height: 204px;
    overflow-y: auto;
  }

  :deep(.el-radio) {
    display: block;
    margin-bottom: 16px;
  }

  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
  }

  :deep(.el-radio__label) {
    font-size: 14px;
    color: #666666;
  }
}
</style>
