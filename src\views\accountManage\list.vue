<template>
  <div
    class="p24 bgc_white"
    v-loading="loading"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <!-- 筛选 -->
    <div class="flex justify_content_between">
      <search-box
        :modelValue="filterObj.searchStr"
        :placeholder="$t('receipt.inputAccountPhone')"
        @onInput="(value) => (filterObj.searchStr = value)"
        @onSearch="
          pageObj.pageNo = 1;
          getList();
        "
        @onClear="
          pageObj.pageNo = 1;
          filterObj.searchStr = '';
          getList();
        "
      ></search-box>

      <div class="flex_shrink0">
        <el-button
          type="primary"
          @click="
            operateRow = {};
            operateDialog = 'addDrawer';
          "
          ><i class="iconfont-flb icon-add mr2"></i
          >{{ $t("receipt.addAccount") }}</el-button
        >
      </div>
    </div>

    <!-- 表格 -->
    <div class="mt16">
      <el-table
        :data="tableData"
        size="small"
        border
        :height="tableHeight"
        class="custom_radius_table"
        style="width: 100%"
      >
        <el-table-column
          prop="name"
          :label="$t('receipt.employeeName')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="c_1D2330 fw600">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="account"
          :label="$t('receipt.account')"
          show-overflow-tooltip
        />
        <el-table-column
          prop="email"
          :label="$t('receipt.email')"
          show-overflow-tooltip
        />
        <el-table-column
          prop="phone"
          :label="$t('receipt.phone')"
          show-overflow-tooltip
        />
        <el-table-column
          prop="roleTitle"
          :label="$t('receipt.role')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.roleTitle?.join("、") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="storeIds"
          :label="$t('receipt.storeManage')"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="!scope.row.havePowerStoreNumber">-</span>
            <span
              v-else
              class="flex align_items_center pointer c_415FFF"
              @click="operate('detail', scope.row)"
            >
              {{ scope.row?.havePowerStoreNumber||0 }}
              <i
                class="iconfont-flb icon-sanjiaoxing ml2 c_415FFF"
                style="font-size: 6px"
              ></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="140"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="
                operateRow = scope.row;
                operateDialog = 'addDrawer';
              "
              >{{ $t("receipt.edit") }}</el-button
            >
            <el-divider direction="vertical" class="ml12 mr12"></el-divider>
            <el-button
              link
              type="danger"
              @click="
                operateRow = scope.row;
                operateDialog = 'delDialog';
                showDialog = true;
              "
              >{{ $t("receipt.delete") }}</el-button
            >
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>
    </div>

    <!-- 删除 -->
    <del-dialog
      v-if="operateDialog === 'delDialog'"
      :content="$t('receipt.delAccountConfirm')"
      v-model:show="showDialog"
      @confirm="onDel"
      @close="operateDialog = ''"
    ></del-dialog>

    <!-- 新增 -->
    <add-drawer
      v-if="operateDialog === 'addDrawer'"
      :operateRow="operateRow"
      @refresh="getList"
      @close="operateDialog = ''"
    ></add-drawer>

    <!-- 门店列表详情 -->
    <detail-drawer v-model:show="operateShow" :data="operateRow" isAll />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref } from "vue";
import searchBox from "@/components/filter/search.vue";
import { ElMessage } from "element-plus";
import delDialog from "@/components/dialog/delete.vue";
import emptyBox from "@/components/empty/index.vue";
import {
  delHrUserApi,
  delUserApi,
  getUserInfoApi,
  getUserPageApi,
} from "@/api/accountManage";
import store from "@/store";
import addDrawer from "./components/add-drawer.vue";
import website from "@/config/website";
import lang from "@/lang/index";
import detailDrawer from "./components/detail-drawer.vue";

const i18n = lang.global;
const filterObj = reactive({
  searchStr: "",
});
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = ref("");
const operateShow = ref(false);
const operateRow: any = ref({});
const pageObj = {
  pageNo: 1,
  pageSize: 20,
  total: 0,
};
const showDialog = ref(false);
const userInfo = computed(() => {
  return store.getters.userInfo;
});

onMounted(() => {
  window.addEventListener("resize", getTableHeight);
  getTableHeight();

  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

// 计算表格高度
const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 144;
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = {
    moduleId: website.moduleId,
    buIds: [userInfo.value.buId],
    page: pageObj.pageNo,
    pageSize: pageObj.pageSize,
    searchStr: filterObj.searchStr,
  };
  getUserPageApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data?.records || [];
        getFlbUser(list);
        pageObj.total = Number(res.data?.total || 0);
      } else {
        ElMessage.error(res.msg);
        loading.value = false;
      }
    })
    .catch(() => {
      loading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
// 获取菲律宾用户信息
const getFlbUser = (hrList) => {
  const ids = hrList.map((el) => el.userId);
  if (!ids || !ids.length) {
    tableData.value = [];
    loading.value = false;
    return;
  }
  const params = {
    moduleId: website.moduleId,
    sysUserIdList: ids,
  };
  getUserInfoApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        hrList.forEach((hEl) => {
          hEl.warehouseList = [];
          hEl.warehouseRegions = [];
          hEl.costPermissions = "NO";
          hEl.moduleId = website.moduleId;
          list.forEach((fEl) => {
            if (hEl.userId === fEl.sysUserId) {
              hEl.havePowerStoreNumber = fEl.havePowerStoreNumber;
              hEl.storeCustomers = fEl.storeCustomers || [];
              hEl.storeList = fEl.storeList || [];
              hEl.storeIds = fEl.storeIds || [];
              hEl.havePowerNames = fEl.havePowerNames || "";
              hEl.costPermissions = fEl.costPermissions?.value || "NO";
              hEl.id = fEl.id;
            }
          });
        });
        tableData.value = hrList;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 删除
const onDel = () => {
  // 删除中台用户
  const params = {
    buId: operateRow.value.buId,
    moduleId: website.moduleId,
    userId: operateRow.value.userId,
  };
  delHrUserApi(params)
    .then((res) => {
      if (res.code == 200) {
        delFlb();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
// 删除菲律宾用户
const delFlb = () => {
  if (!operateRow.value.id) {
    getList();
    return;
  }
  delUserApi(operateRow.value.id)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        getList();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

const operate = (type, row) => {
  switch (type) {
    case "detail":
      operateRow.value = row;
      operateShow.value = true;
      break;
    default:
      break;
  }
};
</script>

<style lang="scss" scoped></style>
