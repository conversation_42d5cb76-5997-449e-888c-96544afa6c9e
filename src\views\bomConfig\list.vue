<template>
  <div
    v-loading="loading"
    class="p24 bgc_white position_re"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <!-- 筛选 -->
    <div class="flex justify_content_between">
      <div class="flex_shrink0">
        <el-button
          type="primary"
          @click="
            operate('addDialog', {
              data: tableData[tableData.length - 1] || {},
            })
          "
        >
          <i class="iconfont-flb icon-add mr4" style="font-size: 12px"></i>
          {{ $t("receipt.addTo") }}
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="mt16" ref="tableRef">
      <el-table
        :data="tableData"
        :height="tableHeight"
        border
        class="custom_radius_table"
        size="small"
        style="width: 100%"
      >
        <!-- 配方名称 -->
        <el-table-column
          :label="$t('receipt.bomName')"
          min-width="400"
          prop="name"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 main-color">
              {{ scope.row?.name?.value || "-" }}
            </span>
          </template>
        </el-table-column>
        <!-- 商品 -->
        <el-table-column
          :label="$t('receipt.commodity')"
          min-width="635"
          prop="productGroupId"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="110"
          class-name="operate_column"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="
                operate('sortDialog', {
                  data: scope.row,
                })
              "
            >
              {{ $t("receipt.edit") }}
            </el-button>
            <el-divider class="ml8 mr8" direction="vertical"></el-divider>
            <el-button
              link
              type="danger"
              @click="
                operate('delDialog', {
                  data: scope.row,
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <pagination
        :showPage="false"
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>

    <!-- 删除 -->
    <del-dialog
      v-if="operateDialog.type === 'delDialog'"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
      @close="operateDialog.show = false"
    ></del-dialog>

    <!-- 新增编辑 -->
    <add-draw
      v-if="operateDialog.type === 'addDialog'"
      :data="operateDialog.data"
      v-model:show="operateDialog.show"
      @close="operateDialog.show = false"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  nextTick,
  onActivated,
  onMounted,
  onUnmounted,
  reactive,
  ref,
} from "vue";
import { ElMessage } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import pagination from "@/components/pagination/index.vue";
import { tableFormat } from "@/util/util";
import lang from "@/lang/index";
import { debounce } from "lodash";
import router from "@/router";
import { delProductGroup, getProductGroupList } from "@/api/naviConfig";
import delDialog from "@/components/dialog/delete.vue";
import addDraw from "./components/add-draw.vue";

const i18n = lang.global;
const tableRef = ref(null);
const filterObj = reactive({
  searchStr: "",
  orderDate: "",
  examineState: "",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {} as any,
  title: "",
  content: "",
});

onActivated(() => {
  getList();
});

onMounted(() => {
  window.addEventListener("resize", debounce(getTableHeight, 500));
  getTableHeight();
  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", debounce(getTableHeight, 500));
});

const getTableHeight = () => {
  nextTick(() => {
    if (tableRef.value) {
      const wrapperEl = tableRef.value; // 获取封装层容器元素
      const wrapperHeight = wrapperEl.parentElement.clientHeight; // 获取封装层的高度
      // 遍历封装层的子元素，累加非 tableRef 元素的高度
      let occupiedHeight = 0;
      Array.from(wrapperEl.parentElement.children).forEach((child: any) => {
        if (child !== wrapperEl) {
          // 排除 tableRef 元素本身
          occupiedHeight += child.getBoundingClientRect().height;
        }
      });
      // 计算表格的可用高度
      tableHeight.value = wrapperHeight - occupiedHeight - 104;
    }
  }); // 在延迟后执行原始函数
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, filterObj) as any;
  getProductGroupList(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        pageObj.total = Number(res.data.total || 0);
        tableData.value = list;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};

// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

const operate = (type, val: any = {}) => {
  switch (type) {
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = val.data || {};
      operateDialog.show = true;
      break;
  }
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "delDialog") {
    delProductGroup({ id: data.id })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};
</script>

<style lang="scss" scoped></style>
