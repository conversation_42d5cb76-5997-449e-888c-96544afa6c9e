<template>
  <div class="menu-wrapper">
    <template v-for="item in menu">
      <el-menu-item v-if="validatenull(item[childrenKey]) && vaildRoles(item)"
                    :index="item[pathKey]"
                    @click="open(item)"
                    :key="item[labelKey]"
                    :class="{ 'is-active': vaildAvtive(item), 'no-children-item': validatenull(item[childrenKey]) }">
        <i class="iconfont-flb" 
           :class="item[iconKey]"></i>
        <template #title>
          <span :alt="item[pathKey]">
            <em :title="generateTitle(item)">{{ generateTitle(item) }}</em>
          </span>
        </template>

      </el-menu-item>

      <el-sub-menu v-else-if="!validatenull(item[childrenKey]) && vaildRoles(item)"
                  :index="item[pathKey]"
                  :key="item[labelKey]">
        <template v-slot:title>
          <i class="iconfont-flb" 
             :class="item[iconKey]"></i>
          <span slot="title"
                :class="{ 'el-menu--display': collapse && first }">
            <em :title="generateTitle(item)">{{ generateTitle(item) }}</em>
            <!-- {{generateTitle(item)}} -->
          </span>
        </template>

        <template v-for="(child, cindex) in item[childrenKey]">

          <el-menu-item :index="child[pathKey], cindex"
                        @click="open(child)"
                        :class="{ 'is-active': vaildAvtive(child) }"
                        v-if="validatenull(child[childrenKey])"
                        :key="child[labelKey]">
            <i class="iconfont-flb" 
               :class="child[iconKey]"></i>
            <span slot="title">
              <em :title="generateTitle(child)">{{ generateTitle(child) }}</em>
            </span>
          </el-menu-item>

          <sidebar-item v-else
                        :menu="[child]"
                        :key="cindex"
                        :props="props"
                        :screen="screen"
                        :collapse="collapse">
          </sidebar-item>

        </template>

      </el-sub-menu>
    </template>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { validatenull } from "@/util/validate";
import { getPath } from "@/util/util"
import config from "./config.js";
export default {
  name: "sidebarItem",
  data () {
    return {
      config: config,
    };
  },
  props: {
    menu: {
      type: Array,
    },
    screen: {
      type: Number,
    },
    first: {
      type: Boolean,
      default: false,
    },
    props: {
      type: Object,
      default: () => {
        return {};
      },
    },
    collapse: {
      type: Boolean,
    },
  },
  created () { },
  mounted () { },
  computed: {
    ...mapGetters(["roles"]),
    labelKey () {
      return this.props.label || this.config.propsDefault.label;
    },
    pathKey () {
      return this.props.path || this.config.propsDefault.path;
    },
    iconKey () {
      return this.props.icon || this.config.propsDefault.icon;
    },
    childrenKey () {
      return this.props.children || this.config.propsDefault.children;
    },
    nowTagValue () {
      return this.$router.$avueRouter.getValue(this.$route);
    },
    langKey () {
      return this.props.langKey || this.config.propsDefault.langKey;
    },
  },
  methods: {
    generateTitle (item) {
      return this.$router.$avueRouter.generateTitle(
        item[this.labelKey],
        item[this.langKey]
        // (item.meta || {}).i18n
      );
    },
    vaildAvtive (item) {
      const groupFlag = (item["group"] || []).some((ele) =>
        this.$route.path.includes(ele)
      );
      return this.nowTagValue === item[this.pathKey] || groupFlag;
    },
    vaildRoles (item) {
      item.meta = item.meta || {};
      return item.meta.roles ? item.meta.roles.includes(this.roles) : true;
    },
    validatenull (val) {
      return validatenull(val);
    },
    open (item) {
      if (this.screen <= 1) this.$store.commit("common/SET_COLLAPSE");
      this.$router.$avueRouter.group = item.group;
      this.$router.$avueRouter.meta = item.meta;
      let query = { ...item.query }
      // query.t = Date.now()
      // this.$router.go(0)
      this.$router.push({
        path: getPath(
          {
            name: item[this.labelKey],
            src: item[this.pathKey],
          },
          item.meta
        ),
        query: query,

      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-menu {
  .svg-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: -7px;
  }

  .el-menu-item.is-active {
    color: #fff;

    .svg-icon {
      opacity: 1;
    }
  }
}
</style>

