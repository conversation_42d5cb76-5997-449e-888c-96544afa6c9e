{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": "./", "types": ["webpack-env", "element-plus/global"], "paths": {"@/*": ["src/*"], "@img/*": ["src/assets/img/*"], "@styl/*": ["src/assets/styl/*"], "@js/*": ["src/assets/js/*"], "@ts/*": ["src/assets/ts/*"], "@fonts/*": ["src/assets/fonts/*"], "@css/*": ["src/assets/css/*"], "@libs/*": ["src/libs/*"], "@cp/*": ["src/components/*"], "@views/*": ["src/views/*"], "@plugins/*": ["src/plugins/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "typings", "src/api/axios.js", "src/api/axios.js"], "exclude": ["node_modules"]}