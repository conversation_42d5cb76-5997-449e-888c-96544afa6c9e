/*
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-04 15:46:26
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-21 11:11:23
 * @FilePath: \flb-receipt\src\util\numberUtil.ts
 * @Description: 计算工具
 */
import { getStore } from "./store";
import store from "@/store";
import NP from "number-precision";

// 初始化 Number-Precision
NP.enableBoundaryChecking(false); // 禁用边界检查

/**
 * 金额格式化,带单位——用于显示
 * @param {number} value 金额
 * @param {boolean} isComma 是否带逗号
 * @returns {string} 格式化的金额
 */
export const formatMoney = (
  value: number | string | null | undefined,
  isComma = false
): string => {
  // 如果为空字符串返回空字符串（排除0）
  if ((value || "").toString().trim() === "") {
    value = 0;
  }
  value = Number(value || 0);
  // const language = getStore({ name: "language" }) || "zh";
  // USD 美元
  // EUR 欧元
  // CNY 人民币
  if (isComma) {
    return (
      (store.getters.currencyInfo?.unit || "") +
      "" +
      round(value, store.getters.currencyInfo?.decimalPlace).toFixed(
        store.getters.currencyInfo?.decimalPlace
      )
    ).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  } else {
    return (
      (store.getters.currencyInfo?.unit || "") +
      "" +
      round(value, store.getters.currencyInfo?.decimalPlace).toFixed(
        store.getters.currencyInfo?.decimalPlace
      )
    );
  }
  // return value.toLocaleString(language, { style: "currency", currency: "CNY" });
};

/**
 * 将数字转换为中文表示。
 *
 * @param {number} num - 要转换的数字。
 * @return {string} 数字的中文表示。
 */
export const numberToZh = (num: number): string => {
  if (num !== 0 && !num) {
    return "";
  }
  return num.toLocaleString("zh-u-nu-hanidec"); // "一"
};

/**
 * 四舍五入到指定小数位——用于计算
 * @param {number} num 数字
 * @param {number} decimalPlaces 小数位数
 * @returns {number} 结果
 */
export const round = (
  num: number,
  decimalPlaces = store.getters.currencyInfo?.decimalPlace
): number => {
  return NP.round(num, decimalPlaces);
};

/**
 * 精确加法
 * @param {number[]} nums 数字数组
 * @returns {number} 结果
 */
export const add = (...nums: number[]): number => {
  return NP.plus(...nums);
};

/**
 * 精确减法
 * @param {number} num1 被减数
 * @param {number} num2 减数
 * @returns {number} 结果
 */
export const subtract = (num1: number, num2: number): number => {
  return NP.minus(num1, num2);
};

/**
 * 精确乘法
 * @param {number[]} nums 数字数组
 * @returns {number} 结果
 */
export const multiply = (...nums: number[]): number => {
  return NP.times(...nums);
};

/**
 * 精确除法
 * @param {number} num1 被除数
 * @param {number} num2 除数
 * @returns {number} 结果
 */
export const divide = (num1: number, num2: number): number => {
  return NP.divide(num1, num2);
};

/**
 * 获取小数位数的最小步进值
 * @param {number} decimalPlaces 小数位数
 * @returns {number} 步进值
 */
export const getStep = (
  decimalPlaces = store.getters.currencyInfo?.decimalPlace
): number => {
  return 1 / Math.pow(10, decimalPlaces);
};
