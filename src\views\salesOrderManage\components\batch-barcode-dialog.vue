<!--
 * @Author: SPANULL <EMAIL>
 * @Date: 2024-12-02 14:49:28
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-06-16 17:41:05
 * @FilePath: \flb-receipt\src\views\salesOrderManage\components\audit-dialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="$t('receipt.barcodeIn')"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="400px"
  >
    <el-form ref="formRef" :model="form" label-position="top">
      <el-form-item
        prop="text"
        :rules="[
          {
            required: true,
            message: $t('receipt.pleaseEnter'),
            trigger: 'blur',
          },
        ]"
      >
        <template #label>
          <span class="fs12 c_1D2330 lh18 fw400">{{
            $t("receipt.serialCodeM")
          }}</span>
        </template>
        <el-input
          v-model="form.text"
          :placeholder="$t('receipt.pleaseEnter')"
          @keydown.enter="inputChange"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm" :loading="loading">
        {{ $t("receipt.submit") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { ref, defineEmits, defineProps, watch } from "vue";
import lang from "@/lang/index";
import {
  countPriceBindDiscount,
  querySkuInfoByBarcodeList,
} from "@/api/salesOrderManage";
import { multiply, round } from "@/util/numberUtil";
const i18n = lang.global;

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  storeId: {
    type: [String, Number],
    required: true,
  },
  productList: {
    type: Array,
    default: () => [],
  },
  secondList: {
    type: Array,
    default: () => [],
  },
});
const form = ref({
  text: "",
});
const formRef = ref();
const loading = ref(false);

watch(
  () => props.show,
  (val) => {
    if (val) {
      formRef.value.resetFields();
    }
  }
);

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    loading.value = true;
    querySkuInfoByBarcodeList({
      barcodeList: form.value.text.split(",").map((item) => item.trim()),
      storeId: props.storeId,
    })
      .then((res) => {
        if (res.code === 200) {
          const list = (res.data || []).map((item) => ({
            ...item,
            id: item.skuId,
            quantity: 1,
            barcodeList: [
              {
                barcode: item.barcode,
                stockType: item.stockType?.value,
                skuId: item.skuId,
              },
            ],
          }));

          // 把skuId相同的合并
          const skuMap = new Map();
          const secondMap = new Map();

          (props.productList || []).forEach((item: any) => {
            if (skuMap.has(item.skuId)) {
              const existingItem = skuMap.get(item.skuId);
              existingItem.quantity += item.quantity;
              // barcodeList根据barcode去重
              if (
                !existingItem.barcodeList.some(
                  (barcode) => barcode.barcode === item.barcode
                )
              ) {
                existingItem.barcodeList.push(...item.barcodeList);
              }
            } else {
              skuMap.set(item.skuId, item);
            }
          });

          (props.secondList || []).forEach((item: any) => {
            if (secondMap.has(item.skuId)) {
              const existingItem = secondMap.get(item.skuId);
              existingItem.quantity += item.quantity;
              // barcodeList根据barcode去重
              if (
                !existingItem.barcodeList.some(
                  (barcode) => barcode.barcode === item.barcode
                )
              ) {
                existingItem.barcodeList.push(...item.barcodeList);
              }
            } else {
              secondMap.set(item.skuId, item);
            }
          });

          const noSecondPrice = [];
          list.forEach((item) => {
            if (item.stockType?.value === "SECOND_HAND") {
              if (secondMap.has(item.id)) {
                const existingItem = secondMap.get(item.id);
                existingItem.quantity += item.quantity;
                // barcodeList根据barcode去重
                if (
                  !existingItem.barcodeList.some(
                    (barcode) => barcode.barcode === item.barcode
                  )
                ) {
                  existingItem.barcodeList.push(...item.barcodeList);
                }
              } else {
                // 二手价判断
                let price = item.price || 0;
                try {
                  price = Number(JSON.parse(item.otherPrice)?.SECOND_PRICE);
                  if (!price) {
                    noSecondPrice.push(item.skuCode || item.productCode);
                  }
                } catch (e) {
                  noSecondPrice.push(item.skuCode || item.productCode);
                }
                item.price = price;

                secondMap.set(item.id, item);
              }
            } else {
              if (skuMap.has(item.id)) {
                const existingItem = skuMap.get(item.id);
                existingItem.quantity += item.quantity;
                // barcodeList根据barcode去重
                if (
                  !existingItem.barcodeList.some(
                    (barcode) => barcode.barcode === item.barcode
                  )
                ) {
                  existingItem.barcodeList.push(...item.barcodeList);
                }
              } else {
                skuMap.set(item.id, item);
              }
            }
          });

          // 转换回数组
          const skuList = Array.from(skuMap.values());
          const sencondList = Array.from(secondMap.values());

          const isInThisStore = list.filter(
            (item) => item.isInThisStore?.value === "NO"
          );
          const isSaleBarcode = list.filter(
            (item) => item.isSaleBarcode?.value === "YES"
          );

          if (noSecondPrice.length) {
            ElMessage.warning(
              i18n.t("receipt.batchBarcodeTip3", {
                code: noSecondPrice.join(","),
              })
            );
            loading.value = false;
            return;
          }

          if (isInThisStore.length) {
            ElMessage.warning(
              i18n.t("receipt.batchBarcodeTip1", {
                code: isInThisStore.map((item) => item.barcode).join(","),
              })
            );
            loading.value = false;
            return;
          }
          if (isSaleBarcode.length) {
            ElMessage.warning(
              i18n.t("receipt.batchBarcodeTip2", {
                code: isSaleBarcode.map((item) => item.barcode).join(","),
              })
            );
            loading.value = false;
            return;
          }
          emit("confirm", {
            skuList: skuList.map((el) => ({
              ...el,
              stockType: el.stockType?.value || el.stockType,
            })),
            secondList: sencondList.map((el) => {
              return {
                ...el,
                stockType: el.stockType?.value || el.stockType,
                id: el.id || el.skuId, // 确保id存在
                skuId: el.skuId || el.id,
                skuCode: el.skuCode || el.productCode,
                skuName: el.skuName || el.productName,
                isBarcode: el.isBarcode,
                unitId: el.unitId,
                unitName: el.unitName,
                quantity:
                  el.quantity ||
                  (el.isBarcode?.value === "YES" || !getQuantity(el) ? 0 : 1), //货品数量
                referenceUnitPrice: round(
                  Number(
                    el.referenceUnitPrice ||
                      el.originalUnitPrice ||
                      el.price ||
                      0
                  )
                ), //货品原价
                originalUnitPrice: round(
                  Number(el.originalUnitPrice || el.price || 0)
                ), //货品原价
                discountUnitPrice: round(
                  Number(el.discountUnitPrice || el.price || 0)
                ), //货品优惠后价格

                originalTotalPrice: round(
                  Number(
                    el.originalTotalPrice ||
                      multiply(
                        Number(el.price),
                        Number(
                          el.quantity ||
                            (el.isBarcode?.value === "YES" || !getQuantity(el)
                              ? 0
                              : 1)
                        )
                      ) ||
                      0
                  )
                ), //货品优惠前总价
                discountTotalPrice: round(
                  Number(
                    el.discountTotalPrice ||
                      multiply(
                        Number(el.price),
                        Number(
                          el.quantity ||
                            (el.isBarcode?.value === "YES" || !getQuantity(el)
                              ? 0
                              : 1)
                        )
                      ) ||
                      0
                  )
                ), //货品优惠后总价
                stockQuantity: el.stockQuantity, //库存
              };
            }),
          });
          emit("update:show", false);
        } else {
          ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
        }
        loading.value = false;
      })
      .catch((error) => {
        console.log(error, "error");

        ElMessage.error(error?.msg || i18n.t("receipt.networkError"));
        loading.value = false;
      });
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};

// 回车增加逗号
const inputChange = () => {
  form.value.text += ",";
};

// 获取库存
function getQuantity(row: any) {
  if (row.isBarcode?.value === "YES") {
    if (row.stockType !== "SECOND_HAND") {
      return row.newQuantity || 0;
    } else {
      return row.secondHandQuantity || 0;
    }
  } else {
    return row.stockQuantity;
  }
}
</script>

<style lang="scss" scoped></style>
