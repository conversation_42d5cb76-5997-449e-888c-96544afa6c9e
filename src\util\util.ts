/* eslint-disable @typescript-eslint/no-explicit-any */
import i18n from "@/lang";
import { useI18n } from "vue-i18n";
import store from "../store";
import { getLangDataApi } from "@/api/common";
import { languageNodeId } from "@/config/env";
import { createVNode, render } from "vue";
import { ElImageViewer } from "element-plus";

//表单序列化
export const serialize = (data: any) => {
  const list: any[] = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join("&");
};

export const getObjType = (obj: any) => {
  const toString = Object.prototype.toString;
  const map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object",
  };
  if (obj instanceof Element) {
    return "element";
  }
  return (map as any)[toString.call(obj)];
};

/**
 * 对象深拷贝
 */
export const deepClone = (data: any) => {
  const type = getObjType(data);
  let obj;
  if (type === "array") {
    obj = [];
  } else if (type === "object") {
    obj = {};
  } else {
    //不再具有下一层次
    return data;
  }
  if (type === "array") {
    for (let i = 0, len = data.length; i < len; i++) {
      obj.push!(deepClone(data[i]));
    }
  } else if (type === "object") {
    // const Data: any = data;
    for (const key in data) {
      (obj as any)[key] = deepClone(data[key]);
    }
  }
  return obj;
};

/**
 * 设置灰度模式
 */
export const toggleGrayMode = (status: any) => {
  if (status) {
    document.body.classList.add("grayMode");
  } else {
    document.body.classList.remove("grayMode");
  }
};

/**
 * 设置主题
 */
export const setTheme = (themeStr: string) => {
  /**基于乾坤沙箱隔离的修改*/
  document.body.classList.remove(store.getters.themeName);
  store.commit("common/SET_THEME_NAME", themeStr, { root: true });
  document.body.classList.add(themeStr);
};

/**
 * 设置语言
 */
export const setLang = (language: any, langIndex: string | number) => {
  /**基于乾坤沙箱隔离的修改*/
  document.body.classList.remove("lang-" + store.getters.language);
  // i18n.global.locale = language;
  store.commit("common/SET_LANGUAGE", language, { root: true });
  store.commit("common/SET_LANGUAGEINDEX", langIndex, { root: true });
  document.body.classList.add("lang-" + language);

  return new Promise((resolve, reject) => {
    getLangDataApi({ nodeId: languageNodeId })
      .then((res) => {
        if (res.code == 200) {
          const langData = res.data;
          const langJson = {};
          getLangDataJson(langData, langJson, language);

          //设置多语言
          let elLangKey = "en";
          if (language == "zh") elLangKey = "zh-cn";
          const resLangJson: any = {
            ...langJson,
            ...require(`element-plus/dist/locale/${elLangKey}`).default,
          };
          i18n.global.setLocaleMessage(language, resLangJson);
          i18n.global.locale.value = language;
        }
      })
      .finally(() => {
        resolve(language);
      });
  });
};

//获取多语言
const getLangData = (langKey) => {
  getLangDataApi({ nodeId: languageNodeId }).then((res) => {
    if (res.code == 200) {
      const langData = res.data;
      const langJson = {};
      getLangDataJson(langData, langJson, langKey);

      //设置多语言
      let elLangKey = "en";
      if (langKey == "zh") elLangKey = "zh-cn";
      const resLangJson: any = {
        ...langJson,
        ...require(`element-plus/dist/locale/${elLangKey}`).default,
      };
      i18n.global.setLocaleMessage(langKey, resLangJson);
      i18n.global.locale.value = langKey;
    }
  });
};

//根据后端返回的数据转换多语言的json数据
const getLangDataJson = (langData, langJson, langKey) => {
  langData.forEach((item) => {
    const filedNameList = item.languageConfigPageReqVOList || [];
    const filedNameJson = {};
    filedNameList.forEach((fieldItem) => {
      if (langKey)
        filedNameJson[fieldItem.filedName] = fieldItem[`${langKey}Name`];
    });
    if (item.code) {
      langJson[item.code] = filedNameJson;
    } else {
      langJson = { ...filedNameJson };
    }
    if (item.children && item.children.length) {
      getLangDataJson(item.children, langJson[item.code], langKey);
    }
  });
};

/**
 * esc监听全屏
 */
export const listenfullscreen = (callback: () => void) => {
  function listen() {
    callback();
  }

  document.addEventListener("fullscreenchange", function () {
    listen();
  });
  document.addEventListener("mozfullscreenchange", function () {
    listen();
  });
  document.addEventListener("webkitfullscreenchange", function () {
    listen();
  });
  document.addEventListener("msfullscreenchange", function () {
    listen();
  });
};

/**
 * 递归寻找子类的父类
 */

export const findParent = (menu: any, id: string | number): any => {
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].children.length != 0) {
      for (let j = 0; j < menu[i].children.length; j++) {
        if (menu[i].children[j].id == id) {
          return menu[i];
        } else {
          if (menu[i].children[j].children.length != 0) {
            return findParent(menu[i].children[j].children, id);
          }
        }
      }
    }
  }
};

/**
 * 动态插入css
 */
export const loadStyle = (url: string) => {
  const link = document.createElement("link");
  link.type = "text/css";
  link.rel = "stylesheet";
  link.href = url;
  const head = document.getElementsByTagName("head")[0];
  head.appendChild(link);
};

/**
 * 判断路由是否相等
 */
export const diff = (obj1: any, obj2: any): boolean => {
  delete obj1.close;
  const o1 = obj1 instanceof Object;
  const o2 = obj2 instanceof Object;
  if (!o1 || !o2) {
    /*  判断不是对象  */
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
    //Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.keys(arr))->0,1,2;
  }
  for (const attr in obj1) {
    const t1 = obj1[attr] instanceof Object;
    const t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
};

/**
 * 生成随机len位数字
 */
export const randomLenNum = (len?: any, date?: any) => {
  let random = "";
  random = Math.ceil(Math.random() * 100000000000000)
    .toString()
    .substr(0, len ? len : 4);
  if (date) random = random + Date.now();
  return random;
};

/**
 * 下载文件流格式的Excel
 */
const MIMEType = {
  xlsx: "application/vnd.ms-excel",
  pdf: "application/pdf",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  doc: "application/msword",
  zip: "application/zip",
  rar: "application/x-rar-compressed",
  "7z": "application/x-7z-compressed",
};
export const downloadExcel = (
  filestream: any,
  fileName: any,
  type = "xlsx"
) => {
  const exportBlob = new Blob([filestream], {
    // type: 'application/vnd.ms-excel'
    type: MIMEType[type],
  });
  const exportUrl = URL.createObjectURL(exportBlob);
  const exportElement = document.createElement("a");
  exportElement.setAttribute("href", exportUrl);
  exportElement.setAttribute("download", fileName);
  exportElement.style.display = "none";
  document.body.appendChild(exportElement);
  exportElement.click();
  document.body.removeChild(exportElement);
};

export const createBlob = (filestream: any, fileName: any, type = "xlsx") => {
  const exportBlob = new Blob([filestream], {
    // type: 'application/vnd.ms-excel'
    type: MIMEType[type],
  });
  const exportUrl = URL.createObjectURL(exportBlob);
  return exportUrl;
};

// 图片base64部分转换成文件
export const dataURLtoFile = (dataurl, filename) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};

/**
 * 移出树结构中的空children
 */
export const removeEmptyChildren = (node: any) => {
  node.forEach((item: any) => {
    if ("children" in item && item.children.length === 0) {
      delete item.children;
    } else if ("children" in item && item.children.length) {
      removeEmptyChildren(item.children);
    }
  });
  return node;
};

/**
 * 树形结构搜寻节点
 */
export const getTreeNode = (data: any, id: any, key = "string") => {
  let hasFound = false, // 表示是否有找到id值
    result = null;
  const fn = function (data: any) {
    if (Array.isArray(data) && !hasFound) {
      // 判断是否是数组并且没有的情况下，
      data.forEach((item) => {
        if (item[key] === id) {
          // 数据循环每个子项，并且判断子项下边是否有id值
          result = item; // 返回的结果等于每一项
          hasFound = true; // 并且找到id值
        } else if (item.children) {
          fn(item.children); // 递归调用下边的子项
        }
      });
    }
  };
  fn(data); // 调用一下
  return result;
};

/**
 * 树形结构添加节点
 */
export const appendNodeInTree = (tree: any, code: string, obj: any) => {
  tree.forEach((ele: any) => {
    if (ele.code === code) {
      ele.children ? ele.children.push(obj) : (ele.children = [obj]);
    } else {
      if (ele.children) {
        appendNodeInTree(ele.children, code, obj);
      }
    }
  });
  return tree;
};

/**
 * 树形结构删除节点
 */
export const removeNodeInTree = (treeList: any, code: string) => {
  // 通过id从数组（树结构）中移除元素
  if (!treeList || !treeList.length) {
    return;
  }
  for (let i = 0; i < treeList.length; i++) {
    if (treeList[i].code === code) {
      treeList.splice(i, 1);
      break;
    }
    removeNodeInTree(treeList[i].children, code);
  }
};

/**
 * 数组内对象根据key值去重
 */
export const removeArrRepeat = (arr: any, key: any) => {
  const result = [];
  const obj = {};
  for (let i = 0; i < arr.length; i++) {
    if (!(obj as any)[arr[i][key]]) {
      result.push(arr[i]);
      (obj as any)[arr[i][key]] = true;
    }
  }
  return result;
};

/**
 * 获取treeselect组件选中值的名称
 */
export const getTreeLabel = (id: any) => {
  const labelList = [];
  const tree = document.getElementById(id);
  const labelDome = tree!.getElementsByClassName(
    "vue-treeselect__multi-value-label"
  );
  for (let i = 0; i < labelDome.length; i++) {
    labelList.push(labelDome[i].innerHTML);
  }
  return labelList;
};

/**
 *根据语言获取日期格式
 */
export const getFormatByLang = (type?: number, month?: any) => {
  let formatStr = "YYYY-MM-DD";

  if (month) {
    formatStr = "YYYY-MM";
  }
  if (store.getters.language == "vi") {
    //如果是越南语则反转时间
    formatStr = formatStr.split("-").reverse().join("-");
  }

  if (type == 1) {
    formatStr = `${formatStr} HH`; //年-月-日 时
  } else if (type == 2) {
    formatStr = `${formatStr} HH:mm`; //年-月-日 时:分
  } else if (type == 3) {
    formatStr = `${formatStr} HH:mm:ss`; //年-月-日 时:分:秒
  }

  return formatStr;
};

//设置标题
export const generateTitle = (item: any, key?: any) => {
  const i18nKey = key || item['langKey']
  // const { te, t } = useI18n();
  const { te, t } = i18n.global
  const hasKey = te("receipt." + i18nKey);
  if (hasKey) {
    const translatedTitle = t("receipt." + i18nKey);
    return translatedTitle;
  }
  return item.label;
};


//设置菜单标题
export const generateName = (item: any, key?: any) => {
  const i18nKey = key || "langKey";
  const { te, t } = useI18n();
  const hasKey = te("receipt." + item[i18nKey]);
  if (hasKey) {
    const translatedTitle = t("receipt." + item[i18nKey]);
    return translatedTitle;
  }
  return item.label;
};

//处理路由
export const getPath = (params: any = {}, meta = {}) => {
  const { src } = params;
  const result = src || "/";
  return result;
};

//设置路由值
export const getValue = (route: { query: { src: string }; path: string }) => {
  let value = "";
  if (route.query.src) {
    value = route.query.src;
  } else {
    value = route.path;
  }
  return value;
};

// 获取当前时间 并格式化为yyyy-MM-dd HH:mm:ss
export const getFormatDate = (type) => {
  const date = new Date();
  let month: number | string = date.getMonth() + 1;
  let strDate: number | string = date.getDate();
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }

  let hours: number | string = date.getHours();
  if (hours >= 0 && hours <= 9) {
    hours = "0" + hours;
  }

  let minutes: number | string = date.getMinutes();
  if (minutes >= 0 && minutes <= 9) {
    minutes = "0" + minutes;
  }

  let seconds: number | string = date.getSeconds();
  if (seconds >= 0 && seconds <= 9) {
    seconds = "0" + seconds;
  }
  let currentDate = "";
  if (type) {
    currentDate =
      date.getFullYear().toString() +
      month +
      strDate +
      hours +
      minutes +
      seconds;
  } else {
    currentDate =
      date.getFullYear() +
      "-" +
      month +
      "-" +
      strDate +
      " " +
      hours +
      ":" +
      minutes +
      ":" +
      seconds;
  }
  return currentDate;
};

//找到store中的tag标签
function findTag(value: string, list: any[]) {
  let tag, key;
  list.map((item, index) => {
    if (item.value === value) {
      tag = item;
      key = index;
    }
  });
  return { tag: tag, key: key };
}

//关闭当前标签页
export const closeTag = (value: any, router: { path: any }[]) => {
  const tagList = store.getters.tagList;
  // eslint-disable-next-line prefer-const
  let { tag, key }: { tag: any; key: any } = findTag(value, tagList);
  store.commit("tags/DEL_TAG", tag);
  if (key) {
    tag = tagList[key === 0 ? key : key - 1];
  }
  router.push({
    path: tag.value,
  });
};

/**
 * 表格中对数据进行格式化
 * 如果类型是字符串且为空返回 -
 * @param {any} value 需要格式化的数据
 * @returns {any} 格式化后的数据
 */
export const tableFormat = (
  row: any,
  column: any,
  cellValue: any,
  index: number
) => {
  if (
    (typeof cellValue === "string" ||
      cellValue === null ||
      cellValue === undefined) &&
    !cellValue
  )
    return "-";
  return cellValue;
};

export const textFormat = (text: any) => {
  if (
    (typeof text === "string" || text === null || text === undefined) &&
    !text
  )
    return "-";
  return text;
};

/**
 * 限制数字的格式:
 * 1. 仅保留有效数字和小数点
 * 2. 防止多个小数点或负号
 * 3. 限制小数点后两位
 * @param {string|number} val 需要格式化的数字
 * @returns {number} 格式化后的数字
 */
export const digitFormat = (
  val: string | number,
  decimalPlaces = store.getters.currencyInfo?.decimalPlace
) => {
  let value = val + "";

  // 如果用户只输入了一个小数点，保留原样
  if (value === ".") {
    return "0.";
  }

  // 防止非数字、小数点或负号的字符输入
  value = value.replace(/[^0-9.-]/g, "");

  // 处理负号：负号只能出现在开头，且只能有一个
  if ((value.match(/-/g) || []).length > 1) {
    value = value.replace(/(?!^)-/g, "");
  }

  // 防止多个小数点：保留第一个小数点，移除其余的
  if ((value.match(/\./g) || []).length > 1) {
    const firstDotIndex = value.indexOf(".");
    value =
      value.slice(0, firstDotIndex + 1) +
      value.slice(firstDotIndex + 1).replace(/\./g, "");
  }

  // 限制小数点后最多两位，但不要移除末尾小数点
  if (value.includes(".")) {
    const [intPart, decPart] = value.split(".");
    if (decimalPlaces == 0) {
      value = intPart;
    } else {
      value = decPart
        ? `${intPart}.${decPart.slice(0, decimalPlaces)}`
        : `${intPart}.`;
    }
  }

  return value;
};

/**
 * input格式化，移除非数字字符，并将值限制在指定范围内。
 *
 * @param {string|number} val - 要格式化的数字字符串。
 * @param {number|null} min - 允许的最小值。默认为 null。
 * @param {number|null} max - 允许的最大值。默认为 null。
 * @returns {string} 格式化后的数字字符串。
 */
export const numberFormat = (
  val: string | number,
  min: number | null = null,
  max: number | null = null
) => {
  let value = val + "";

  // 移除非数字和负号的字符
  value = value.replace(/[^0-9-]/g, "");

  // 确保负号只能在开头，且不能重复
  if (
    (value.match(/-/g) || []).length > 1 ||
    (value.includes("-") && value[0] !== "-")
  ) {
    value = value.replace(/(?!^)-/g, "");
  }

  if (value === "") {
    return "";
  }

  // 转换为整数（如果输入为空，暂时保留为空字符串）
  let newValue = Number(value);

  // 检查范围限制
  if (max != null && newValue > max) newValue = max;
  if (min != null && newValue < min) newValue = min;

  return newValue.toString();
};

// 获取当前时间 并格式化yyyyMMddHHmmss
export const getFormatCurDate = () => {
  const date = new Date();
  let month: number | string = date.getMonth() + 1;
  let strDate: number | string = date.getDate();
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }

  let hours: number | string = date.getHours();
  if (hours >= 0 && hours <= 9) {
    hours = "0" + hours;
  }

  let minutes: number | string = date.getMinutes();
  if (minutes >= 0 && minutes <= 9) {
    minutes = "0" + minutes;
  }

  let seconds: number | string = date.getSeconds();
  if (seconds >= 0 && seconds <= 9) {
    seconds = "0" + seconds;
  }
  const currentDate = `${date.getFullYear()}${month}${strDate}${hours}${minutes}${seconds}`;

  return currentDate;
};

/**
 * 将一个数字转换为其中文表示。
 *
 * @param {number} num - 要转换的数字
 * @return {string} 数字的中文表示
 */
export function numberToChinese(num) {
  const unit = ["", "十", "百", "千"];
  const sectionUnit = ["", "万", "亿", "兆"];
  const digit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];

  if (typeof num !== "number" || isNaN(num)) {
    throw new Error("请输入一个有效的数字");
  }

  if (num === 0) return "零";

  let integerPart = Math.floor(Math.abs(num));
  const decimalPart = num.toString().split(".")[1];
  let chineseStr = "";

  // 处理整数部分
  let sectionCount = 0;
  while (integerPart > 0) {
    let section = integerPart % 10000;
    let sectionChinese = "";
    let zeroFlag = false;

    for (let i = 0; i < 4; i++) {
      const digitValue = section % 10;
      if (digitValue === 0) {
        zeroFlag = true;
      } else {
        if (zeroFlag) {
          sectionChinese = digit[0] + sectionChinese;
          zeroFlag = false;
        }
        sectionChinese = digit[digitValue] + unit[i] + sectionChinese;
      }
      section = Math.floor(section / 10);
    }

    if (sectionChinese !== "") {
      sectionChinese += sectionUnit[sectionCount];
    }
    chineseStr = sectionChinese + chineseStr;

    sectionCount++;
    integerPart = Math.floor(integerPart / 10000);
  }

  chineseStr = chineseStr.replace(/零+/g, "零").replace(/零$/, "");

  // 处理小数部分
  if (decimalPart) {
    chineseStr += "点";
    for (const char of decimalPart) {
      chineseStr += digit[parseInt(char, 10)];
    }
  }

  // 处理负数
  if (num < 0) {
    chineseStr = "负" + chineseStr;
  }

  return chineseStr;
}

/**
 * 判断数据是否为空
 * @param {*} value - 需要判断的数据
 * @returns {Boolean} - 如果为空返回 true，否则返回 false
 */
export function isEmpty(value) {
  // 判断 null 和 undefined
  if (value === null || value === undefined) return true;

  // 判断数字：如果是数字，则只对 NaN 视为空
  if (typeof value === "number") {
    return isNaN(value);
  }

  // 判断字符串：如果字符串仅由空白字符组成或者为空字符串，则为空
  if (typeof value === "string") {
    return value.trim() === "";
  }

  // 判断数组：数组长度为 0 时为空
  if (Array.isArray(value)) {
    return value.length === 0;
  }

  // 判断对象：对象的可枚举属性个数为 0 时为空
  if (typeof value === "object") {
    return Object.keys(value).length === 0;
  }

  // 其它类型默认认为不为空
  return false;
}

/**
 * @description 预览图片
 * @param urlList
 * @param initialIndex
 */
export function previewImageViewer(urlList, initialIndex = 0) {
  const container = document.createElement("div");
  document.body.appendChild(container);

  const vNode = createVNode(ElImageViewer, {
    urlList,
    initialIndex,
    onClose: () => {
      render(null, container); // 卸载组件
      document.body.removeChild(container);
    },
  });

  render(vNode, container);
}
