const getters = {
  // user
  userInfo: (state: any) => state.user.userInfo,
  personInfo: (state: any) => state.user.personInfo,
  token: (state: any) => state.user.token,
  roles: (state: any) => state.user.roles,
  permission: (state: any) => state.user.permission,
  menuId: (state: any) => state.user.menuId,
  menu: (state: any) => state.user.menu,
  menuAll: (state: any) => state.user.menuAll,
  menuChildren: (state: any) => state.user.menuChildren,
  activeMenu: (state: any) => state.user.activeMenu,
  countryInfo: (state: any) => state.user.countryInfo, //账号行政区域
  languageList: (state: any) => state.user.languageList,
  currencyInfo: (state: any) => state.user.currencyInfo,//货币

  // tags
  tag: (state: any) => state.tags.tag,
  keyCollapse: (state: any, getters: any) =>
    getters.screen > 1 ? getters.isCollapse : false,
  tagList: (state: any) => state.tags.tagList,
  tagWel: (state: any) => state.tags.tagWel,

  // options
  storeList: (state: any) => state.options.storeList,

  //common
  language: (state: any) => state.common.language,
  langIndex: (state: any) => state.common.langIndex,
  website: (state: any) => state.common.website,
  themeName: (state: any) => state.common.themeName,
  isCollapse: (state: any) => state.common.isCollapse,
  screen: (state: any) => state.common.screen,
  isLock: (state: any) => state.common.isLock,
  isFullScren: (state: any) => state.common.isFullScren,
  isMenu: (state: any) => state.common.isMenu,
  lockPasswd: (state: any) => state.common.lockPasswd,
  showCollapse: (state: any) => state.common.showCollapse,
  topBarHeight: (state: any) => state.common.topBarHeight,
};
export default getters;
