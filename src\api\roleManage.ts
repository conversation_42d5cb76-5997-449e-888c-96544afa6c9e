/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-06 11:39:44
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-06 14:15:32
 * @FilePath: \flb-receipt\src\api\roleManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from './axios'
import { hrBaseUrl } from '@/config/env'

interface Params {
  [key: string]: any
}

// 角色-事业线-分页
export const getRolePageApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/role/buIdPage',
  method: 'get',
  params: data
})

// 角色-添加/修改
export const saveRoleApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/role/save',
  method: 'post',
  data
})

// 角色-删除
export const delRoleApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/role/del',
  method: 'post',
  params: data
})

// 角色-获取菜单信息
export const getByRoleIdApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/role/getByRoleId',
  method: 'get',
  params: data
})

// 角色-绑定菜单
export const bindMenuApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/role/bindMenu',
  method: 'post',
  data
})

// 根据租户id查询可用菜单树结构
export const getMenuByTenantIdApi = (data: Params) => request({
  url: hrBaseUrl + '/core/sys/menu/getMenuByTenantId',
  method: 'get',
  params: data
})