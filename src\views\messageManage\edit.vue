<template>
  <el-drawer v-model="isShowDrawer" append-to-body size="70%" @close="onClose"
             class="radius8_drawer title_drawer"
             modal-class="transparent_modal top_drawer_modal"
             :title="row ? $t('receipt.editMessage') : $t('receipt.newMessage')"
  >
    <div class="">
      <el-form :model="form" :rules="rules" ref="formRef">
        <div class="flex align_items_center">
          <div class="main-bg height12 width3 border_radius2"></div>
          <div class="ml8 fs14 fw500">{{ $t('receipt.config') }}</div>
        </div>
        <div class="dash-divide mt8"></div>
        <div class="mt24 ml16 ">
          <el-form-item :label="$t('receipt.messageTitle')" prop="title" required>
            <el-input :placeholder="$t('receipt.pleaseInput')" show-word-limit maxlength="50"
                      v-model="form.title"></el-input>
          </el-form-item>
          <el-form-item :label="$t('receipt.mainBody')" class="mt20" required prop="content">
            <div class="widthP100">
              <div class="flex flex_wrap">
                <div v-for="item in tag" class="tag flex_shrink0 mr8 mb8" @click="insertFlag(item)">
                  <span>${</span>
                  <span>{{ item }}</span>
                  <span>}</span>
                </div>
              </div>
              <el-input type="textarea"
                        :autosize="{ minRows: 10 ,maxRows:15}"
                        maxlength="100"
                        :placeholder="$t('receipt.pleaseInput')"
                        show-word-limit
                        @blur="campaignNameBlur"
                        v-model="form.content"
              ></el-input>
            </div>
          </el-form-item>
        </div>

        <div class="mt24 ml16 ">
          <!--推送提醒-->
          <div class="flex align_items_center mt48">
            <div class="main-bg height12 width3 border_radius2"></div>
            <div class="ml8 fs14 fw500">{{ $t('receipt.pushAndRemind') }}</div>
          </div>
          <div class="dash-divide mt8"></div>

          <!--是否周期性-->
          <el-form-item :label="$t('receipt.whetherPeriodicMessage')" class="mt24 switch_radio" required>
            <el-radio-group v-model="form.cycleMessage">
              <el-radio-button label="YES">{{ $t('receipt.yes') }}</el-radio-button>
              <el-radio-button label="NO">{{ $t('receipt.no') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>


          <div v-if="form.cycleMessage === 'YES'">
            <el-form-item :label="$t('receipt.dimensionDeliveryTime')" class="mt20 switch_radio" required>
              <el-radio-group v-model="form.issueDimension">
                <el-radio-button label="DAY">{{ $t('receipt.everyday') }}</el-radio-button>
                <el-radio-button label="WEEK">{{ $t('receipt.weekly') }}</el-radio-button>
                <el-radio-button label="MONTH">{{ $t('receipt.monthly') }}</el-radio-button>
              </el-radio-group>
            </el-form-item>


            <div class="mt20" v-if="form.issueDimension==='WEEK'">
              <el-form-item :label="$t('receipt.deliveryDate')" required prop="issueWeek">
                <el-select v-model="form.issueWeek" :placeholder="$t('receipt.pleaseSelect')" class="widthP100">
                  <el-option
                      v-for="item in weekOpts"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"/>
                </el-select>
              </el-form-item>
            </div>
            <div v-else-if="form.issueDimension==='MONTH'" class="mt20">
              <el-form-item :label="$t('receipt.deliveryDate')" required prop="issueDay">
                <el-select v-model="form.issueDay" :placeholder="$t('receipt.pleaseSelect')" class="widthP100">
                  <el-option
                      v-for="item in 30"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item :label="$t('receipt.deliveryTime')" required class="mt20" prop="sendTime">
              <el-time-picker style="width: 100% !important;" v-model="form.sendTime"
                              format="HH:mm:ss"
                              value-format="HH:mm:ss"
                              :placeholder="$t('receipt.pleaseSelect')"
              ></el-time-picker>
            </el-form-item>
          </div>

          <div>
            <el-form-item :label="$t('receipt.webLandingPagePath')" class="mt20">
              <el-input :placeholder="$t('receipt.pleaseInput')" show-word-limit maxlength="50"
                        v-model="form.webLink"></el-input>
            </el-form-item>
            <el-form-item :label="$t('receipt.appPathType')" class="mt20" required>
              <el-radio-group v-model="form.pushLinkType">
                <el-radio border label="VANILLA">{{ $t('receipt.protogenesis') }}</el-radio>
                <el-radio border label="H5">{{ $t('receipt.h5') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('receipt.appLandingPagePath')" class="mt20">
              <el-input :placeholder="$t('receipt.pleaseInput')" show-word-limit maxlength="50"
                        v-model="form.appLink"></el-input>
            </el-form-item>

            <el-form-item :label="$t('receipt.whetherApopUpReminderIsRequired')" class="mt20" required>
              <el-radio-group v-model="form.toastRemind">
                <el-radio border label="YES">{{ $t('receipt.yes') }}</el-radio>
                <el-radio border label="NO">{{ $t('receipt.no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="t_right">
        <el-button size="small" @click="onClose">{{ $t('receipt.cancel') }}</el-button>
        <el-button type="primary" class="ml8" size="small" :loading="btnLoading" @click="onSubmit">
          {{ $t('receipt.submit') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {onMounted, ref, defineProps, defineEmits} from "vue";
import {addMsgConfigApi, editMsgConfigApi, getMessageManageDetailsApi} from "@/api/messageManage";
import {ElMessage, FormInstance} from "element-plus";
import website from "@/config/website";
import lang from '@/lang/index'

const i18n = lang.global
const isShowDrawer = ref(false)
const btnLoading = ref(false)
const formRef = ref<FormInstance>()

const props = defineProps({
  row: {type: Object}
})

const rules = ref({
  title: [
    {required: true, message: i18n.t('receipt.pleaseInput'), trigger: 'blur'}
  ],
  content: [
    {required: true, message: i18n.t('receipt.pleaseInput'), trigger: 'blur'}
  ],
  issueDay: [
    {required: true, message: i18n.t('receipt.pleaseInput'), trigger: 'change'}
  ],
  issueWeek: [
    {required: true, message: i18n.t('receipt.pleaseInput'), trigger: 'change'}
  ],
  sendTime: [
    {required: true, message: i18n.t('receipt.pleaseInput'), trigger: 'change'}
  ],
})
const weekOpts = [
  {id: 0, name: i18n.t('receipt.monday')},
  {id: 1, name: i18n.t('receipt.tuesday')},
  {id: 2, name: i18n.t('receipt.wednesday')},
  {id: 3, name: i18n.t('receipt.thursday')},
  {id: 4, name: i18n.t('receipt.friday')},
  {id: 5, name: i18n.t('receipt.saturday')},
  {id: 6, name: i18n.t('receipt.sunday')}
]

const tag = [i18n.t('receipt.salesOrderNumber'),
  i18n.t('receipt.salesReturnNumber'),
]
const form = ref(
    {
      title: '',
      content: '',
      cycleMessage: 'NO',
      issueDimension: 'DAY',
      toastRemind: 'NO',
      pushLinkType: 'H5'
    })

onMounted(() => {
  isShowDrawer.value = true
  if (props.row) {
    getDetail(props.row.id)
  }
})

const getDetail = (id) => {
  getMessageManageDetailsApi(id).then((res) => {
    if (res.code == 200) {
      const info = res.data;
      info.contentThird = info.contentThird ? info.contentThird.value : ''
      info.cycleMessage = info.cycleMessage ? info.cycleMessage.value : ''
      info.issueDimension = info.issueDimension ? info.issueDimension.value : ''
      info.messageType = info.messageType ? info.messageType.value : ''
      info.systemType = info.systemType ? info.systemType.value : ''
      info.toastRemind = info.toastRemind ? info.toastRemind.value : ''
      info.triggerThird = info.triggerThird ? info.triggerThird.value : ''
      info.pushLinkType = info.pushLinkType ? info.pushLinkType.value : 'H5'
      form.value = info;
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const emit = defineEmits(['onRefresh', 'onClose'])
const onClose = () => {
  emit('onClose')
}

const onSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      btnLoading.value = true
      let saveApi = addMsgConfigApi
      if (props.row) {
        saveApi = editMsgConfigApi
      }
      form.value.moduleId = website.moduleId
      delete form.value.allUser
      delete form.value.delFlag
      delete form.value.userThird
      saveApi(form.value).then((res) => {
        if (res.success) {
          ElMessage.success(i18n.t('receipt.saveSuccess'))
          emit('onRefresh')
          onClose()
        } else {
          ElMessage.error(res.msg)
        }
        btnLoading.value = false
      })
    }
  })
}

///光标位置
const cursorPosition = ref("")
///记录光标位置
const campaignNameBlur = (e) => {
  cursorPosition.value = e.target.selectionStart;
}
///插入标识
const insertFlag = (flag) => {
  const newFlag = "${" + flag + "}"
  const num = cursorPosition.value;
  const type = typeof num
  if (type == "number") {
    const content = form.value.content
    const right = content.slice(0, num)
    const left = content.slice(num);
    form.value.content = right + newFlag + left
  } else {
    form.value.content += newFlag;
  }
}
</script>

<style scoped lang="scss">
.head {
  display: flex;
  align-items: center;
  padding: 16px 24px 12px 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #1D2330;
  border-bottom: 1px solid #DFE2E7;
}

.footer {
  box-shadow: 0px -2px 4px 0px rgba(27, 30, 35, 0.06);
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 14px 16px;
}

.tag {
  padding: 8px 12px;
  border: 1px solid #D6DCE1;
  box-shadow: 0px 2px 2px 0px rgba(23, 33, 54, 0.1);
  background: linear-gradient(180deg, #FFFFFF 22%, #F0F2F3 100%);
  border-radius: 4px;
}

::v-deep {
  .el-form-item__label {
    width: 144px;
    color: #1D2330;
    font-size: 12px;
    text-align: left;
    justify-content: flex-start;
  }

  .el-form-item__content {
    width: 480px;
  }

  .switch_radio {
    .el-radio-group {
      padding: 2px;
      background: #EDF0F2;
      border-radius: 4px;
    }

    .el-radio-button__inner {
      background: transparent;
      font-size: 12px;
      color: #646878;
      border-radius: 0;
      border: none !important;
      padding: 8px 12px;
    }

    .el-radio-button.is-active .el-radio-button__original-radio:not(:disabled) + .el-radio-button__inner {
      border-radius: 4px;
      background-color: #FFFFFF;
      font-size: 12px;
      font-weight: 500;
      color: #1D2330;
      border: none !important;
      border-color: transparent !important;
      box-shadow: none;
      -webkit-box-shadow: none;
    }
  }
}

</style>