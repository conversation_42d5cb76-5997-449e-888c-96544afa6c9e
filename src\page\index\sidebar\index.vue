<template>
  <div class="avue-sidebar">
    <logo></logo>
    <el-scrollbar style="height:100%">
      <el-menu mode="vertical"
               :default-active="$route.path"
               :collapse="keyCollapse">
        <template #title>
          <i class="el-icon-location"></i>
          <span></span>
        </template>
        <template v-for="item in childrenMenu">
          <el-menu-item v-if="validatenull(item.children) && vaildRoles(item)"
                        @click="open(item)"
                        :key="item.label"
                        :index="item.path">
            <i class="iconfont-flb"  :class="item['icon']"></i>
            <template #title>
              <span :title="generateName(item)">{{ generateName(item) }}</span>
            </template>
          </el-menu-item>

          <el-sub-menu v-if="!validatenull(item.children) && vaildRoles(item)"
                        :index="item.path"
                        :key="item.label">
            <template #title>
              <i class="iconfont-flb"  :class="item.icon"></i>
              <span :title="generateName(item)"> {{ generateName(item) }}</span>
            </template>
            <template v-for="(child) in item.children">
              <el-menu-item :index="child.path"
                            @click="open(child)"
                            v-if="validatenull(child.children)"
                            :key="child.label">
                <i class="iconfont-flb"  :class="child.icon"></i>
                <template #title>
                  <span :title="generateName(child)">
                    {{ generateName(child) }}
                    <!-- {{$t('route.'+child.langKey)}} -->
                  </span>
                </template>
              </el-menu-item>
            </template>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import logo from '../logo.vue'
import { validatenull } from "@/util/validate"
import { generateName } from "@/util/util"

export default {
  name: "sidebar",
  inject: ["index"],
  components: { logo },
  props: {
    childrenMenu: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {}
  },
  created () {
    // this.index.openMenu(this.menuId)
  },
  computed: {
    ...mapGetters([
      "website",
      "menu",
      "tag",
      "keyCollapse",
      "screen",
      "menuId",
    ])
  },
  methods: {
    validatenull (data) {
      return validatenull(data)
    },
    vaildRoles (item) {
      return true
      // item.meta = item.meta || {}
      // return item.meta.roles ? item.meta.roles.includes(this.roles) : true
    },
    open (item) {
      //除薪酬外的直接调用跳转
      if (item.path.indexOf('/salaryPerformance') == -1) {
        let query = { ...item.query }
        this.$router.push({
          path: item.path,
          query: query
        })
        this.$emit('setIframeShowFalse')
      } else {
        //薪酬部分需要设置iframe
        this.$emit('setIframeUrl', item)
      }
    },
    generateName (item) {
      return generateName(item)
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-submenu__icon-arrow) {
  margin-top: -3px;
}

.el-menu {
  .svg-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: -7px;
  }

  .el-menu-item.is-active {
    color: rgba(255, 255, 255, 0.90);

    .svg-icon {
      opacity: 1;
    }
  }
}
</style>
<style lang="scss">
</style>