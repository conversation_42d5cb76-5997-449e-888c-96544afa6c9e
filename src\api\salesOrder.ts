import request from './axios'
import { baseUrl } from '@/config/env'

interface Params {
  [key: string]: any
}

// 销售订单管理
// 列表(分页)
export const getSalesOrderApi = (data: Params) => request({
  url: baseUrl + '/erp/salesOrder/getPage',
  method: 'get',
  params: data
})

// 根据Id获取详情
export const getSalesOrderInfoApi = (id: string) => request({
  url: baseUrl + '/erp/salesOrder/getById/' + id,
  method: 'get'
})

// 提交
export const submitSalesOrderApi = (data: Params) => request({
  url: baseUrl + '/erp/salesOrder/submit',
  method: 'post',
  data: data
})

// 修改
export const updateSalesOrderApi = (data: Params) => request({
  url: baseUrl + '/erp/salesOrder/updateById',
  method: 'PUT',
  data: data
})

// 批量删除
export const batchDelSalesApi = (idList: string) => request({
  url: baseUrl + '/erp/salesOrder/deleteByIdList/' + idList,
  method: 'DELETE'
})



// 销售货品串码明细管理
// 列表(分页)
export const getSalesOrderCodeApi = (data: Params) => request({
  url: baseUrl + '/erp/salesBarcode/',
  method: 'get',
  params: data
})



// 销售货品明细管理
// 列表(分页)
export const getSalesOrderSkuApi = (data: Params) => request({
  url: baseUrl + '/erp/salesSku/getPage',
  method: 'get',
  params: data
})



// 客户管理表管理
// 获取分组树
export const getCustomerTreeApi = (data: Params) => request({
  url: baseUrl + '/sys/customer/tree',
  method: 'get',
  params: data
})

// 获取客户门店
export const getCustomerStoreApi = (data: Params) => request({
  url: baseUrl + '/sys/sysUserManage/getStorePageByUserAndModule' ,
  method: 'get',
  params: data
})
