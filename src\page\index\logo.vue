<template>
  <div class="avue-logo" :style="{ top: `${topBarHeight}px` }">
    <transition name="fade">
      <div v-if="keyCollapse" class="avue-logo_subtitle" key="0"></div>
    </transition>
    <transition-group name="fade">
      <div v-if="!keyCollapse" key="1" class="flex align_items_center pl12">
        <div class="avue-logo_title mr8"></div>
        <span class="fs16 fw600 lh24"> 开单管理 </span>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "logo",
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["website", "keyCollapse", "topBarHeight"]),
  },
  methods: {},
};
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.avue-logo {
  position: fixed;
  top: 0;
  left: 0;
  width: 252px;
  padding: 20px 0;
  overflow: hidden;
  box-sizing: border-box;
  z-index: 1024;
  &_title {
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-position: center;
  }
  &_subtitle {
    width: 36px;
    height: 36px;
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-position: center;
  }
}
</style>
