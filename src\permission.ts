/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-06 10:23:47
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-28 10:41:25
 * @FilePath: \flb-receipt\src\permission.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { workbenchUrl, logOutPage } from "./config/env";
import router from "./router";
import store from "./store";
import { getToken } from "./util/auth";
import _ from "lodash";
import { generateTitle } from '@/util/util'

const lockPage = store.getters.website.lockPage;
router.beforeEach((to, from, next) => {
  const meta = to.meta || {};

  //乾坤环境-如果不存在匹配的路由则进入home（兼容vue-router3+和vue-router4+导致的问题）
  if ((window as any).__POWERED_BY_QIANKUN__) {
    const hasRouteItem = router
      .getRoutes()
      .some((routeItem) => routeItem.path === to.path);
    if (!hasRouteItem && store.getters.menu.length) {
      //默认跳转第一个菜单
      if (store.getters.menu[0].children.length) {
        next({ path: store.getters.menu[0].children[0].path });
      } else {
        next({ path: store.getters.menu[0].path });
      }
      return false;
    }
  }

  //判断是否有token
  if (getToken() && meta.auth !== false) {
    if (store.getters.isLock && to.path != lockPage) {
      return lockPage;
    } else if (to.path === "/login") {
      store.dispatch("user/FedLogOut").then(() => {
        window.location.href = workbenchUrl + "/login" + logOutPage();
        // next({path:'/login'})
      });
    } else if (to.path === "/403" || to.path === "/404") {
      next();
    } else {
      // 如果用户信息为空则获取用户信息,获取用户信息失败,跳转到登录页
      if (!store.getters.roles.length) {
        store
          .dispatch("user/GetAuthorInfo")
          .then(() => {
            if (store.getters.menu.length) {
              if (to.path) {
                next();
                return false;
              }
              //默认跳转第一个菜单
              if (store.getters.menu[0].children.length) {
                next({ path: store.getters.menu[0].children[0].path });
              } else {
                next({ path: store.getters.menu[0].path });
              }
            } else {
              next({ path: "/403" });
            }
          })
          .catch(() => {
            store.dispatch("user/FedLogOut").then(() => {
              window.location.href = workbenchUrl + "/login" + logOutPage();
            });
          });
      } else {
        next();
      }
    }
  } else {
    //判断是否需要认证，没有登录访问去登录页
    if (meta.isAuth === false) {
      next();
    } else {
      store.dispatch("user/LogOut").then(() => {
        window.location.href = workbenchUrl + "/login" + logOutPage();
        // next({path:'/login'})
      });
    }
  }
});

router.afterEach((to: any) => {
  // const title = to.name || "";
  const i18n = to.meta.i18n;
  const title = generateTitle(to, i18n);
  if(title) document.title = title;
});
