<template>
  <el-select
    v-model="personId"
    :placeholder="props.placeholder || $t('receipt.pleaseChoose')"
    size="default"
    :class="props.class"
    filterable
    remote
    reserve-keyword
    :remote-method="getList"
    :loading="loading"
    popper-class="custom_header_select"
  >
    <template #header>
      <ul class="select_box flex border_radius4 c_4F5363">
        <li
          v-for="item in sourceList"
          :key="item.id"
          class="border_radius4 lh24 pointer"
          :class="{ active: item.id === source }"
          @click="source = item.id"
        >
          {{ item.name }}
        </li>
      </ul>
    </template>

    <template v-if="source == '1'" #default>
      <el-option
        v-for="item in hrPersonOpts"
        :key="item.code"
        :label="`${item.value}（${item.personCode}-${item.departmentName}）`"
        :value="item.code"
        @click="onSelHrPerson(item)"
      />
    </template>
    <template v-else #default>
      <el-option
        v-for="item in areaPersonOpts"
        :key="item.personId"
        :label="`${item.personName}（${item.businessCode}-${item.title}）`"
        :value="item.personId"
        @click="onSelAreaPerson(item)"
      />
    </template>

    <template #empty>
      <empty-box></empty-box>
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { defineProps, ref, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import { getAreaPersonApi, getHrPersonApi } from "@/api/common";
import emptyBox from "@/components/empty/index.vue";
import lang from "@/lang/index";

const i18n = lang.global;
const emit = defineEmits(["change"]);
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: () => "",
  },
  class: {
    type: String,
    default: () => "widthP100",
  },
});

const loading = ref(false);
const personId = ref(props.modelValue);
const source = ref("1");
const sourceList = [
  { id: "1", name: "人资系统" },
  { id: "2", name: "导购系统" },
];
const hrPersonOpts = ref([]);
const areaPersonOpts = ref([]);

const getList = (str: string) => {
  if (str) {
    getAreaPerson(str);
    getHrPerson(str);
  }
};
// 获取人资人
const getHrPerson = (str: string) => {
  loading.value = true;
  getHrPersonApi({ searchKey: str })
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        hrPersonOpts.value = list;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};
// 获取导购人
const getAreaPerson = (str: string) => {
  loading.value = true;
  getAreaPersonApi({ searchKey: str })
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        areaPersonOpts.value = list;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// 选择部门人
const onSelHrPerson = (item) => {
  const person = {
    id: item.code,
    code: item.personCode,
    name: item.value,
  };
  emit("change", person);
};
// 选择区域人
const onSelAreaPerson = (item) => {
  const person = {
    id: item.personId,
    code: item.businessCode,
    name: item.personName,
  };
  emit("change", person);
};
</script>

<style lang="scss" scoped>
.select_box {
  padding: 2px;
  background-color: #edf0f2;
  list-style: none;
  li {
    width: 50%;
    text-align: center;
  }
  .active {
    color: #1d2330;
    font-weight: 600;
    background-color: #ffffff;
  }
}
</style>
<style lang="scss">
.custom_header_select {
  .el-select-dropdown__header {
    border-bottom: none;
  }
  .el-radio-group {
    display: block;
  }
  .el-radio {
    display: block;
  }
}
</style>
