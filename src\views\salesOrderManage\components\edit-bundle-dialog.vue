<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="$t('receipt.addBundleActivity')"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="600px"
  >
    <div
      class="border_radius4 bgc_EFF4FF flex flex_direction_column pt12 pb12 pl16 pr16 mb24"
    >
      <div class="fs12">
        <i
          class="iconfont-flb icon-tishi mr8 main-color"
          style="font-size: 16px; vertical-align: bottom"
        ></i>
        <span class="c_3D3D3D">
          {{
            $t("receipt.currentActivityNumM", {
              count: list?.length || 0,
            })
          }}
        </span>
      </div>
    </div>
    <div
      class="flex flex_direction_column overflow_y_auto flex1"
      style="gap: 12px"
    >
      <div
        v-for="item in list || []"
        :key="item.id"
        class="flex flex_direction_column pt16 pb16 pl16 pr24 border_radius4 acitivity-item"
      >
        <div
          v-if="!checkListIds.includes(item.id) && getLackProduct(item)?.length"
          class="flex border_radius2 pt4 pb4 pl12 pr12 mb16"
          style="background-color: #fff5e2"
        >
          <i
            class="iconfont-flb icon-caozuotishi mr4"
            style="font-size: 16px; vertical-align: bottom; color: #fe9b0e"
          ></i>
          <span class="c_4F5363">
            {{
              $t("receipt.addBundleTip", {
                name: getLackProduct(item)
                  .map((item) => `【${item.productName?.value || ""}】`)
                  .join("、"),
              })
            }}
          </span>
        </div>
        <div class="flex align_items_center">
          <div class="flex flex_direction_column flex1">
            <div class="flex align_items_center">
              <el-checkbox
                :model-value="checkListIds.includes(item.id)"
                @click.prevent.stop="changeCheck(item)"
              />
              <div class="flex1 lh22 fw500 fs14 ml8">
                {{ item?.ruleName || "" }}
              </div>
            </div>
            <div class="flex flex_direction_column mt16" style="gap: 8px">
              <div
                class="flex"
                v-for="el in item?.activityMultipleRuleProductVOList || []"
                :key="el.productId"
              >
                <div
                  class="flex align_items_center pt4 pb4 pl12 pr12 border_radius4 bgc_F5F7F9"
                  :class="
                    !checkListIds.includes(item.id) && getLackClass(item, el)
                  "
                >
                  <i
                    class="iconfont-flb icon-shangpin mr4"
                    style="font-size: 16px; vertical-align: bottom"
                  ></i>
                  <tooltip-text
                    text-class="c_4F5363"
                    :text="el?.productName?.value || ''"
                  />
                  <span class="pl16 c_4F5363">
                    {{ $t("receipt.quantityM") }}{{ el?.productNum || "" }}
                  </span>
                  <span
                    v-if="el?.usageMethod?.value === 'ORIGINAL_PRICE'"
                    class="pl16 c_4F5363"
                  >
                    {{ $t("receipt.originPrice") }}
                  </span>
                  <span
                    v-if="el?.usageMethod?.value === 'FREE'"
                    class="pl16 c_EC2D30"
                  >
                    {{ $t("receipt.free") }}
                  </span>
                  <span
                    v-if="el?.usageMethod?.value === 'DISCOUNT'"
                    class="pl16 c_EC2D30"
                  >
                    {{ $t("receipt.rabateM") }}
                    {{ el?.discountAmount }}
                    {{ $t("receipt.fracture") }}
                  </span>
                  <span
                    v-if="el?.usageMethod?.value === 'REDUCE_PRICE'"
                    class="pl16 c_EC2D30"
                  >
                    {{ $t("receipt.deductionM2") }}
                    {{ formatMoney(el?.reducePrice) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="checkListIds.includes(item.id)"
            class="flex flex_direction_column align_items_center"
          >
            <div class="mb6">
              {{ $t("receipt.activityNum") }}
            </div>
            <el-input-number
              v-model="item.groupNum"
              :step="1"
              :min="1"
              :max="getNumMax(item)"
              step-strictly
              style="width: 88px"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { ref, defineEmits, defineProps, watch, computed } from "vue";
import lang from "@/lang/index";
import store from "@/store";
import {
  add,
  formatMoney,
  round,
  divide,
  multiply,
  subtract,
} from "@/util/numberUtil";
import tooltipText from "@/components/tooltip-text.vue";
import { nextTick } from "vue";
import { plus } from "number-precision";
import { queryStoreStock } from "@/api/salesOrderManage";
import { onUnmounted } from "vue";

const i18n = lang.global;

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  data: {
    //活动规则列表
    type: [Array, null],
    required: true,
  },
  product: {
    // 主商品
    type: [Object, null],
    required: true,
  },
  selectProduct: {
    //已选商品列表
    type: Array,
    required: true,
  },
  storeId: {
    type: [String, Number],
    required: true,
  },
});
const productList = ref([]);
const list = ref([]);
const checkList = ref([]);

const checkListIds = computed(() => checkList.value.map((item) => item.id));

onUnmounted(() => {
  window.removeEventListener("keydown", onLisner);
});

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        productList.value = JSON.parse(
          JSON.stringify(props.selectProduct || [])
        );
        list.value = JSON.parse(JSON.stringify(props.data || [])).map(
          (item) => {
            return {
              ...item,
              groupNum: getGroupNum(item),
            };
          }
        );
        checkList.value = [];
        productList.value.forEach((el) => {
          if (el.skuId + "" === props.product.skuId + "") {
            if (el.children) {
              for (const key in el.children) {
                const c = list.value.find((e) => e.id + "" === key + "");
                checkList.value.push(c);
              }
            }
          }
        });
        list.value.sort((a, b) => {
          const aChecked = checkListIds.value.includes(a.id);
          const bChecked = checkListIds.value.includes(b.id);

          // 第 1 优先级：选中的排前
          if (aChecked !== bChecked) {
            return aChecked ? -1 : 1;
          }

          const aLack = (a.activityMultipleRuleProductVOList || []).some((c) =>
            getLackClass(a, c)
          );
          const bLack = (b.activityMultipleRuleProductVOList || []).some((c) =>
            getLackClass(b, c)
          );

          // 第 2 优先级：不缺的排前
          if (aLack !== bLack) {
            return aLack ? 1 : -1;
          }

          // 第 3 优先级：保持原顺序或按 id 排序
          return a.id - b.id; // 或 return 0 保持原数组顺序
        });

        window.addEventListener("keydown", onLisner);
      });
    } else {
      // 取消监听（需要传入同一个函数引用）
      window.removeEventListener("keydown", onLisner);
    }
  }
);

const onLisner = (e) => {
  if (e.key === "Enter") {
    onConfirm();
  }
};

const getLackProduct = (item) => {
  const list = item?.activityMultipleRuleProductVOList || [];

  const ids = [];
  productList.value.forEach((el) => {
    if (el.children) {
      // 活动组
      for (const key in el.children) {
        if (key + "" === item.id) {
          (el.children[key] || []).forEach((c) => {
            ids.push(c.skuId);
          });
        }
      }
    } else {
      ids.push(el.skuId + "");
    }
  });

  const lackList = [];
  list.forEach((el) => {
    if (
      !ids.includes(el.productId + "") &&
      el.productId + "" !== props.product?.skuId + ""
    ) {
      lackList.push(el);
    }
  });
  return lackList;
};

// 初始化获取组数
const getGroupNum = (item) => {
  let num = 0;
  const c = productList.value.find(
    (c) => c.skuId + "" === props?.product?.skuId + ""
  );
  const d = (c?.children && c?.children[item.id]) || [];
  if (d.length) {
    num = d[0].marketingUseNum || 0;
  } else {
    num = 0;
  }

  return num;
};

const getLackClass = (item, el) => {
  return getLackProduct(item)
    .map((c) => c.productId + "")
    .includes(el.productId + "")
    ? " bgc_FFF5E2"
    : "";
};

const changeCheck = (item) => {
  const index = checkListIds.value.findIndex((el) => item.id === el);

  if (index === -1) {
    if (props.product?.isBarcode?.value === "YES") {
      if (!getNumMax(item)) {
        ElMessage.warning(i18n.t("receipt.pleaceAddCode"));
        return;
      }
    }
    checkList.value.push(item);
    item.groupNum = 1;
  } else {
    checkList.value.splice(index, 1);
  }
};

const getNumMax = (item) => {
  let num = 0,
    ownNum = 0,
    groupNum = 0;

  list.value.forEach((el) => {
    let num2 = 0;
    (el?.activityMultipleRuleProductVOList || []).forEach((c) => {
      if (c.productId + "" === props.product?.skuId + "") {
        if (el.id === item.id) {
          groupNum = Number(c.productNum || 0);
        }
        num2 = add(
          multiply(Number(c.productNum || 0), Number(el.groupNum || 0)),
          num2
        );
      }
    });

    if (checkListIds.value.includes(el.id)) {
      if (el.id === item.id) {
        ownNum = num2;
      }
      num = add(num, num2);
    }
  });
  if (props.product.isBarcode?.value === "NO") {
    return round(
      divide(
        add(subtract(Number(props.product?.stockQuantity || 0), num), ownNum),
        groupNum
      ),
      0
    );
  }
  return Math.min(
    round(
      divide(
        add(ownNum, subtract(Number(props.product.quantity || 0), num)),
        groupNum
      ),
      0
    ),
    round(
      divide(
        add(subtract(Number(props.product?.stockQuantity || 0), num), ownNum),
        groupNum
      ),
      0
    )
  );
};

const onConfirm = () => {
  const waitAdd = [];
  const waitDelete = [];
  list.value.forEach((item) => {
    const productIndex = productList.value.findIndex(
      (c) => c.skuId === props.product?.skuId && !c.marketingActivityId
    );
    const el = productList.value[productIndex];
    if (!checkListIds.value.includes(item.id)) {
      // 取消选择

      if (el.children && el.children[item.id]) {
        (el.children[item.id] || []).forEach((c) => {
          waitAdd.push({
            ...c,
            marketingActivityId: null,
            marketingDiscountAmount: null,
            marketingMainSkuId: null,
            marketingReducePrice: null,
            marketingUseMethod: null,
            marketingUseNum: null,
            marketingActivityName: null,
          });
        });
        delete el.children[item.id];
      }
    } else {
      el.children = el.children || {};
      el.children[item.id] = (
        item?.activityMultipleRuleProductVOList || []
      ).map((c) => {
        const obj = {
          barcodeList: [],
          discountTotalPrice: 0,
          discountUnitPrice: Number(c.price || 0),
          originalTotalPrice: 0,
          originalUnitPrice: Number(c.price || 0),
          isBarcode: c.isBarcode,
          productCode: c.productCode,
          isGiveaway: "NO",
          productName: c.productName,
          quantity: multiply(
            Number(c.productNum || 0),
            Number(item.groupNum || 0)
          ),
          referenceUnitPrice: Number(c.price || 0),
          skuCode: c.productCode,
          skuId: c.productId,
          skuName: c.productName,
          stockQuantity: c.stockQuantity,
          stockType: "NEW",
          unitId: c.unitId,
          unitName: c.unitName,
          marketingActivityId: item.id, //团购/捆绑规则id
          marketingDiscountAmount:
            c?.usageMethod?.value === "DISCOUNT"
              ? Number(c?.discountAmount || 0)
              : null, //折扣数
          marketingMainSkuId:
            props.product?.skuId + "" === c.productId + ""
              ? null
              : props.product?.skuId, //捆绑活动的主商品id
          marketingReducePrice:
            c?.usageMethod?.value === "REDUCE_PRICE"
              ? Number(c?.reducePrice || 0)
              : null, //减价
          marketingUseMethod: c?.usageMethod?.value, //团购/捆绑优惠类型
          marketingUseNum: item.groupNum,
          marketingActivityName: item.ruleName,
        };
        waitDelete.push(obj);
        if (obj.skuId + "" === props.product.skuId + "") {
          waitAdd.push(obj);
        }
        return obj;
      });
    }
  });

  waitAdd.forEach((el) => {
    const index = productList.value.findIndex(
      (c) => c.skuId + "" === el.skuId + "" && !c.marketingActivityId
    );

    if (index !== -1) {
      if (el.marketingActivityId) {
        let childNum = 0;
        const obj = productList.value[index];
        if (obj.children) {
          for (const key in obj.children) {
            (obj.children[key] || []).forEach((c) => {
              if (c.skuId === el.skuId) {
                childNum = add(childNum, Number(c.quantity || 0));
              }
            });
          }
        }
        obj.quantity = Math.max(childNum, Number(obj.quantity || 0));
      } else {
        productList.value[index].quantity = plus(
          Number(productList.value[index]?.quantity || 0),
          Number(el?.quantity || 0)
        );
      }
    } else {
      productList.value.push(el);
    }
  });

  waitDelete.forEach((el) => {
    const index = productList.value.findIndex(
      (c) => c.skuId + "" === el.skuId + "" && !c.marketingActivityId
    );
    const obj = productList.value[index];

    if (index !== -1) {
      let childNum = 0;
      if (obj.children) {
        for (const key in obj.children) {
          (obj.children[key] || []).forEach((c) => {
            if (c.skuId === el.skuId) {
              childNum = add(childNum, Number(c.quantity || 0));
            }
          });
        }
      }
      const num = subtract(Number(obj.quantity || 0), childNum);
      if (num >= Number(el?.quantity || 0)) {
        obj.quantity = Math.max(
          subtract(Number(obj?.quantity || 0), Number(el?.quantity || 0)),
          0
        );
      }
    }
  });
  productList.value = productList.value.filter((el) => el.quantity);

  let ids = [];
  productList.value.forEach((el) => {
    if (!el.stockQuantity) {
      ids.push(el.skuId);
    }
    if (el.children) {
      for (const key in el.children) {
        (el.children[key] || []).forEach((item) => {
          if (!item.stockQuantity) {
            ids.push(item.skuId);
          }
        });
      }
    }
  });

  ids = [...new Set(ids)];

  if (ids.length) {
    // 获取库存赋值
    queryStoreStock({
      skuIdList: ids,
      storeId: props.storeId,
    })
      .then((res) => {
        if (res.code === 200) {
          const stockList = res.data || [];

          const list = [];

          productList.value.forEach((el) => {
            if (Number(el.stockQuantity || 0) < Number(el.quantity || 0)) {
              list.push(el);
            }

            const c = stockList.find((c) => c.skuId + "" === el.skuId + "");
            if (c) {
              el.stockQuantity = Number(c.canUseStockQuantity || 0);
            }
            if (el.children) {
              for (const key in el.children) {
                (el.children[key] || []).forEach((item) => {
                  const d = stockList.find(
                    (d) => d.skuId + "" === item.skuId + ""
                  );

                  if (d) {
                    item.stockQuantity = Number(d.canUseStockQuantity || 0);
                  }

                  if (
                    Number(item.stockQuantity || 0) < Number(item.quantity || 0)
                  ) {
                    list.push(item);
                  }
                });
              }
            }
          });

          if (list.length) {
            ElMessage.warning(
              i18n.t("receipt.stockTip", {
                name: [...list]
                  .map((item) => `【${item.skuName?.value}】`)
                  .join("、"),
              })
            );
          } else {
            emit("confirm", productList.value);
            emit("update:show", false);
          }
        } else {
          ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    emit("confirm", productList.value);
    emit("update:show", false);
  }
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped>
.el-checkbox {
  padding: 0;
  border: none;
  width: min-content;
}

.acitivity-item {
  border: 1px solid #dfe2e7;
  transition: all 0.3s;

  &:has(.is-checked) {
    border-color: #415fff;
  }
}
</style>
