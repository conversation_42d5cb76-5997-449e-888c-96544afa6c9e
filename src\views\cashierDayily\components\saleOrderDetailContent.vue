<template>
  <div
    v-loading="loading"
    :element-loading-text="$t('receipt.loadingInProgress')"
    class="flex flex_direction_column"
  >
    <div class="flex gap8">
      <search-box
        style="width: 320px"
        :modelValue="filterObj.searchStr"
        @onInput="(value) => (filterObj.searchStr = value)"
        :placeholder="$t('receipt.orderNoTip')"
        @onClear="
          filterObj.searchStr = '';
          getList();
        "
        @onSearch="getList"
      ></search-box>
      <!-- 单据状态 -->
      <!-- <el-select
          v-model="filterObj.examineState"
          style="width: 156px"
          :placeholder="$t('receipt.status')"
          @change="getList"
          clearable
        >
          <el-option
            v-for="item in examineStateOpts.filter((el) => el.id !== 0)"
            :key="item.id"
            :label="item.name"
            :value="item.key"
          />
        </el-select> -->
    </div>
    <!-- 表格 -->
    <div class="mt16" ref="tableRef">
      <el-table
        v-loading="loading"
        :element-loading-text="$t('receipt.loadingInProgress')"
        :data="tableData"
        :height="tableHeight"
        border
        class="custom_radius_table"
        size="small"
        style="width: 100%"
      >
        <el-table-column
          :label="$t('receipt.orderNo')"
          min-width="200"
          prop="orderNo"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="fw600 pointer main-color"
              @click="
                operate('detailsDrawer', {
                  data: scope.row,
                })
              "
            >
              {{ scope.row.orderNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.member')"
          min-width="128"
          prop="memberName"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="c_1D2330">{{ scope.row.memberName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.beforeTotalDiscountPrice')"
          min-width="128"
          prop="originalPrice"
          align="right"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 c_1D2330">
              {{ formatMoney(scope.row.originalPrice) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.afterTotalDiscountPrice')"
          min-width="128"
          prop="actualPrice"
          align="right"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 c_1D2330">
              {{
                scope.row.modifyActualPrice == null
                  ? formatMoney(scope.row.actualPrice)
                  : formatMoney(scope.row.modifyActualPrice)
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.salePerson')"
          min-width="128"
          prop="salespersonName"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="c_1D2330">{{ scope.row.salespersonName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.reviewer')"
          min-width="128"
          prop="examineName"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="c_1D2330">{{ scope.row.examineName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.status')"
          min-width="95"
          prop="examineState"
          fixed="right"
        >
          <template #default="scope">
            <span
              :class="judgeStatusColorOpts[scope.row.examineState.code] || ''"
              class="common-status-box"
            >
              {{
                filterNameById(scope.row.examineState.code, examineStateOpts)
              }}
            </span>
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <pagination
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>

    <!-- 销售单详情 -->
    <details-drawer
      v-if="operateDialog.type === 'detailsDrawer'"
      :data="operateDialog.data"
      v-model:show="operateDialog.show"
      @refresh="getList"
      noButton
    ></details-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, reactive, onUnmounted, onMounted } from "vue";
import searchBox from "@/components/filter/search.vue";
import {
  examineStateOpts,
  judgeStatusColorOpts,
} from "@/views/salesOrderManage/ts/enum";
import pagination from "@/components/pagination/index.vue";
import { filterNameById } from "@/hooks/publicMethod";
import { formatMoney } from "@/util/numberUtil";
import { tableFormat } from "@/util/util";
import { getOrderInfo } from "@/api/salesOrderManage";
import { ElMessage } from "element-plus";
import lang from "@/lang/index";
import emptyBox from "@/components/empty/index.vue";
import DetailsDrawer from "@views/salesOrderManage/components/details-drawer.vue";

const props = defineProps({
  orderData: {
    type: Object,
    required: true,
  },
});

const i18n = lang.global;
const loading = ref(false);
const filterObj = reactive({
  searchStr: "",
  examineState: "AUDIT_SUCCESS",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});

onMounted(() => {
  getTableHeight();
  getList();
  window.addEventListener("resize", getTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", getTableHeight);
});

const getTableHeight = () => {
  tableHeight.value = window.innerHeight - 187 - 58;
};

function getList() {
  loading.value = true;
  console.log(
    JSON.stringify({
      storeId: props.orderData.storeId,
      orderDate: props.orderData.date,
    })
  );

  const params = Object.assign(
    {
      storeId: props.orderData.storeId,
      orderDate: props.orderData.date,
    },
    pageObj,
    filterObj
  ) as any;
  if (!params.examineState) {
    params.examineStateList = ["AUDIT_NOW", "AUDIT_SUCCESS", "AUDIT_FAIL"];
  }
  getOrderInfo(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        tableData.value = list;
        pageObj.total = Number(res.data.total || 0);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
}

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};
// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

const operate = (type, val) => {
  operateDialog.title = val.title || "";
  operateDialog.content = val.content || "";
  operateDialog.type = type;
  operateDialog.data = val.data || {};
  operateDialog.show = true;
};
</script>

<style lang="scss">
.title-drawer2 {
  box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);

  .el-drawer__header {
    color: #1d2330;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
    padding: 14px 24px;
    border-bottom: 1px solid #dfe2e7;
    .el-drawer__close {
      font-size: 16px;
      color: #7781a1;
    }
  }
}
</style>
