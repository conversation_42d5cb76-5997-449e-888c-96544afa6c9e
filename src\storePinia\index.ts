import { defineStore, create<PERSON><PERSON> } from 'pinia'

const pinia = createPinia()
const mainStore = defineStore('main', {
  state: () => {
    return {
      menu: [] as any[],
      userInfo: {} as any
    }
  },
  persist: {
    enabled: true //数据持久化开启
  },
  getters: {
    getMenu: (state) => state.menu,
    getUserInfo: (state) => {
      return state.userInfo ? state.userInfo : JSON.parse(localStorage.getItem('sysUser'))
      // return JSON.parse(localStorage.getItem('sysUser'))
    }
  },
  actions: {
    setMenu(val: any[]) {
      this.menu = val
    },
    setUserInfo(val: any) {
      this.userInfo = val
      localStorage.setItem('sysUser', JSON.stringify(val))
    }
  }
})

export { pinia, mainStore }