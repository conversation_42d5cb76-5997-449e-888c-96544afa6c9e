import { encode, decode } from 'js-base64'

interface Currency {
  [key: string]: any
}

const base64 = {
  //加密
  encode (str: string) {
    return encode(str)
  },
  //加密query对象
  encodeQuery (obj: Currency) {
    for (const item in obj) {
      obj[item] = encode(obj[item])
    }
    return obj
  },
  //解密
  decode (str: string) {
    return decode(str)
  },
  //解密query对象
  decodeQuery (obj: Currency) {
    for (const item in obj) {
      obj[item] = decode(obj[item])
    }
    return obj
  },
}
export default base64