<template>
  <el-drawer v-model="isShowDrawer" append-to-body size="70%" @close="onClose" :modal-append-to-body="false"
             class="radius8_drawer title_drawer"
             modal-class="transparent_modal top_drawer_modal"
             :title="$t('receipt.messageDetails')"
  >
    <div class="flex1 ">
      <div class="item">
        <div class="c_1D2330 fs14 fw500">{{ details.title }}</div>
        <div class="dash-divide mt12"></div>
        <div class="mt8 flex">
          <div class="fs12 c_4F5363">{{ $t('receipt.ctime') }}：</div>
          <div class="fs12 c_1D2330">{{ details.ctime }}</div>
          <el-divider direction="vertical" class="ml16 mr16"></el-divider>
          <div class="fs12 c_4F5363">{{ $t('receipt.contentSource') }}：</div>
          <div class="fs12 c_1D2330">
            {{ details.contentThird?.code === 1 ? $t('receipt.busiSystemTrig') : $t('receipt.thisSystemConfig') }}
          </div>
        </div>

        <div class="fs12 c_1D2330 mt16">{{ details.content }}</div>
      </div>

      <!--推送提醒-->
      <div class="flex align_items_center mt48">
        <div class="main-bg height12 width3 border_radius2"></div>
        <div class="ml8 fs14 fw500">{{ $t('receipt.pushAndRemind') }}</div>
      </div>
      <div class="dash-divide mt8"></div>

      <div class="flex mt20">
        <div class="fs12 c_1D2330 flex1">{{ `${$t('receipt.webLandingPagePath')}：${details.webLink??'-'}` }}</div>
        <div class="fs12 c_1D2330 flex1">{{ `${$t('receipt.appLandingPagePath')}：${details.appLink??'-'}` }}</div>
        <div class="fs12 c_1D2330 flex1">
          {{ `${$t('receipt.whetherApopUpReminderIsRequired')}：${details.toastRemind?.message??'-'}` }}
        </div>
      </div>
    </div>


    <template #footer>
      <div class="t_right">
        <el-button size="small" @click="onClose">{{ $t('receipt.cancel') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {onMounted, ref, defineEmits, defineProps} from "vue";
import {useI18n} from "vue-i18n";
import {getMessageManageDetailsApi} from "@/api/messageManage";
import {ElMessage} from "element-plus";

const props = defineProps({
  row: {
    type: Object,
  }
})
const i18n = useI18n()
const isShowDrawer = ref(false)
const details = ref({})
const weekOpts = [
  {id: 0, name: i18n.t('receipt.monday')},
  {id: 1, name: i18n.t('receipt.tuesday')},
  {id: 2, name: i18n.t('receipt.wednesday')},
  {id: 3, name: i18n.t('receipt.thursday')},
  {id: 4, name: i18n.t('receipt.friday')},
  {id: 5, name: i18n.t('receipt.saturday')},
  {id: 6, name: i18n.t('receipt.sunday')}
]

onMounted(() => {
  isShowDrawer.value = true
  getDetail()
})

const getDetail = () => {
  getMessageManageDetailsApi(props.row.id).then((res) => {
    if (res.code == 200) {
      const info = res.data;
      details.value = info
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const emit = defineEmits(['onClose'])
const onClose = () => {
  emit("onClose");
}

</script>

<style scoped lang="scss">
.head {
  display: flex;
  align-items: center;
  padding: 16px 24px 12px 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #1D2330;
  border-bottom: 1px solid #DFE2E7;
}

.footer {
  box-shadow: 0px -2px 4px 0px rgba(27, 30, 35, 0.06);
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 14px 16px;
}

.item {
  border: 0.5px solid #DFE2E7;
  background: #F9FAFB;
  padding: 12px 16px;
}
</style>