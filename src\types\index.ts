export interface ResData {
  code: number,
  msg: string,
  error?: boolean,
  success?: boolean,
  notNull?: boolean,
  data: any,
  [key: string]: any
}

export interface ResPageData extends ResData {
  data: {
    pages: string,
    size: string,
    total: string,
    records: any[] | null
  }
}

export interface PageParams {
  page: number,
  pageSize: number,
  searchStr?: string,
  [key: string]: any
}