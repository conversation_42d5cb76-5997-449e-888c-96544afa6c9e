/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-17 11:05:47
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-17 13:46:08
 * @FilePath: \flb-receipt\src\api\naviConfig.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "./axios";
import { baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 开单导航配置
// 列表(分页)
export const getProductGroupList = (data: Params) =>
  request({
    url: baseUrl + "/order/productGroupShow/",
    method: "get",
    params: data,
  });

// 新增
export const addProductGroup = (data: Params) =>
  request({
    url: baseUrl + "/order/productGroupShow/",
    method: "post",
    data: data,
  });

// 删除
export const delProductGroup = (data: Params) =>
  request({
    url: baseUrl + `/order/productGroupShow/${data.id}`,
    method: "delete",
  });

//   排序
export const sortProductGroup = (data: Params) =>
  request({
    url: baseUrl + `/order/productGroupShow/sort`,
    method: "put",
    data,
  });
