<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible"
        content="IE=edge">
  <meta name="viewport"
        content="width=device-width,initial-scale=1.0">
  <link rel="icon"
        href="<%= BASE_URL %>favicon.ico">
  <!-- <link rel="stylesheet"
        href="https://lctech.oss-cn-chengdu.aliyuncs.com/vue/avue/2.6.16/index.css"> -->
  <!-- <link rel="stylesheet"
        href="https://oss.ynlcgroup.com/vue/animate/3.5.2/animate.css">
  <link rel="stylesheet"
        href="https://oss.ynlcgroup.com/vue/iconfont/1.0.0/index.css"> -->
  <title>
    开单管理
  </title>
  <style>
    html,
    body,
    #receipt-app {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    .avue-home {
      /* background-color: #303133; */
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .avue-home__main {
      user-select: none;
      width: 100%;
      flex-grow: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .avue-home__footer {
      width: 100%;
      flex-grow: 0;
      text-align: center;
      padding: 1em 0;
    }

    .avue-home__footer>a {
      font-size: 12px;
      color: #ABABAB;
      text-decoration: none;
    }

    .avue-home__loading {
      height: 32px;
      width: 32px;
      margin-bottom: 20px;
    }

    .avue-home__title {
      color: #FFF;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .avue-home__sub-title {
      color: #ABABAB;
      font-size: 12px;
    }
  </style>
</head>

<body class="theme-default" style="overscroll-behavior: none;">
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title
          %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="receipt-app"></div>
  <!-- built files will be auto injected -->

  <!--定位-->
  <!-- <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=4QTBZ-7DQKH-4CFD2-WUUGE-RXO3Z-PSFNA" ignore="true"></script>
  <script src="https://3gimg.qq.com/lightmap/components/geolocation/geolocation.min.js"></script> -->
</body>

</html>