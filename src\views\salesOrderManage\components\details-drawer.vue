<template>
  <el-drawer
    :model-value="show"
    :before-close="onClose"
    direction="rtl"
    size="720px"
    :title="$t('receipt.salesOrderDetail')"
    :modal="false"
    class="no_shadow_drawer"
    modal-class="detail_drawer_modal top_drawer_modal"
  >
    <div
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
      class="flex flex_direction_column"
    >
      <div class="b_solid_default border_radius8 mb32">
        <div
          class="pt12 pb8 pl16 pr16 bb_dashed_default flex align_items_center justify_content_between"
        >
          <div>
            <span class="c_1D2330 fs16 fw500 lh24 mr8">
              {{ $t("receipt.salesOrderM") }}{{ infoData.orderNo }}
            </span>
            <span
              v-if="infoData.examineState"
              :class="judgeStatusColorOpts[infoData.examineState?.code] || ''"
              class="common-status-box"
              style="vertical-align: bottom"
            >
              {{ infoData.examineState?.message }}</span
            >
            <span v-else>-</span>
          </div>
          <!-- <el-button
            link
            type="primary"
            @click="operate('priceChangeRecordDialog')"
          >
            {{ $t("receipt.priceChangeRecord") }}
          </el-button> -->
        </div>
        <div class="pt8 pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.billingDateM") }}
            <span class="c_1D2330">
              {{ dayjs(infoData.orderDate).format("YYYY/MM/DD") }}
            </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <span class="c_4F5363 lh18">
            {{ $t("receipt.salesmanM") }}
            <span class="c_1D2330">{{ infoData.salespersonName }}</span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <span class="c_4F5363 lh18">
            {{ $t("receipt.reviewerM") }}
            <span class="c_1D2330">{{ infoData.examineName || "-" }}</span>
          </span>
        </div>
        <div class="pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.memberM") }}
            <span class="c_1D2330">
              {{
                infoData.memberName
                  ? `${infoData.memberName}（${
                      infoData.memberCode || "-"
                    }） (LV.${infoData.level || 1}）`
                  : `-`
              }}
            </span>
          </span>
        </div>
        <div class="pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.storeM") }}
            <span class="c_1D2330">
              {{ infoData.storeName?.value }}
            </span>
          </span>
        </div>
        <div class="pb8 pl16 pr16" v-if="discountActivity">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.discountActivityM") }}
            <span class="c_1D2330">
              {{ discountActivity?.discountType?.message || "-" }}
            </span>
          </span>
        </div>
        <div class="pb16 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.remarksM") }}
            <span class="c_1D2330"> {{ infoData.orderRemarks || "-" }}</span>
          </span>
        </div>
      </div>
      <template v-if="productList && productList.length">
        <div class="common_title_with_flag mb16">
          {{ $t("receipt.goodsInfo") }}
        </div>
        <!--      商品信息卡片     -->
        <div v-for="(list, index2) in activityList" :key="index2">
          <div
            class="flex align_items_center mb8"
            :class="index2 === 0 ? 'mt4' : 'mt8'"
          >
            <i
              class="iconfont-flb icon-megaphone-fill mr4"
              style="font-size: 16px; color: #ff9320"
            ></i>
            <div class="flex1">
              {{
                $t("receipt.activityNumM", {
                  index: index2 + 1,
                }) + (list[0]?.marketingActivityName || "")
              }}
            </div>
          </div>
          <div
            v-for="(product, index) in list"
            :key="index"
            class="b_solid_default border_radius8 mb12"
          >
            <div class="bb_dashed_default">
              <div class="pt12 pb8 pl16 pr16 flex justify_content_between">
                <span class="c_1D2330 fs14 fw500 lh24 mr8">
                  <span
                    class="second-tag"
                    v-if="product?.stockType?.value === 'SECOND_HAND'"
                  >
                    {{ $t("receipt.secondHand") }}
                  </span>
                  {{ product?.skuName?.value }}
                </span>
                <span class="c_4F5363 lh18">
                  {{ $t("receipt.unitPriceM") }}
                  <span class="c_1D2330">
                    {{ formatMoney(Number(product.originalUnitPrice)) }}
                  </span>
                </span>
              </div>
              <div class="pb8 pl16 pr16 flex align_items_center">
                <div class="flex flex_direction_column flex1">
                  <div>
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.codeM") }}
                      <span class="c_1D2330">{{ product.skuCode }}</span>
                    </span>
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.unitM") }}
                      <span class="c_1D2330">
                        {{ product.unitName?.value || "-" }}
                      </span>
                    </span>
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span
                      class="lh18"
                      :class="
                        product?.marketingUseMethod?.value === 'FREE'
                          ? 'c_EC2D30'
                          : 'c_4F5363'
                      "
                    >
                      <span>
                        {{ product?.marketingUseMethod?.message || "" }}
                        <span
                          v-if="
                            product?.marketingUseMethod?.value ===
                              'REDUCE_PRICE' ||
                            product?.marketingUseMethod?.value === 'DISCOUNT'
                          "
                        >
                          ：
                        </span>
                      </span>
                      <span
                        v-if="
                          product?.marketingUseMethod?.value === 'REDUCE_PRICE'
                        "
                        class="c_EC2D30"
                      >
                        {{ formatMoney(product?.marketingReducePrice || 0) }}
                      </span>
                      <span
                        v-if="product?.marketingUseMethod?.value === 'DISCOUNT'"
                        class="c_EC2D30"
                      >
                        {{ product?.marketingDiscountAmount || 0 }}
                        {{ $t("receipt.fracture") }}
                      </span>
                    </span>
                    <!--唯一码商品才有串码-->
                    <template v-if="product.isBarcode?.value === 'YES'">
                      <el-divider
                        class="ml12 mr12"
                        direction="vertical"
                      ></el-divider>
                      <span class="c_4F5363 lh18">
                        {{ $t("receipt.serialCodeM") }}
                        <span class="c_1D2330">
                          {{
                            (product.barcodeList || [])
                              .map((el) => el.barcode)
                              .join(",")
                          }}
                        </span>
                      </span>
                    </template>
                  </div>
                </div>
                <span class="c_4F5363 lh18"> X{{ product.quantity }} </span>
              </div>
            </div>
            <el-collapse v-model="activeNames">
              <el-collapse-item :name="product.id" title="Consistency">
                <template #title>
                  <div
                    class="pt8 pb8 pr16 flex justify_content_between widthP100 heightP100 align_items_center"
                  >
                    <span class="c_1D2330 fw500 lh18">
                      {{ $t("receipt.payableAmountM") }}
                    </span>
                    <span class="c_EC2D30 fw500">
                      {{
                        product.modifyTotalPrice !== null
                          ? formatMoney(
                              subtract(
                                Number(product.modifyTotalPrice),
                                Number(getString("point", product, true))
                              )
                            )
                          : formatMoney(
                              subtract(
                                Number(product.discountTotalPrice),
                                Number(getString("point", product, true))
                              )
                            )
                      }}
                    </span>
                  </div>
                </template>
                <!--            折叠内容-->
                <div class="pt14 pb3 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.totalPriceProductM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ formatMoney(Number(product.originalTotalPrice)) }}
                    </span>
                  </div>
                </div>
                <div class="pt3 pb3 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.exchangeQuantityM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("exchange", product) }}
                    </span>
                  </div>
                </div>
                <div class="pt3 pb3 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.fullDeductionM") }}
                    </span>
                    <span class="c_1D2330">{{
                      getString("max", product)
                    }}</span>
                  </div>
                </div>
                <div class="pt3 pb3 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.rabateM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("discount", product) }}
                    </span>
                  </div>
                </div>
                <div class="pt3 pl16 pr16">
                  <div class="flex justify_content_between">
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.pointsDeductionM") }}
                    </span>
                    <span class="c_1D2330">
                      {{ getString("point", product) }}
                    </span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        <div v-if="activityList.length" class="flex align_items_center mb8 mt8">
          <div class="flex1">
            {{ $t("receipt.noActivityProduct") }}
          </div>
        </div>
        <div
          v-for="(product, index) in noActivityProduct"
          :key="index"
          class="b_solid_default border_radius8 mb12"
        >
          <div class="bb_dashed_default">
            <div class="pt12 pb8 pl16 pr16 flex justify_content_between">
              <span class="c_1D2330 fs14 fw500 lh24 mr8">
                <span
                  class="second-tag"
                  v-if="product?.stockType?.value === 'SECOND_HAND'"
                >
                  {{ $t("receipt.secondHand") }}
                </span>
                {{ product?.skuName?.value }}
              </span>
              <span class="c_4F5363 lh18">
                {{ $t("receipt.unitPriceM") }}
                <span class="c_1D2330">
                  {{ formatMoney(Number(product.originalUnitPrice)) }}
                </span>
              </span>
            </div>
            <div class="pb8 pl16 pr16 flex align_items_center">
              <div class="flex flex_direction_column flex1">
                <div>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.codeM") }}
                    <span class="c_1D2330">{{ product.skuCode }}</span>
                  </span>
                  <el-divider
                    class="ml12 mr12"
                    direction="vertical"
                  ></el-divider>
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.unitM") }}
                    <span class="c_1D2330">
                      {{ product.unitName?.value || "-" }}
                    </span>
                  </span>
                  <template
                    v-if="
                      discountActivity?.discountType?.value === 'GROUP_BUYING'
                    "
                  >
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span
                      class="c_4F5363 lh18"
                      v-if="
                        discountActivity &&
                        discountActivity?.skuId
                          ?.split(',')
                          ?.includes(product.skuId)
                      "
                    >
                      {{ discountActivity.discountType?.message }}：
                      <span class="c_1D2330">
                        {{ discountActivity?.discountName || "-" }}
                      </span>
                    </span>
                  </template>
                  <!--唯一码商品才有串码-->
                  <template v-if="product.isBarcode?.value === 'YES'">
                    <el-divider
                      class="ml12 mr12"
                      direction="vertical"
                    ></el-divider>
                    <span class="c_4F5363 lh18">
                      {{ $t("receipt.serialCodeM") }}
                      <span class="c_1D2330">
                        {{
                          (product.barcodeList || [])
                            .map((el) => el.barcode)
                            .join(",")
                        }}
                      </span>
                    </span>
                  </template>
                </div>
              </div>
              <span class="c_4F5363 lh18"> X{{ product.quantity }} </span>
            </div>
          </div>
          <el-collapse v-model="activeNames">
            <el-collapse-item :name="product.id" title="Consistency">
              <template #title>
                <div
                  class="pt8 pb8 pr16 flex justify_content_between widthP100 heightP100 align_items_center"
                >
                  <span class="c_1D2330 fw500 lh18">
                    {{ $t("receipt.payableAmountM") }}
                  </span>
                  <span class="c_EC2D30 fw500">
                    {{
                      product.modifyTotalPrice !== null
                        ? formatMoney(
                            subtract(
                              Number(product.modifyTotalPrice),
                              Number(getString("point", product, true))
                            )
                          )
                        : formatMoney(
                            subtract(
                              Number(product.discountTotalPrice),
                              Number(getString("point", product, true))
                            )
                          )
                    }}
                  </span>
                </div>
              </template>
              <!--            折叠内容-->
              <div class="pt14 pb3 pl16 pr16">
                <div class="flex justify_content_between">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.totalPriceProductM") }}
                  </span>
                  <span class="c_1D2330">
                    {{ formatMoney(Number(product.originalTotalPrice)) }}
                  </span>
                </div>
              </div>
              <div class="pt3 pb3 pl16 pr16">
                <div class="flex justify_content_between">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.exchangeQuantityM") }}
                  </span>
                  <span class="c_1D2330">
                    {{ getString("exchange", product) }}
                  </span>
                </div>
              </div>
              <div class="pt3 pb3 pl16 pr16">
                <div class="flex justify_content_between">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.fullDeductionM") }}
                  </span>
                  <span class="c_1D2330">{{ getString("max", product) }}</span>
                </div>
              </div>
              <div class="pt3 pb3 pl16 pr16">
                <div class="flex justify_content_between">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.rabateM") }}
                  </span>
                  <span class="c_1D2330">
                    {{ getString("discount", product) }}
                  </span>
                </div>
              </div>
              <div class="pt3 pl16 pr16">
                <div class="flex justify_content_between">
                  <span class="c_4F5363 lh18">
                    {{ $t("receipt.pointsDeductionM") }}
                  </span>
                  <span class="c_1D2330">
                    {{ getString("point", product) }}
                  </span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </template>
      <template v-if="giftsList.length">
        <div class="common_title_with_flag mb16">
          {{ $t("receipt.giftGiveaway") }}
        </div>
        <el-table
          :data="giftsList"
          border
          class="custom_radius4_table table_no_bborder"
          size="small"
          style="width: 100%"
        >
          <el-table-column :label="$t('receipt.giftGiveaway')" prop="skuName">
            <template #default="{ row }">
              {{ row.skuName?.value || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('receipt.giftNumber')"
            prop="quantity"
            width="100"
          />
          <template #empty>
            <empty-box></empty-box>
          </template>
        </el-table>
      </template>

      <template v-if="paymentList.length">
        <div class="common_title_with_flag mb16 mt20">
          {{ $t("receipt.receiveInfo") }}
        </div>
        <el-table
          :data="paymentList"
          border
          class="custom_radius4_table table_no_bborder"
          size="small"
          style="width: 100%"
        >
          <el-table-column
            min-width="296"
            :label="$t('receipt.payTypeHeader')"
            prop="payTypeHeader"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{
                `${row.terminalName?.value || "-"} - ${
                  row.bankName?.value || "-"
                } - ${row.payWay || "-"}`
              }}
            </template>
          </el-table-column>
          <el-table-column
            min-width="128"
            :label="$t('receipt.stageNum')"
            prop="periods"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.periods || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            min-width="160"
            :label="$t('receipt.receiveAmount')"
            prop="paymentPrice"
            show-overflow-tooltip
            align="left"
          >
            <template #default="{ row }">
              {{ formatMoney(row.paymentPrice) }}
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('receipt.voucher')"
            prop="attachment"
            width="88"
          >
            <template #default="{ row }">
              <img
                v-if="row.attachment && row.attachment.length"
                :src="row.attachment[0].fileUrl"
                style="
                  width: 100%;
                  height: 100%;
                  border-radius: 4px;
                  cursor: pointer;
                "
                @click="preview(row)"
              />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <template #empty>
            <empty-box></empty-box>
          </template>
        </el-table>
      </template>
    </div>

    <template #footer>
      <div class="t_left">
        <div class="fs14" :class="props.noButton ? '' : 'pb8'">
          <span class="fs14 c_4F5363 lh18">
            {{ $t("receipt.totalPriceProductM") }}
            <span class="fs14 c_1D2330 fw500">
              {{ formatMoney(Number(infoData.originalPrice || 0)) }}
            </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <span class="fs14 c_4F5363 lh18">
            {{ $t("receipt.totalReductionM") }}
            <span class="fs14 c_1D2330 fw500">
              {{ formatMoney(Number(infoData.discountPrice || 0)) }}
            </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <template v-if="infoData.modifyActualPrice !== null">
            <span class="fs14 c_4F5363 lh18">
              {{ $t("receipt.beforePayableAmountM") }}
              <span class="fs14 c_EC2D30 fw500">
                {{ formatMoney(Number(infoData.actualPrice || 0)) }}
              </span>
            </span>
            <el-divider class="ml12 mr12" direction="vertical"></el-divider>
            <span class="fs14 c_4F5363 lh18">
              {{ $t("receipt.afterPayableAmountM") }}
              <span class="fs14 c_EC2D30 fw500">
                {{ formatMoney(Number(infoData.modifyActualPrice || 0)) }}
              </span>
            </span>
          </template>
          <template v-else>
            <span class="fs14 c_4F5363 lh18">
              {{ $t("receipt.payableM") }}
              <span class="fs14 c_EC2D30 fw500">
                {{ formatMoney(Number(infoData.actualPrice || 0)) }}
              </span>
            </span>
          </template>
        </div>
        <template v-if="!props.noButton">
          <template v-if="infoData?.examineState?.code === 0">
            <!-- 挂起 -->
            <el-button type="primary" @click="operate('addSalesOrderPage', {})">
              {{ $t("receipt.edit") }}
            </el-button>
            <el-button
              type="danger"
              @click="
                operate('delDialog', {
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
            <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          </template>
          <template v-if="infoData?.examineState?.code === 1">
            <!-- 审核中 -->
            <template v-if="checkAuth(infoData)">
              <el-button
                type="primary"
                @click="
                  operate('auditDialog', {
                    title: $t('receipt.auditPass'),
                  })
                "
              >
                {{ $t("receipt.pass") }}
              </el-button>
              <el-button
                type="danger"
                @click="
                  operate('auditDialog', {
                    title: $t('receipt.auditReject'),
                  })
                "
              >
                {{ $t("receipt.reject") }}
              </el-button>
            </template>
            <el-button
              type="danger"
              @click="
                operate('returnDialog', {
                  title: $t('receipt.recallPrompt'),
                  content: $t('receipt.recallPromptContent'),
                })
              "
            >
              {{ $t("receipt.recall") }}
            </el-button>
            <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          </template>
          <!-- 已通过 -->
          <template v-else-if="infoData?.examineState?.code === 2">
            <el-button
              v-if="infoData?.returnState?.value !== 'ALL_RETURN'"
              type="primary"
              @click="
                operate('addReturnSalesOrder', {
                  data: { saleOrderNo: infoData.orderNo },
                })
              "
            >
              {{ $t("receipt.returnGoods") }}
            </el-button>
            <!-- <el-button
            v-if="
              infoData?.returnState?.value === 'NOT_RETURN' &&
              checkPriceChange(infoData)
            "
            type="primary"
            @click="operate('priceSalesOrderPage', {})"
          >
            {{ $t("receipt.priceRevision") }}
          </el-button> -->
            <el-divider
              class="ml12 mr12"
              direction="vertical"
              v-if="infoData?.returnState?.value !== 'ALL_RETURN'"
            ></el-divider>
          </template>
          <!-- 已拒绝 -->
          <template v-else-if="infoData?.examineState?.code === 3">
            <el-button type="primary" @click="operate('addSalesOrderPage', {})">
              {{ $t("receipt.resubmit") }}
            </el-button>
            <el-button
              type="danger"
              @click="
                operate('delDialog', {
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
            <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          </template>
          <el-button plain @click="onClose">
            {{ $t("receipt.return") }}
          </el-button>
        </template>
      </div>
    </template>

    <!-- 价格修改记录 -->
    <price-change-record-dialog
      v-if="operateDialog.type === 'priceChangeRecordDialog'"
      v-model:show="operateDialog.show"
      :orderNo="props.data.orderNo"
      :title="$t('receipt.priceChangeRecord')"
      @close="operateDialog.show = false"
    ></price-change-record-dialog>

    <!-- 撤回提示 -->
    <stop-dialog
      v-if="
        operateDialog.type === 'delDialog' ||
        operateDialog.type === 'returnDialog'
      "
      :title="operateDialog.title"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
    ></stop-dialog>

    <!-- 审核 -->
    <audit-dialog
      v-if="operateDialog.type === 'auditDialog'"
      :title="operateDialog.title"
      v-model:show="operateDialog.show"
      @confirm="onAudit"
    ></audit-dialog>
  </el-drawer>
</template>

<script lang="ts" setup>
import emptyBox from "@/components/empty/index.vue";
import { ref, defineEmits, defineProps, watch, reactive, computed } from "vue";
import { ElMessage, dayjs } from "element-plus";
import auditDialog from "@views/salesOrderManage/components/audit-dialog.vue";
import stopDialog from "@/components/dialog/delete.vue";
import PriceChangeRecordDialog from "@views/salesOrderManage/components/price-change-record-dialog.vue";
import {
  deleteOrder,
  getExamineFail,
  getExamineSuccess,
  getExamineWithdraw,
  getOrderInfoDetail,
} from "@/api/salesOrderManage";
import { judgeStatusColorOpts } from "@views/salesOrderManage/ts/enum";
import { add, formatMoney, multiply, round, subtract } from "@/util/numberUtil";
import { nextTick } from "vue";
import lang from "@/lang/index";
import store from "@/store";
import router from "@/router";
import { previewImageViewer } from "@/util/util";

const i18n = lang.global;

const emit = defineEmits(["close", "refresh", "update:show"]);
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => {
      return {};
    },
  },
  show: {
    type: Boolean,
    required: true,
  },
  /**不显示操作按钮 */
  noButton: {
    type: Boolean,
    default: false,
  },
});
const activeNames = ref([]);
const infoData: any = ref({});
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});
const loading = ref(false);
const productList = ref([]);
const giftsList = ref([]);
const paymentList = ref([]);

const discountActivity = computed(() => {
  return (infoData.value?.discountList || []).find((el) =>
    ["BIND_DISCOUNT", "GROUP_BUYING"].includes(el.discountType?.value)
  );
});

const activityList = computed<any>(() => {
  const obj = {};
  productList.value.forEach((el) => {
    const activity = (infoData.value?.discountList || []).find(
      (c) => c.groupBuyingId + "" === el.marketingActivityId + ""
    );
    if (activity) {
      obj[el.marketingActivityId] = obj[el.marketingActivityId] || [];
      obj[el.marketingActivityId].push({ activity, ...el });
    }
  });
  const list = [];
  for (const key in obj) {
    const list2 = [];
    (obj[key] || []).forEach((item) => {
      const c = list2.findIndex((c) => c.skuId === item.skuId);
      if (c !== -1) {
        list2[c].quantity = add(
          Number(list2[c].quantity || 0),
          Number(item.quantity || 0)
        );
        list2[c].originalTotalPrice = add(
          Number(list2[c].originalTotalPrice || 0),
          Number(item.originalTotalPrice || 0)
        );
        list2[c].discountTotalPrice = add(
          Number(list2[c].discountTotalPrice || 0),
          Number(item.discountTotalPrice || 0)
        );
      } else {
        list2.push(item);
      }
    });

    list.push(list2);
  }
  return list;
});

const noActivityProduct = computed(() => {
  return productList.value.filter((el) => !el.marketingActivityId);
});

watch(
  [() => props.show, () => props.data.orderNo],
  (val) => {
    // 获取销售单详情
    if (val[1] && val[0]) {
      nextTick(() => {
        getInfo();
        activeNames.value = [];
      });
    }
  },
  { immediate: true }
);

// 获取销售单详情
const getInfo = () => {
  loading.value = true;
  getOrderInfoDetail({
    orderNo: props.data.orderNo,
  })
    .then((res) => {
      loading.value = false;
      if (res.code == 200) {
        infoData.value = res.data;
        productList.value = infoData.value.skuList.filter((item: any) => {
          return item.isGiveaway.value === "NO";
        });
        giftsList.value = infoData.value.skuList.filter((item: any) => {
          return item.isGiveaway.value === "YES";
        });
        paymentList.value = (
          infoData.value.orderPaymentChannelBaseDTOList || []
        ).map((item) => {
          return {
            ...item,
            attachment: item.attachment ? JSON.parse(item.attachment) : [],
          };
        });
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

/**
 * 根据类型返回优惠信息字符串。
 *
 * @param {string} type - 优惠类型（exchange、max、discount、point）
 * @param {any} product - 商品对象
 * @param {boolean} isNumber - 是否返回数字（用于计算）
 * @return {string} 优惠信息字符串（例如“1”、“10.00”、“8折”等）
 */
const getString = (type: string, product: any, isNumber?: boolean) => {
  // 0: "COUPON_EXCHANGE", //"兑换券"
  // 1: "COUPON_DISCOUNT", //"折扣券"
  // 2: "COUPON_THRESHOLD_DISCOUNT", //"满减券"
  // 3: "REDEEM_POINTS", //"积分抵扣"
  if (product?.stockType?.value === "SECOND_HAND") {
    return type === "point" ? 0 : "-";
  }

  if (type === "exchange") {
    // 兑换数量
    let num = 0;
    (infoData.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
    });
    return num ? num : "-";
  } else if (type === "max") {
    // 满减
    let num = 0;
    let price = 0;
    (infoData.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
      if (item.discountType.value === "COUPON_THRESHOLD_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          price = round(
            subtract(
              Number(product.originalTotalPrice),
              Number(product.discountTotalPrice)
            )
          );
        }
      }
    });
    if (price) {
      price = round(price - multiply(num, Number(product.originalUnitPrice)));
    }

    return price ? `${formatMoney(price)}` : "-";
  } else if (type === "discount") {
    // 折扣
    let discountRate = 0;
    (infoData.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          discountRate = item.discountRate;
        }
      }
    });
    // return discountRate ? `${discountRate}${i18n.t("receipt.fracture")}` : "-";
    return discountRate ? `${discountRate}%` : "-";
  } else if (type === "point") {
    // 积分
    let pointsPrice = 0;
    (infoData.value.discountList || []).forEach((item: any) => {
      if (item.discountType.value === "REDEEM_POINTS") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          pointsPrice = item.pointsPrice;
        }
      }
    });
    return pointsPrice || 0;
  }
};

// 刷新
const refresh = () => {
  getInfo();
  emit("refresh");
};

// 操作
const operate = (type, val: any = {}) => {
  switch (type) {
    case "addSalesOrderPage":
    case "priceSalesOrderPage":
      onClose();
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          type: type === "addSalesOrderPage" ? "add" : "price",
          data: val?.data || infoData.value,
        })
      );
      router.push({
        path: "/salesOrderManage/add",
      });
      break;
    case "addReturnSalesOrder":
      onClose();
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          data: val?.data || infoData.value,
        })
      );
      router.push({
        path: "/returnOrderManage/add",
      });
      break;
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = val?.data || infoData.value;
      operateDialog.show = true;
      break;
  }
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "returnDialog") {
    getExamineWithdraw([data.id])
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          emit("close");
          refresh();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    deleteOrder({ id: data.id })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          emit("close");
          onClose();
          refresh();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

/**
 * @description: 审核
 * @param {Object} form - 表单数据
 * @param {String} form.text - 审核备注
 * @return {void} 无返回值
 */
const onAudit = (form) => {
  const data: any = operateDialog.data;
  // 审核通过
  if (operateDialog.title === i18n.t("receipt.auditPass")) {
    getExamineSuccess({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          refresh();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    getExamineFail({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          refresh();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

// 关闭
const onClose = () => {
  emit("update:show", false);
  emit("close");
};

// 检验审核权限
const checkAuth = (row: any) => {
  const personInfo = store.getters.personInfo;
  return personInfo.personId === row.examineId;
};

// 判断是否可以价格修订（ 使用优惠的不可以）
const checkPriceChange = (item: any) => {
  return !(
    Number(item.actualPrice) !== Number(item.originalPrice) &&
    (item.skuList || []).some((el: any) => el.isGiveaway?.value === "YES")
  );
};

function preview(row) {
  previewImageViewer([row.attachment[0]["fileUrl"]], 0);
}
</script>

<style lang="scss" scoped>
//.el-collapse-item__header
::v-deep .el-collapse {
  margin-bottom: 2px;
  border: none;

  .el-collapse-item {
    display: flex;
    flex-direction: column-reverse;
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(-90deg);
  }

  .el-collapse-item__wrap {
    border: none;

    .el-collapse-item__content {
      padding: 0;
    }
  }

  .el-collapse-item__header {
    height: 30px;
    border-radius: 4px;
    flex-direction: row-reverse;
    justify-content: flex-end;
    border: none;

    .el-collapse-item__arrow {
      margin-left: 10px;
    }
  }
}

.second-tag {
  font-size: 10px;
  color: #ff9900;
  padding: 1px 8px;
  border-radius: 4px;
  border: 1px solid #ff9900;
  margin-right: 8px;
  vertical-align: bottom;
}
</style>
