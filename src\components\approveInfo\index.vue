<template>
  <div>
    <p class="fs16 c_1D2330 lh24 fw600">审批信息</p>
    <el-timeline class="mt20">
      <el-timeline-item
        v-for="(activity, index) in flowList"
        :key="index"
        :type="activity.type"
        :icon="activity.icon"
        :color="activity.color"
        :hollow="activity.hollow"
        :size="activity.size">
        <p class="fs14 c_1D2330 fw600 lh22">{{ activity.nodeName }}
          <span v-if="activity.status == 0" class="fs14 c_FE9B0E">（待审核）</span>
          <span v-else-if="activity.status == 1" class="fs14 c_10D986">（已通过）</span>
          <span v-else-if="activity.status == 2" class="fs14 c_EC2D30">（已拒绝）</span>
        </p>
        <div v-if="activity.status == 2" class="pt8 pb8 pl16 pr16 bg_FFEAEA border_radius4 mt4">
          <div class="flex justify_content_between">
            <span class="c_1D2330 lh18">{{ activity.name }}（{{ activity.code }}）</span>
            <span class="c_4F5363 lh18">{{ activity.ctime }}</span>
          </div>
          <p class="lh18 c_EC2D30 mt8">{{ activity.reason }}</p>
        </div>
        <div v-else class="pt8 pb8 pl16 pr16 bgc_FAFBFC border_radius4 flex justify_content_between mt4">
          <span class="c_1D2330 lh18">{{ activity.name }}（{{ activity.code }}）</span>
          <span class="c_4F5363 lh18">{{ activity.ctime }}</span>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

const props = defineProps({
  list: {
    type: Array,
    required: true
  }
})

const flowList: any = computed(() => {
  const list: any = props.list
  list.forEach(el => {
    el.size = 'normal'
    if (!el.status && el.status != 0) {
      el.type = 'success'
      el.hollow = true
    } else if (el.status == 0) {
      el.type = 'info'
      el.hollow = true
    } else if (el.status == 1) {
      el.size = 'large'
      el.type = 'primary'
      el.icon = SuccessFilled
    } else if (el.status == 2) {
      el.size = 'large'
      el.type = 'danger'
      el.icon = CircleCloseFilled
    }
  })
  return list
})
</script>

<style lang="scss" scoped>
.c_10D986 {
  color: #10D986;
}
.c_FE9B0E {
  color: #FE9B0E;
}
.bg_FFEAEA {
  background-color: #FFEAEA;
}
::v-deep {
  .el-timeline-item__node--normal {
    left: 0px;
    --el-timeline-node-size-normal: 10px;
  }
  .el-timeline-item__node--success {
    --el-color-success: #10D986;
  }
  .el-timeline-item__node--primary {
    --el-color-primary: #ffffff;
    .el-timeline-item__icon {
      --el-color-white: #10D986;
    }
  }
  .el-timeline-item__node--danger {
    --el-color-danger: #ffffff;
    .el-timeline-item__icon {
      --el-color-white: #EC2D30;
    }
  }
  .el-timeline-item__node--large {
    .el-timeline-item__icon {
      font-size: 16px;
    }
  }
}
</style>