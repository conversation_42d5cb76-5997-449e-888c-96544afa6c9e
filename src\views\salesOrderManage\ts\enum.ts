import lang from "@/lang/index";

const i18n = lang.global;

// 入库单据审核状态,可用值:AUDIT,DRAFT,FINISH,REJECT
const statusOpts = [
  { id: "DRAFT", name: i18n.t("receipt.draft") },
  { id: "AUDIT", name: i18n.t("receipt.waitAudit") },
  { id: "REJECT", name: i18n.t("receipt.reject") },
  { id: "FINISH", name: i18n.t("receipt.finish") },
];

// 审核状态（0挂起，1审核中，2通过，3不通过）
const examineStateOpts = [
  { id: 0, name: i18n.t("receipt.pending"), key: "DRAFT" },
  { id: 1, name: i18n.t("receipt.reviewIng"), key: "AUDIT_NOW" },
  { id: 2, name: i18n.t("receipt.passed"), key: "AUDIT_SUCCESS" },
  { id: 3, name: i18n.t("receipt.rejected"), key: "AUDIT_FAIL" },
];
// 审核状态（0挂起，1审核中，2通过，3不通过）
const judgeStatusColorOpts = {
  0: "gray",
  1: "orange",
  2: "green",
  3: "red",
};

const discountType = {
  0: "COUPON_EXCHANGE", //"兑换券"
  1: "COUPON_DISCOUNT", //"折扣券"
  2: "COUPON_THRESHOLD_DISCOUNT", //"满减券"
  3: "REDEEM_POINTS", //"积分抵扣"
};

export { statusOpts, examineStateOpts, judgeStatusColorOpts, discountType };
