<script setup lang="ts">
import { defineProps } from "vue";
import { ArrowRight } from "@element-plus/icons-vue";

const props = defineProps({
  title: {
    type: String,
    default: () => {
      return "";
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div
    class="bgc_F5F7F9 common_bar_btn mb12"
    :class="props.disabled ? '' : 'pointer'"
  >
    <span class="flex align_items_center"
      ><span v-html="title"></span><slot name="label"></slot
    ></span>
    <div class="right-content">
      <slot name="right"></slot>
      <el-icon class="ml8 mr8 c_4F5363" v-if="!disabled">
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-switch) {
  height: 16px;

  &.is-checked{
    .el-switch__action{
      left: calc(100% - 13px);
    }
  }

  .el-switch__core {
    height: 16px;
    min-width: 30px;
  }

  .el-switch__action{
    height: 12px;
    width: 12px;
  }
  
}
</style>
