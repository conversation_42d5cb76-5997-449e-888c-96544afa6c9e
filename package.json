{"name": "subReceipt", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "build:prd": "vue-cli-service build --mode production", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@types/js-cookie": "^3.0.0", "@vue/cli": "^5.0.8", "@vueup/vue-quill": "^1.0.0-alpha.40", "ali-oss": "^6.17.1", "await-to-js": "^3.0.0", "axios": "^1.1.3", "core-js": "^3.6.5", "cos-js-sdk-v5": "^1.4.6", "echarts": "^5.4.2", "element-plus": "^2.4.3", "file-saver": "^2.0.5", "install": "^0.13.0", "js-audio-recorder": "^1.0.7", "js-base64": "^3.7.2", "js-cookie": "^3.0.1", "lamejs": "^1.2.1", "lodash": "^4.17.21", "moment": "^2.29.4", "npm": "^8.19.2", "nprogress": "^0.2.0", "number-precision": "^1.6.0", "pdfjs-dist": "^3.4.120", "pinia": "^2.0.33", "pinia-plugin-persist": "^1.0.0", "sass": "^1.55.0", "sortablejs": "^1.15.0", "vue": "^3.2.38", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.8", "vue-dragscroll": "^4.0.5", "vue-qr": "^4.0.9", "vue-quill-editor": "^3.0.6", "vue-router": "^4.0.0-0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "xlsx": "^0.16.0"}, "devDependencies": {"@types/ali-oss": "^6.16.4", "@types/vue-i18n": "^7.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-typescript": "^7.0.0", "compression-webpack-plugin": "^6.0.0", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "sass-loader": "^8.0.2", "typescript": "^5.6.2", "vue-i18n": "^11.0.1"}}