/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from "axios";
import store from "@/store/";
import { serialize } from "@/util/util";
import { getToken } from "@/util/auth";
import { ElMessage } from "element-plus";
import website from "@/config/website";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { workbenchUrl, logOutPage } from "@/config/env";

axios.defaults.timeout = 100000;
//返回其他状态码
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500;
};

// NProgress Configuration
NProgress.configure({
  showSpinner: false,
});

//HTTPrequest拦截
axios.interceptors.request.use(
  (config) => {
    NProgress.start(); // start progress bar
    const meta = config.meta || {};
    const isToken = meta.isToken === false;
    if (getToken() && !isToken) {
      config.headers.Authorization = getToken();
    }
    //headers中是否要添加headerData
    if (meta.headerData) {
      config.headers.buIds = meta.headerData;
    }
    if (meta.headers) {
      config.headers["Content-Type"] = meta.headers;
    }
    //headers中配置serialize为true开启序列化
    if (config.method === "post" && meta.isSerialize === true) {
      config.data = serialize(config.data);
    }
    if (config.method === "get" && config.params) {
      let url = config.url;
      url += "?";
      const keys = Object.keys(config.params);
      for (const key of keys) {
        if (config.params[key] == undefined || config.params[key] == null) {
          config.params[key] = "";
        }
        url += `${key}=${encodeURIComponent(config.params[key])}&`;
      }
      url = url?.substring(0, url.length - 1);
      config.params = {};
      config.url = url;
    }

    if (config.method === "post" && config.params) {
      let url = config.url;
      url += "?";
      const keys = Object.keys(config.params);
      for (const key of keys) {
        url += `${key}=${encodeURIComponent(config.params[key])}&`;
      }
      url = url?.substring(0, url.length - 1);
      config.params = {};
      config.url = url;
    }

    //多语言传参
    if (config.params) {
      config.params.lang = store.getters.language;
      config.params.langIndex = store.getters.langIndex;
    } else {
      config.params = { lang: store.getters.language };
      config.params.langIndex = store.getters.langIndex;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

//HTTPresponse拦截
axios.interceptors.response.use(
  (res) => {
    NProgress.done();
    const status = Number(res.status) || 200;
    const statusWhiteList = website.statusWhiteList || [];
    const message = res.data.message || res.data.msg || "未知错误";
    //如果在白名单里则自行catch逻辑处理
    if (statusWhiteList.includes(status)) return Promise.reject(res);
    //如果是401则跳转到登录页面
    if (status === 401) {
      store.dispatch("user/FedLogOut").then(() => {
        window.location.href = workbenchUrl + "/login" + logOutPage();
        // router.push("/login");
      });
    }
    if (status !== 200) {
      if (status !== 401) {
        ElMessage({
          message: message,
          type: "error",
        });
        return Promise.reject(new Error(message));
      }
    }
    return res.data;
  },
  (error) => {
    NProgress.done();
    return Promise.reject(new Error(error));
  }
);

export default axios;
