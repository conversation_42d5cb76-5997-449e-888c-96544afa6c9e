/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-13 11:38:15
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-21 10:29:09
 * @FilePath: \flb-receipt\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import PageRouter from "./page";
import salesOrderManage from "@/router/views/salesOrderManage";
import roleManage from "./views/roleManage";
import accountManage from "./views/accountManage";
import naviConfig from "./views/naviConfig";
import bomConfig from "./views/bomConfig";
import systemManage from "@/router/views/systemManage";
import discountRole from "./views/discountRole";
import cashierDayily from "./views/cashierDayily";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/jumpLoading",
    name: "Loading",
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      i18n: "jumpLoading",
    },
    component: () => import("@/page/loading/jumpLoading.vue"),
  },
  ...PageRouter,
  ...(salesOrderManage as any),
  ...roleManage,
  ...accountManage,
  ...naviConfig,
  ...bomConfig,
  ...systemManage,
  ...discountRole,
  ...cashierDayily,
];
const router = createRouter({
  history: createWebHashHistory(
    (window as any).__POWERED_BY_QIANKUN__ ? "/#/" : "/"
  ),
  routes,
});
export default router;
