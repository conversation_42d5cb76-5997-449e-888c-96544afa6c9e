import {
  setStore,
  getStore,
  removeStore
} from '@/util/store'
import website from '@/config/website'

const common = {
  namespaced: true,
  state: {
    language: getStore({ name: 'language' }) || 'zh',
    langIndex: getStore({ name: 'langIndex' }) || 1,
    isCollapse: false, //控制左侧菜单展开收起的变量
    isFullScren: false, //是否全屏展示
    isMenu: true,
    screen: -1,
    isLock: getStore({ name: 'isLock' }) || false,//是否激活锁屏
    showTag: true,//是否显示页签tags
    showDebug: false, //是否显示debug按钮
    showCollapse: true,//是否显示顶部的收起菜单按钮
    showSearch: false,//是否显示顶部搜索框
    showLock: false,//是否显示顶部锁
    showFullScren: false,//是否显示顶部全屏按钮
    showTheme: true,//是否
    showMenu: true,//是否显示顶部菜单
    showColor: false,//是否显示顶部颜色主题
    themeName: getStore({ name: 'themeName' }) || 'theme-default',//主题设置
    lockPasswd: getStore({ name: 'lockPasswd' }) || '',
    website: website,
    topBarHeight: (window as any).__POWERED_BY_QIANKUN__ ? 52 : 0,//顶部菜单高度
  },

  actions: {
  },

  mutations: {
    SET_LANGUAGE: (state: any, language: any) => {
      state.language = language
      setStore({
        name: 'language',
        content: state.language
      })
    },
    SET_LANGUAGEINDEX: (state: any, langIndex: any) => {
      state.langIndex = langIndex
      setStore({
        name: 'langIndex',
        content: state.langIndex
      })
    },
    SET_COLLAPSE: (state: any) => {
      state.isCollapse = !state.isCollapse;
      if (state.isCollapse) {
        document.body.classList.add("avue--collapse");
      } else {
        document.body.classList.remove("avue--collapse");
      }
    },
    SET_IS_MENU: (state: any, menu: any) => {
      state.isMenu = menu;
    },
    SET_FULLSCREN: (state: any) => {
      state.isFullScren = !state.isFullScren;
    },
    SET_LOCK: (state: any) => {
      state.isLock = true;
      setStore({
        name: 'isLock',
        content: state.isLock,
        type: 'session'
      })
    },
    SET_SCREEN: (state: any, screen: any) => {
      state.screen = screen;
    },
    SET_THEME_NAME: (state: any, themeName: any) => {
      state.themeName = themeName;
      setStore({
        name: 'themeName',
        content: state.themeName,
      })
    },
    SET_LOCK_PASSWD: (state: any, lockPasswd: any) => {
      state.lockPasswd = lockPasswd;
      setStore({
        name: 'lockPasswd',
        content: state.lockPasswd,
        type: 'session'
      })
    },
    CLEAR_LOCK: (state: any) => {
      state.isLock = false;
      state.lockPasswd = '';
      removeStore({
        name: 'lockPasswd',
        type: 'session'
      });
      removeStore({
        name: 'isLock',
        type: 'session'
      });
    }
  }
}
export default common