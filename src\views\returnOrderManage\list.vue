<template>
  <div
    v-loading="loading"
    class="p24 bgc_white position_re"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <!-- 筛选 -->
    <div class="flex flex_direction_column">
      <div>
        <search-box
          :modelValue="filterObj.searchStr"
          :placeholder="$t('receipt.searchPlaceholder')"
          @onClear="
            filterObj.searchStr = '';
            onFilter();
          "
          @onInput="(value) => (filterObj.searchStr = value)"
          @onSearch="onFilter"
        ></search-box>
        <div class="flex justify_content_between mt8">
          <div>
            <!-- 单据状态 -->
            <el-select
              v-model="filterObj.examineState"
              class="width168"
              :placeholder="$t('receipt.orderStatus')"
              size="default"
              @change="filterChange('examineState', $t('receipt.orderStatus'))"
            >
              <el-option
                v-for="item in examineStateOpts.filter((el) => el.id !== 0)"
                :key="item.id"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
            <!-- 开单日期 -->
            <el-date-picker
              v-model="filterObj.orderDate"
              class="ml8"
              :placeholder="$t('receipt.orderDate')"
              size="default"
              style="width: 168px"
              type="date"
              value-format="YYYY-MM-DD"
              @change="filterChange('orderDate', $t('receipt.orderDate'))"
            />
          </div>
          <div class="flex_shrink0">
            <el-button @click="operate('draftBox')">
              <i class="iconfont-flb icon-caogao mr4"></i>
              {{ $t("receipt.draft") }}
              &nbsp;&nbsp;({{ draftNum }})
            </el-button>
            <el-button @click="onExport">
              <i class="iconfont-flb icon-daochu-shangchuan mr2"></i>
              {{ $t("receipt.export") }}
            </el-button>
            <el-button
              type="primary"
              @click="operate('addReturnSalesOrder', { data: {} })"
            >
              <i class="iconfont-flb icon-add mr2"></i>
              {{ $t("receipt.addReturnOrder") }}
            </el-button>
          </div>
        </div>
        <filter-item
          :filterItemList="filterList"
          class="mt8"
          @onDel="delFilterItem"
        ></filter-item>
      </div>
    </div>

    <!-- 表格 -->
    <div class="mt16" ref="tableRef">
      <el-table
        :data="tableData"
        :height="tableHeight"
        border
        class="custom_radius_table"
        size="small"
        style="width: 100%"
      >
        <!-- 退货单 -->
        <el-table-column
          :label="$t('receipt.returnOrder')"
          min-width="200"
          prop="orderNo"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="fw600 pointer main-color"
              @click="
                operate('detailsDrawer', {
                  data: scope.row,
                })
              "
            >
              {{ scope.row.orderNo }}
            </span>
          </template>
        </el-table-column>
        <!-- 关联销售单 -->
        <el-table-column
          :label="$t('receipt.relatedSalesOrder')"
          min-width="128"
          prop="saleOrderNo"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
        </el-table-column>
        <!-- 退货日期 -->
        <el-table-column
          :label="$t('receipt.returnDate')"
          min-width="128"
          prop="orderDate"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ dayjs(scope.row.ctime).format("YYYY/MM/DD") }}</span>
          </template>
        </el-table-column>
        <!-- 会员 -->
        <el-table-column
          :label="$t('receipt.member')"
          min-width="128"
          prop="memberName"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <!-- 门店 -->
        <el-table-column
          :label="$t('receipt.store')"
          min-width="200"
          prop="inTime"
          :formatter="tableFormat"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.storeName?.value || "-" }}
          </template>
        </el-table-column>
        <!-- 退货金额 -->
        <el-table-column
          :label="$t('receipt.returnAmount')"
          min-width="128"
          prop="returnPrice"
          align="right"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="fw500 c_1D2330">{{ formatMoney(scope.row.returnPrice) }}</span>
          </template>
        </el-table-column>
        <!-- 销售人员 -->
        <el-table-column
          :label="$t('receipt.salePerson')"
          min-width="128"
          prop="salespersonName"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <!-- 审核人 -->
        <el-table-column
          :label="$t('receipt.reviewer')"
          min-width="128"
          prop="examineName"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <!-- 备注 -->
        <el-table-column
          :label="$t('receipt.remark')"
          min-width="188"
          prop="orderRemarks"
          :formatter="tableFormat"
          show-overflow-tooltip
        />
        <!-- 审核状态 -->
        <el-table-column
          :label="$t('receipt.auditStatus')"
          min-width="95"
          prop="examineState"
          fixed="right"
        >
          <template #default="scope">
            <span
              :class="judgeStatusColorOpts[scope.row.examineState.code] || ''"
              class="common-status-box"
            >
              {{
                filterNameById(scope.row.examineState.code, examineStateOpts)
              }}
            </span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          fixed="right"
          :label="$t('receipt.operation')"
          width="164"
          class-name="operate_column"
        >
          <template #default="scope">
            <!-- 审核中 -->
            <template v-if="scope.row.examineState.code === 1">
              <template v-if="checkAuth(scope.row)">
                <el-button
                  link
                  type="primary"
                  @click="
                    operate('auditDialog', {
                      data: scope.row,
                      title: $t('receipt.auditPass'),
                    })
                  "
                >
                  {{ $t("receipt.pass") }}
                </el-button>
                <el-divider class="ml8 mr8" direction="vertical"></el-divider>
                <el-button
                  link
                  type="danger"
                  @click="
                    operate('auditDialog', {
                      data: scope.row,
                      title: $t('receipt.auditReject'),
                    })
                  "
                >
                  {{ $t("receipt.reject") }}
                </el-button>
                <el-divider class="ml8 mr8" direction="vertical"></el-divider>
              </template>
              <el-button
                link
                type="danger"
                @click="
                  operate('returnDialog', {
                    data: scope.row,
                    title: $t('receipt.recallPrompt'),
                    content: $t('receipt.recallPromptContent'),
                  })
                "
              >
                {{ $t("receipt.recall") }}
              </el-button>
            </template>
            <!-- 已通过 -->
            <template v-else-if="scope.row.examineState.code === 2">
              <span>-</span>
            </template>
            <!-- 已拒绝 -->
            <template v-else-if="scope.row.examineState.code === 3">
              <el-button
                link
                type="primary"
                @click="
                  operate('addReturnSalesOrder', {
                    data: scope.row,
                  })
                "
              >
                {{ $t("receipt.resubmit") }}
              </el-button>
              <el-divider class="ml8 mr8" direction="vertical"></el-divider>
              <el-button
                link
                type="danger"
                @click="
                  operate('delDialog', {
                    data: scope.row,
                    title: $t('receipt.deletePrompt'),
                    content: $t('receipt.deletePromptContent'),
                  })
                "
              >
                {{ $t("receipt.delete") }}
              </el-button>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>

      <pagination
        :pageObj="pageObj"
        @currentChange="currentChange"
        @onPageChange="onPageChange"
      ></pagination>
    </div>

    <!-- 审核 -->
    <audit-dialog
      v-if="operateDialog.type === 'auditDialog'"
      :title="operateDialog.title"
      v-model:show="operateDialog.show"
      @confirm="onAudit"
      :loading="dialogLoading"
    ></audit-dialog>

    <!-- 撤回提示 -->
    <stop-dialog
      v-if="
        operateDialog.type === 'delDialog' ||
        operateDialog.type === 'returnDialog'
      "
      :title="operateDialog.title"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
    ></stop-dialog>

    <!-- 退货单详情 -->
    <details-drawer
      v-if="operateDialog.type === 'detailsDrawer'"
      :data="operateDialog.data"
      v-model:show="operateDialog.show"
      @refresh="getList"
    ></details-drawer>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onActivated,
  onMounted,
  onUnmounted,
  reactive,
  ref,
} from "vue";
import searchBox from "@/components/filter/search.vue";
import stopDialog from "@/components/dialog/delete.vue";
import { ElMessage, dayjs } from "element-plus";
import emptyBox from "@/components/empty/index.vue";
import {
  filterByKeyValue,
  filterNameById,
  splicingUrl,
} from "@/hooks/publicMethod";
import pagination from "@/components/pagination/index.vue";
import {
  downloadExcel,
  getFormatCurDate,
  getObjType,
  tableFormat,
} from "@/util/util";
import { formatMoney } from "@/util/numberUtil";
import filterItem from "@/components/filter/filterItem.vue";
import detailsDrawer from "./components/details-drawer.vue";
import {
  judgeStatusColorOpts,
  examineStateOpts,
} from "../salesOrderManage/ts/enum";
import { baseUrl } from "@/config/env";
import auditDialog from "@views/salesOrderManage/components/audit-dialog.vue";
import { getSalesReturnApi } from "@/api/salesReturnOrder";
import {
  examineFail,
  examineSuccess,
  withdraw,
  deleteOrderReturn,
  exportReturnOrder,
} from "@/api/returnOrderManage";
import store from "@/store";
import lang from "@/lang/index";
import { debounce } from "lodash";
import router from "@/router";

const i18n = lang.global;
const tableRef = ref(null);
const filterObj = reactive({
  searchStr: "",
  orderDate: "",
  examineState: "",
});
const pageObj = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0,
});
const filterList = ref([]);
const loading = ref(false);
const tableData = ref([]);
const tableHeight = ref(0);
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});
const draftNum = ref(0);
const dialogLoading = ref(false);
const storeList = computed(() => {
  const list = store.getters.storeList;
  return list;
});

onActivated(() => {
  getList();
});

onMounted(() => {
  window.addEventListener("resize", debounce(getTableHeight, 0));
  getTableHeight();
  getList();
});

onUnmounted(() => {
  window.removeEventListener("resize", debounce(getTableHeight, 0));
});

const getTableHeight = () => {
  tableHeight.value =
    window.innerHeight - 168 - (filterList.value.length ? 28 : 0) - 58;
};

// 获取表格数据
const getList = () => {
  loading.value = true;
  const params = Object.assign({}, pageObj, filterObj) as any;
  if (!params.examineState) {
    params.examineStateList = ["AUDIT_NOW", "AUDIT_SUCCESS", "AUDIT_FAIL"];
  }
  getSalesReturnApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data.records || [];
        pageObj.total = Number(res.data.total || 0);
        tableData.value = list;
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });

  params.examineStateList = ["DRAFT"];
  getSalesReturnApi(params).then((res) => {
    if (res.code == 200) {
      draftNum.value = Number(res.data.total || 0);
    } else {
      draftNum.value = 0;
    }
  });
};

// size改变
const onPageChange = (pageSize) => {
  pageObj.pageSize = pageSize;
  pageObj.pageNo = 1;
  getList();
};

// 翻页
const currentChange = (page) => {
  pageObj.pageNo = page;
  getList();
};

// 筛选
const onFilter = () => {
  getTableHeight();
  pageObj.pageNo = 1;
  getList();
};

// 筛选项变化
const filterChange = (key, label) => {
  const item = { key: key, value: "" };
  if (key === "storeCode") {
    item.value = `${label}：${filterByKeyValue({
      labelKey: "name",
      valueKey: "code",
      value: filterObj[key],
      option: storeList.value,
    })}`;
  } else if (key === "examineState") {
    item.value = `${label}：${filterByKeyValue({
      labelKey: "name",
      valueKey: "key",
      option: examineStateOpts,
      value: filterObj[key],
    })}`;
  } else if (key === "orderDate") {
    item.value = `${label}：${dayjs(filterObj.orderDate).format("YYYY/MM/DD")}`;
  }
  const index = filterList.value.findIndex((el) => el.key === key);
  if (index == -1) {
    filterList.value.push(item);
  } else {
    // 删除
    const valType = getObjType(filterObj[key]);
    if (
      (valType != "array" &&
        (filterObj[key] === null ||
          filterObj[key] === undefined ||
          filterObj[key] === "")) ||
      (valType === "array" && !filterObj[key].length)
    ) {
      filterList.value.splice(index, 1);
    } else {
      filterList.value.splice(index, 1, item);
    }
  }
  onFilter();
};

// 删除筛选项
const delFilterItem = (key) => {
  if (key) {
    const valType = getObjType(filterObj[key]);
    if (valType === "array") {
      filterObj[key] = [];
    } else {
      filterObj[key] = "";
    }
    filterList.value = filterList.value.filter((e) => e.key !== key);
  } else {
    for (let i = 0; i < filterList.value.length; i++) {
      const item = filterList.value[i];
      const valType = getObjType(filterObj[item.key]);
      if (valType === "array") {
        filterObj[item.key] = [];
      } else {
        filterObj[item.key] = "";
      }
    }
    filterList.value = [];
  }
  onFilter();
};

// 导出
const onExport = () => {
  exportReturnOrder(
    Object.assign({}, pageObj, {
      ...filterObj,
    })
  ).then((res: any) => {
    downloadExcel(
      res,
      `${i18n.t("receipt.returnOrder")}-${getFormatCurDate()}` + ".xlsx"
    );
  });
};

/**
 * @description: 审核
 * @param {Object} form - 表单数据
 * @param {String} form.text - 审核备注
 * @return {void} 无返回值
 */
const onAudit = (form) => {
  const data: any = operateDialog.data;
  if (operateDialog.title === i18n.t("receipt.auditPass")) {
    dialogLoading.value = true;
    examineSuccess({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        dialogLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        dialogLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    dialogLoading.value = true;
    examineFail({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        dialogLoading.value = false;
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        dialogLoading.value = false;
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

const operate = (type, val: any = {}) => {
  switch (type) {
    case "addReturnSalesOrder":
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          data: val.data as any,
        })
      );
      router.push({
        path: "/returnOrderManage/add",
      });
      break;
    case "draftBox":
      router.push({
        path: "/returnOrderManage/draftBox",
      });
      break;
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = val.data || {};
      operateDialog.show = true;
      break;
  }
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "returnDialog") {
    withdraw([data.id])
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    deleteOrderReturn({ id: data.id })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          getList();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

// 检验审核权限
const checkAuth = (row: any) => {
  const personInfo = store.getters.personInfo;
  return personInfo.personId === row.examineId;
};
</script>

<style lang="scss" scoped></style>
