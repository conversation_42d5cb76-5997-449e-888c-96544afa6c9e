<template>
  <el-input
    v-model="searchStr"
    :placeholder="props.placeholder || $t('receipt.place')"
    :class="{ no_sel_type: !props.isSelType }"
    :style="{ width: props.inputWidth }"
    siz="default"
    @blur="inputBlur"
    @input="inputChange"
    @keyup.enter="onSearch"
  >
    <template #prepend v-if="props.isSelType">
      <el-select
        v-model="typeVal"
        :placeholder="props.typePlaceholder"
        @change="typeChange"
      >
        <el-option
          v-for="(item, index) of typeOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
    <template #prefix v-else>
      <i class="iconfont-flb icon-sousuo c_1D2330"></i>
    </template>
    <template #suffix>
      <span class="lh36 pr8 flex align_items_center" v-if="searchStr">
        <i
          class="iconfont-flb icon-qingkong c_959AA5 pointer"
          @click="onClear"
        ></i>
        <i
          class="iconfont-flb icon-chufasousuo ml12 pointer c_989DA7"
          @click="onSearch"
        ></i>
      </span>
    </template>
  </el-input>
</template>

<script setup lang="ts">
import {
  defineProps,
  computed,
  defineEmits,
  ref,
  defineExpose,
  watch,
} from "vue";

const props = defineProps({
  modelValue: {
    type: String,
    default: () => "",
  },
  placeholder: {
    type: String,
    default: () => "",
  },
  isSelType: {
    type: Boolean,
    default: () => false,
  },
  typeValue: {
    type: Number,
    default: () => 1,
  },
  searchType: {
    type: String,
    default: () => "",
  },
  typeOptions: {
    type: Array,
    default: () => [],
  },
  typePlaceholder: {
    type: String,
    default: () => "",
  },
  inputWidth: {
    type: String,
    default: () => "344px",
  },
});
const typeOptions: any = computed(() => {
  return props.typeOptions;
});
const searchStr = ref(props.modelValue);
const typeVal = ref(props.typeValue);

const emit = defineEmits([
  "onInput",
  "onSearch",
  "onClear",
  "typeChange",
  "onBlur",
]);

watch(
  () => props.modelValue,
  (val) => {
    searchStr.value = val;
  },
  { immediate: true }
);

const inputChange = (value) => {
  emit("onInput", value);
};
const typeChange = (value) => {
  emit("typeChange", value);
};
const onSearch = () => {
  emit("onSearch", searchStr.value);
};
const onClear = () => {
  searchStr.value = "";
  emit("onClear");
};
const inputBlur = () => {
  emit("onBlur", searchStr.value);
};

defineExpose({ onClear });
</script>

<style lang="scss" scoped>
.icon-chufasousuo:hover {
  color: #0054ff;
}
.icon-sousuo,
.icon-chufasousuo {
  font-size: 18px !important;
}
.icon-qingkong {
  font-size: 12px !important;
}

::v-deep {
  .el-input-group__prepend {
    padding: 0 12px;
    border-radius: 8px 0 0 8px;
    background-color: #ffffff;
    .el-select {
      width: 72px;
      box-sizing: border-box;
      margin: -10px -12px;
    }
    .el-input__suffix {
      right: 4px;
    }
    .el-input__inner {
      padding: 0 12px;
      border-color: transparent !important;
    }
  }
  .el-input__prefix,
  .el-input__suffix {
    height: 26px;
  }

  .el-input__inner {
    height: 26px !important;
    line-height: 26px !important;

    &::placeholder {
      font-weight: 400;
    }
  }
}
</style>
