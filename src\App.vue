<template>
  <el-config-provider :locale="elementLang">
    <div id="receipt-app">
      <router-view v-if="judgeRoute()"></router-view>
      <index v-else></index>
    </div>
  </el-config-provider>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import index from "@/page/index/index.vue";
import { getStore } from "@/util/store";

const route = useRoute();

//获取多语言
const language = getStore({ name: "language" }) || "zh";
const langIndex = getStore({ name: "langIndex" }) || 1;
const elementLang = {
  ...require(`element-plus/es/locale/lang/${
    language == "zh" ? "zh-cn" : language
  }`).default,
};

const judgeRoute = () => {
  const routeList = [
    "/login",
    "/forgetPwd",
    "/403",
    "/404",
    "/cashierDayily/saleOrderDetail",
    "/cashierDayily/saleProductDetail",
  ];
  return routeList.includes(route.path);
};
</script>

<style lang="scss">
#receipt-app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>
