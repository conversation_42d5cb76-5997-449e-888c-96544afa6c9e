import OSS from 'ali-oss'
import { apiGetAliOssSTS } from '@/api/common'
import { getObjType } from "@/util/util";

interface Currency {
  [key: string]: any
}

/**
 * fileObj
 * {
 *  [moduleId] {String}：所开发项目的moduleId，后端提供给前端（必传）
 *  [uploadType] {String}：上传的类型，后端提供给前端（必传）
 *  [file] {object}：file文件对象或者是elementUi的文件对象（必传）
 *  [packageName] {String}：传apk的时候需要传包名
 *  [fileType] {array}：可以上传的文件类型
 *  [maxSize] {Number}：文件大小限制（KB)
 *  [buId] {Number}：事业线
 *  [isMultipart] {Boolean}：是否使用分片上传
 *  [progressCallBack] {Function}:获取上传进度的回调
 * }
 */

//上传前校验（校验时可不传moduleId和uploadType）
export function uploadCheck (fileObj: Currency) {

  const checkResult = {
    checkType: true,
    msg: ''
  };

  if (!fileObj || !fileObj.file) return checkResult;

  //校验文件格式    
  if (fileObj.fileType && fileObj.fileType.length) {
    const extension = fileObj.file.type.split("/")[1];
    if (fileObj.fileType.indexOf(extension) < 0) {
      checkResult.checkType = false;
      checkResult.msg = '上传的文件格式不正确！';
      return checkResult;
    }
  }

  //校验文件大小    
  if (fileObj.maxSize) {
    const checkType = fileObj.file.size / 1024 < fileObj.maxSize;
    if (!checkType) {
      checkResult.checkType = false;
      // checkResult.msg = "文件大小不能超过" + fileObj.maxSize + "K！";
      checkResult.msg = "文件大小不能超过" + fileObj.maxSize/1024 + "mb！";
      return checkResult;
    }
  }

  return checkResult;

}

//阿里云上传
export async function upload_ali (fileObj: Currency) {

  if (!fileObj) {
    throw new Error('请传入要上传的文件！');
  }

  if (!fileObj.moduleId) {
    throw new Error('请传入moduleId！');
  }

  if (!fileObj.uploadType) {
    throw new Error('请传入uploadType！');
  }

  if (!fileObj.file) {
    throw new Error('请传入要上传的文件！');
  }

  //获取文件对象和文件名称
  const fileData = fileObj.file;
  const fileName = fileObj.file.name;

  //校验
  if (!uploadCheck(fileObj).checkType) {
    throw new Error(uploadCheck(fileObj).msg);
  }

  //封装获取签名的参数
  const sginParam: Currency = {
    'type': fileObj.uploadType,
    'moduleId': fileObj.moduleId
  }
  if (fileObj.packageName) {
    sginParam.packageName = fileObj.packageName;
  }

  try {

    //获取oss签名
    const stsResult: any = await apiGetAliOssSTS(sginParam);

    //校验获取签名是否成功
    if(stsResult.code != 200){
      throw new Error(stsResult.msg);
    }

    //构建上传对象
    const client = new OSS({
      accessKeyId: stsResult.data.accessKeyId, //通过阿里云控制台创建的AccessKey
      accessKeySecret: stsResult.data.accessKeySecret, //通过阿里云控制台创建的AccessSecret
      stsToken: stsResult.data.securityToken,
      region: stsResult.data.endpoint.replace(".aliyuncs.com",""), //bucket所在的区域， 默认oss-cn-hangzhou-国内
      bucket: stsResult.data.bucketName //通过控制台或PutBucket创建的bucket
    });

    //拼接上传的路径名
    const storeAs = stsResult.data.path + Date.parse(new Date().toString()) + '/' + fileName;

    //进行上传
    let putResult: any = {}
    if(fileObj.isMultipart){
      //分片上传
      putResult = await client.multipartUpload(storeAs, fileData,{
        progress: (p: any) => {
          if(fileObj.progressCallBack){
            fileObj.progressCallBack(p.toFixed(2))
          }
        }
      });
    }else{
      //普通上传
      // putResult = await client.put(storeAs, fileData,{
      //   progress: (p: any) => {
      //     if(fileObj.progressCallBack){
      //       fileObj.progressCallBack(p.to)
      //     }
      //   }
      // });
      putResult = await client.put(storeAs, fileData)
    }

    //校验是否上传成功
    if(putResult.res && putResult.res.statusCode != 200){
      throw new Error('上传失败！')
    }

    //上传成功返回上传结果
    putResult.fileName = fileName;
    putResult.fileUrl = stsResult.data.cdnDomain + putResult.name;

    return putResult;

  } catch (e) {
    throw new Error('网络请求超时！')
  }

}

/**
 *  [fileUrl] {String}：要删除的文件的文件地址
 *  [moduleId] {String}：所开发项目的moduleId，后端提供给前端（必传）
 *  [type] {String}：上传的类型，后端提供给前端（必传）
 */
//阿里云删除文件
export async function fileDelete_ali (fileUrl: any, moduleId: string, type: string) {

  //校验
  if (!fileUrl) {
    throw new Error("没有要删除的文件路径");
  }

  try {
    //获取oss签名
    const stsResult = await apiGetAliOssSTS({moduleId,type});

    //构建上传对象
    const client = new OSS({
      accessKeyId: stsResult.data.accessKeyId, //通过阿里云控制台创建的AccessKey
      accessKeySecret: stsResult.data.accessKeySecret, //通过阿里云控制台创建的AccessSecret
      stsToken: stsResult.data.securityToken,
      region: stsResult.data.endpoint.replace(".aliyuncs.com",""), //bucket所在的区域， 默认oss-cn-hangzhou-越南正式
      bucket: stsResult.data.bucketName //通过控制台或PutBucket创建的bucket
    });

    let result = {};


    if (getObjType(fileUrl) == "array") {//批量删除
      fileUrl.forEach((item: Currency) => {
        item.replace(stsResult.data.cdnDomain, '');
      });
      result = await client.deleteMulti(fileUrl);
    } else {//单个删除
      fileUrl = fileUrl.replace(stsResult.data.cdnDomain, '');
      result = await client.delete(fileUrl);
    }

    return result;

  } catch (e) {
    throw new Error('网络请求超时！')
  }

}