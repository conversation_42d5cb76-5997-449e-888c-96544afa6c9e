/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-07-21 09:56:43
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-23 17:21:24
 * @FilePath: \flb-receipt\src\router\views\cashierDayily.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default [
  {
    path: "/cashierDayily",
    children: [
      {
        path: "list",
        name: "收银日报",
        meta: {
          i18n: "cashierDayily",
        },
        component: () => import("@/views/cashierDayily/list.vue"),
      },
      {
        path: "saleOrderDetail",
        name: "收银日报2",
        meta: {
          i18n: "saleOrderDetail",
          isAuth: false,
        },
        component: () => import("@/views/cashierDayily/saleOrderDetail.vue"),
      },
      {
        path: "saleProductDetail",
        name: "收银日报3",
        meta: {
          i18n: "saleProductDetail",
          isAuth: false,
        },
        component: () => import("@/views/cashierDayily/saleProductDetail.vue"),
      },
    ],
  },
];
