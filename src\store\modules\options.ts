/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-06 11:39:44
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-13 10:48:33
 * @FilePath: \flb-receipt\src\store\modules\options.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getStoreSelectorApi } from "@/api/salesOrderManage";

const options = {
  namespaced: true,
  state: {
    storeList: [], // 门店
  },
  actions: {
    getStoreList({ commit }: any) {
      return new Promise<void>((resolve) => {
        getStoreSelectorApi({}).then((res: any) => {
          let data = res.data ? res.data : [];
          data = data.map((ele: any) => {
            return {
              ...ele,
              nameValue: ele.name,
              name: ele.name.value,
            };
          });
          commit("SET_STORE_LIST", data);
          resolve();
        });
      });
    },
  },
  mutations: {
    // 门店列表
    SET_STORE_LIST: (state: any, optList: any) => {
      state.storeList = optList;
    },
  },
};

export default options;
