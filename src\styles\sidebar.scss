.el-menu--popup {
  .el-menu-item {
    background-color: transparent;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    i,span{
      color:#1D2330;
    }
    &:hover{
      i,span{
        color:#1D2330;
      }
    }
    &.is-active {
      background-color: #222933;
      &:before {
        content: '';
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        position: absolute;
      }
      i,span{
        color:#fff;
      }
    }
  }
}
.avue-sidebar {
  user-select: none; 
  position: relative;
  padding-top: 60px;
  height: 100%;
  position: relative;
  background-color: #F2F6F9;
  transition: width .2s;
  box-sizing: border-box;
  .el-scrollbar__wrap {
    overflow-x: hidden;
    padding: 0 8px;
  }
  &--tip {
    width: 90%;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    position: absolute;
    top: 5px;
    left: 5%;
    color: #ccc;
    z-index: 2;
    text-align: center;
    font-size: 14px;
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  // .el-menu,
  // .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
  // .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover,
  // .el-menu--horizontal>.el-submenu .el-submenu__title:hover {
  //     background-color: transparent;
  // }
  .el-menu-item,
  .el-sub-menu__title {
    min-width: auto;
    height: 40px;
    line-height: 40px;
    margin-bottom: 8px;
    color: #1D2330;
    border-radius: 8px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span{
      vertical-align: top;
    }
    i{
      margin-right: 10px;
    }
    i,span{
      color:#1D2330;
    }
    &:hover{
      background: #222933;
      i,span{
          color:#1D2330;
      }
    }
    &.is-active {
      // &:before {
      //   content: "";
      //   top: 0;
      //   left: 0;
      //   bottom: 0;
      //   width: 4px;
      //   background: #409eff;
      //   position: absolute;
      // }
      background-color: #222933;
      i,span{
        color:#fff;
      }
    }
  }
  .el-submenu.is-active .el-sub-menu__title {
    span,
    i {
      color: #fff;
    }
  }
  // .el-menu-item {
  //   padding-left: 32px !important;
  //   .el-tooltip {
  //     padding: 0 12px !important;
  //   }
  // }
  .el-submenu__title,
  .el-menu-item.no-children-item {
    padding: 0 12px !important;
  }
  .el-submenu__icon-arrow {
    right: 0;
  }
}

.avue-sidebar {
  .el-sub-menu__title i,
  .el-menu-item i {
    margin-left: 4px;
  }
  .el-menu--collapse .el-sub-menu__title i,
  .el-menu--collapse .el-menu-item i {
    margin-left: 1px;
  }
  .el-menu--inline {
    .el-sub-menu__title i,
    .el-menu-item i {
      margin-left: 0;
    }
  }
}
