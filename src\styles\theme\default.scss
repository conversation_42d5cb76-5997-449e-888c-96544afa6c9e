.theme-default {
  $default: #415fff; //主颜色
  $danger: #ec2d30;

  // element-plus样式覆盖
  --el-color-primary: #415fff;
  // 时间选择器
  --el-datepicker-active-color: #415fff;
  // 时间选择器
  --el-datepicker-hover-text-color: #415fff;
  // dropdown样式
  --el-dropdown-menuItem-hover-color: #415fff;
  // cascader样式
  --el-cascader-menu-selected-text-color: #415fff;

  .el-button--primary {
    --el-button-hover-bg-color: #5e77ff;
    --el-button-active-bg-color: #415fff;
    --el-button-disabled-bg-color: #c5d8ff;
  }

  .el-button--primary.is-link,
  .el-button--primary.is-plain,
  .el-button--primary.is-text {
    --el-button-text-color: #415fff;
    --el-button-hover-link-text-color: #415fff;
  }

  .el-button--danger.is-link,
  .el-button--danger.is-plain,
  .el-button--danger.is-text {
    --el-button-text-color: #ec2d30;
    --el-button-hover-link-text-color: #ec2d30;
  }

  .el-button {
    --el-text-color-regular: #1d2300;
  }

  .el-button.is-link {
    padding: 0 4px;

    &.el-button--primary {
      &:hover {
        background-color: #f3f6f8;
      }

      &:active {
        color: #2a4bff;
        background-color: #edf0f2;
      }
    }

    &.el-button--danger {
      &:hover {
        background-color: #f3f6f8;
      }

      &:active {
        color: $danger;
        background-color: #edf0f2;
      }
    }
  }

  .main-color {
    color: $default;
  }

  .main-bg {
    background-color: $default;
  }

  // tabs样式
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    font-weight: 550;
  }

  // radio样式
  .el-radio.is-bordered:hover {
    border-color: $default;
    color: $default;
  }
  .el-radio__input.is-disabled.is-checked .el-radio__inner {
    --el-disabled-bg-color: #415fff;
  }

  .login_img_box {
    position: absolute;
    top: 0;
    left: 0;
    width: 357px;
    height: 100vh;
    // background: url('') no-repeat center;
    // background-size: 100% 100%;
  }

  .avue-top {
    background-color: #fff;

    .el-menu-item {
      i,
      span {
        vertical-align: top;
      }

      &:hover {
        i,
        span {
          color: #000;
        }
      }
    }

    .top-bar__item {
      i {
        color: #000;
      }
    }
  }

  .avue-left {
    .avue-sidebar {
      border-right: 1px solid #d6dce1;
      padding-top: 80px;
      background-color: #f2f6f9;
      background-image: url("~@/assets/img/default/left-menu-top-bg.png");
      background-repeat: no-repeat;
      background-position: top right;
      background-size: 252px auto;

      .avue-logo_title {
        background-image: url("~@/assets/img/default/left-menu-logo.png");
      }

      .avue-logo_subtitle {
        background-image: url("~@/assets/img/default/left-menu-logo.png");
      }

      .el-menu-item,
      .el-sub-menu__title {
        i,
        span {
          color: #1d2330;
        }

        &:hover {
          background: #c5d8ff;
          i,
          span {
            color: #1d2330;
          }
        }

        &.is-active {
          background: #c5d8ff;
          i,
          span {
            color: #1d2330;
            font-weight: 550;
          }
        }
      }

      .el-scrollbar {
        background-image: url("~@/assets/img/default/left-menu-bottom-bg.png");
        background-repeat: no-repeat;
        background-position: bottom right;
        background-size: 252px auto;
      }
    }
  }

  .el-menu--popup {
    margin: 0;
    padding: 4px;
    background-color: #1d2330 !important;
    border-radius: 2px 8px 8px 8px;
    .el-menu-item {
      height: 36px;
      line-height: 34px;
      border-radius: 4px;
      background-color: #1d2330;
      i,
      span {
        color: #ffffff;
      }
      &:hover {
        background-color: #2e374a;
        i,
        span {
          color: #ffffff;
        }
      }
      &.is-active {
        background-color: #2e374a;
        // &:before {
        //   content: "";
        //   top: 0;
        //   left: 0;
        //   bottom: 0;
        //   width: 4px;
        //   background: #0054FF;
        //   position: absolute;
        // }
        i,
        span {
          color: #ffffff;
        }
      }
    }
  }

  .avue-sidebar--tip {
    background-color: transparent;
    color: #333;
  }

  .logo_title,
  .avue-breadcrumb {
    color: #1d2330;

    i {
      color: #1d2330;
    }
  }

  .top-search {
    .el-input__inner {
      color: #333;
    }

    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
      /* WebKit browsers */
      color: #fff;
    }

    input:-moz-placeholder,
    textarea:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #fff;
    }

    input::-moz-placeholder,
    textarea::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #fff;
    }

    input:-ms-input-placeholder,
    textarea:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #fff;
    }
  }

  // loading部分的样式
  .el-loading-spinner {
    .path {
      stroke: $default;
    }

    .el-loading-text {
      color: $default;
    }
  }

  //公用面包屑样式
  .common-crumbs-box {
    .hover-span {
      &:hover {
        color: $default;
      }
    }
  }

  .el-breadcrumb__inner a:hover,
  .el-breadcrumb__inner.is-link:hover {
    color: $default;
  }

  // 头部标签
  .avue-tags__contentmenu .item:hover {
    background-color: $default;
  }

  // 时间线
  .el-timeline-item__node--primary {
    border-color: $default;
  }

  // 表格点击看详情hover态
  .table_cell_check_details {
    &:hover {
      color: $default;
    }
  }
}
