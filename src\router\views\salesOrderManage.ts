/*
 * @Author: spanull <EMAIL>
 * @Date: 2024-11-14 14:09:51
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-02-07 15:04:39
 * @FilePath: \flb-receipt\src\router\views\salesOrderManage.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 入库管理
export default [
  {
    path: "/salesOrderManage",
    name: "销售开单管理",
    meta: {
      i18n: "salesBilling",
    },
    children: [
      {
        path: "list",
        name: "销售开单",
        meta: {
          i18n: "salesBilling",
          keepAlive: true,
        },
        component: () => import("@/views/salesOrderManage/list.vue"),
      },
      {
        path: "add",
        name: "新增销售单",
        meta: {
          i18n: "addSalesOrder",
        },
        component: () =>
          import("@/views/salesOrderManage/add-sales-order-page.vue"),
      },
      {
        path: "draftBox",
        name: "销售单草稿箱",
        meta: {
          i18n: "draftBox",
          keepAlive: true,
        },
        component: () => import("@/views/salesOrderManage/draft-box.vue"),
      },
      {
        path: "priceBox",
        name: "销售单价格变动",
        meta: {
          i18n: "priceChange",
          keepAlive: true,
        },
        component: () => import("@/views/salesOrderManage/price-box.vue"),
      },
    ],
  },

  {
    path: "/returnOrderManage",
    name: "退货开单管理",
    meta: {
      i18n: "returnBilling",
    },
    children: [
      {
        path: "list",
        name: "退货开单",
        meta: {
          i18n: "returnBilling",
          keepAlive: true,
        },
        component: () => import("@/views/returnOrderManage/list.vue"),
      },
      {
        path: "add",
        name: "新增退货单",
        meta: {
          i18n: "addReturnOrder",
        },
        component: () =>
          import("@/views/returnOrderManage/add-return-sales-order.vue"),
      },
      {
        path: "draftBox",
        name: "退货单草稿箱",
        meta: {
          i18n: "draftBox",
          keepAlive: true,
        },
        component: () => import("@/views/returnOrderManage/draft-box.vue"),
      },
    ],
  },
];
