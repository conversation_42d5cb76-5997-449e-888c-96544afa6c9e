<template>
  <el-dialog
    v-model="dialogVisible"
    width="448px"
    append-to-body
    :title="props.dialogTitle + '-多语言'"
    :before-close="onClose"
    class="common-dialog-center">

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      :size="formSize">
      <el-form-item label="第一语言" prop="f">
        <el-input v-model="form.f" placeholder="请输入" maxlength="50" show-word-limit oninput="value=value.replace(/[^\w\u4e00-\u9fa5]+$/g,'')" />
      </el-form-item>
      <el-form-item label="第二语言" prop="s">
        <el-input v-model="form.s" placeholder="请输入" maxlength="50" show-word-limit oninput="value=value.replace(/[^\w\u4e00-\u9fa5]+$/g,'')" />
      </el-form-item>
      <el-form-item label="第三语言" prop="t">
        <el-input v-model="form.t" placeholder="请输入" maxlength="50" show-word-limit oninput="value=value.replace(/[^\w\u4e00-\u9fa5]+$/g,'')" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="onClose">取 消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="onSave(formRef)">提 交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, defineEmits, defineProps, reactive } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { deepClone } from '@/util/util'

const emit = defineEmits(['refresh', 'close'])
const props = defineProps({
  // 标题
  dialogTitle: {
    type: String,
    required: true
  },
  langObj: {
    type: Object,
    required: true
  }
})

const dialogVisible = ref(false)
const formSize = ref<ComponentSize>('small')
const formRef = ref<FormInstance>()
const form: any = ref({
  f: '',
  s: '',
  t: '',
  value: ''
})
const rules = reactive<FormRules>({
  f: [{ required: true, message: '请输入', trigger: 'blur' }],
  s: [{ required: true, message: '请输入', trigger: 'blur' }],
  t: [{ required: true, message: '请输入', trigger: 'blur' }]
})

onMounted(() => {
  dialogVisible.value = true
  form.value = deepClone(props.langObj)
})

// 提交
const onSave = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      emit('refresh', form.value)
      onClose()
    }
  })
}

// 关闭
const onClose = () => {
  dialogVisible.value = false
  emit('close')
}
</script>

<style lang="scss" scoped>
</style>