<template>
  <el-drawer
    :model-value="show"
    :before-close="onClose"
    direction="rtl"
    size="720px"
    :title="$t('receipt.returnOrderDetail')"
    :modal="false"
    class="no_shadow_drawer"
    modal-class="detail_drawer_modal top_drawer_modal"
  >
    <div
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
    >
      <div class="b_solid_default border_radius8 mb24">
        <!-- 退货单： -->
        <div
          class="pt12 pb8 pl16 pr16 bb_dashed_default flex align_items_center justify_content_between"
        >
          <div>
            <span class="c_1D2330 fs16 fw500 lh24 mr8">
              {{ $t("receipt.returnOrderM") }}
              {{ infoData.orderNo }}
            </span>
            <span
              v-if="infoData.examineState"
              :class="judgeStatusColorOpts[infoData.examineState?.code] || ''"
              class="common-status-box"
              style="vertical-align: bottom"
            >
              {{ infoData.examineState?.message }}
            </span>
            <span v-else>-</span>
          </div>
        </div>
        <!-- 开单日期 -->
        <div class="pt8 pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.billingDateM") }}
            <span class="c_1D2330">
              {{ dayjs(infoData.orderDate).format("YYYY/MM/DD") }}
            </span>
          </span>
        </div>
        <!-- 关联销售单 -->
        <div class="pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.relatedSalesOrderM") }}
            <span class="c_1D2330">
              {{ infoData.saleOrderNo || "-" }}
            </span>
          </span>
        </div>
        <!-- 销售人员 -->
        <div class="pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.salesmanM") }}
            <span class="c_1D2330">
              {{ infoData.salespersonName }}
            </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <!-- 审核人员 -->
          <span class="c_4F5363 lh18">
            {{ $t("receipt.reviewerM") }}
            <span class="c_1D2330">
              {{ infoData.examineName || "-" }}
            </span>
          </span>
        </div>
        <!-- 门店 -->
        <div class="pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.storeM") }}
            <span class="c_1D2330">
              {{ infoData.storeName?.value }}
            </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <!-- 会员 -->
          <span class="c_4F5363 lh18">
            {{ $t("receipt.memberM") }}
            <span class="c_1D2330">
              {{
                infoData.memberName
                  ? `${infoData.memberName}（${
                      infoData.memberCode || "-"
                    }） (LV.${infoData.level || 1}）`
                  : `-`
              }}
            </span>
          </span>
        </div>
        <!-- 备注 -->
        <div class="pb16 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.remarksM") }}
            <span class="c_1D2330">
              {{ infoData.orderRemarks || "-" }}
            </span>
          </span>
        </div>
      </div>
      <template v-if="productList && productList.length">
        <!-- 退货商品 -->
        <div class="common_title_with_flag mb16">
          {{ $t("receipt.returnProduct") }}
        </div>
        <!--      退货商品卡片     -->
        <div
          v-for="(product, index) in productList"
          :key="index"
          class="b_solid_default border_radius8 mb24"
        >
          <div class="bb_solid_default pb16">
            <div class="pt12 pb16 pl24 pr24">
              <span class="c_1D2330 fs14 fw500 lh24 mr8">
                <span
                  class="second-tag"
                  v-if="product?.stockType?.value === 'SECOND_HAND'"
                >
                  {{ $t("receipt.secondHand") }}
                </span>
                {{ product?.skuName?.value }}
              </span>
              <el-divider class="ml12 mr12" direction="vertical"></el-divider>
              <!-- 单价 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.unitPriceM") }}
                <span class="c_1D2330">
                  {{ formatMoney(Number(product.originalUnitPrice)) }}
                </span>
              </span>
              <el-divider class="ml12 mr12" direction="vertical"></el-divider>
              <!-- 下单数量 -->
              <span
                class="c_4F5363 lh18"
                v-if="product.isBarcode?.value === 'NO'"
              >
                {{ $t("receipt.orderQuantityM") }}
                <span class="c_1D2330">
                  {{ product.saleQuantity }}
                </span>
              </span>
              <!-- 串码 -->
              <span class="c_4F5363 lh18" v-else>
                {{ $t("receipt.serialCodeM") }}
                <span class="c_1D2330">
                  {{
                    product?.barcodeList?.map((item) => item.barcode).join(",")
                  }}
                </span>
              </span>
            </div>
            <!-- 商品总价 -->
            <div class="pt16 ml24 mr24 bt_dashed_default">
              <span class="c_4F5363 lh18">
                {{ $t("receipt.totalPriceProductM") }}
                <span class="c_1D2330">
                  {{ formatMoney(Number(product.originalTotalPrice || 0)) }}
                </span>
              </span>
            </div>
            <div class="pt4 ml24 mr24">
              <!-- 兑换数量 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.exchangeQuantityM") }}
                <span class="c_1D2330">
                  {{ getString("exchange", product) }}
                </span>
              </span>
              <el-divider class="ml12 mr12" direction="vertical"></el-divider>
              <!-- 满减 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.fullDeductionM") }}
                <span class="c_1D2330">
                  {{ getString("max", product) }}
                </span>
              </span>
              <el-divider class="ml12 mr12" direction="vertical"></el-divider>
              <!-- 折扣 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.rabateM") }}
                <span class="c_1D2330">
                  {{ getString("discount", product) }}
                </span>
              </span>
              <el-divider class="ml12 mr12" direction="vertical"></el-divider>
              <!-- 积分抵扣 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.pointsDeductionM") }}
                <span class="c_1D2330">
                  {{
                    getString("point", product)
                      ? formatMoney(getString("point", product))
                      : "-"
                  }}
                </span>
              </span>
            </div>
            <div class="pt4 ml24 mr24">
              <!-- 实付款 -->
              <span class="c_4F5363 lh18">
                {{ $t("receipt.payAmountM") }}
                <span class="c_1D2330">
                  {{ formatMoney(Number(product.saleTotalPrice)) }}
                </span>
              </span>
            </div>
          </div>
          <div class="pt14 pb14 pr24 pl24">
            <template v-if="product.isBarcode?.value === 'NO'">
              <!-- 退货数量 -->
              <span class="c_1D2330 lh18">
                {{ $t("receipt.returnQuantityM") }}
              </span>
              <span class="c_1D2330 fs14 fw500 lh22">
                {{ product.quantity }}
              </span>
              <el-divider class="ml16 mr16" direction="vertical"></el-divider>
            </template>
            <!-- 退回积分 -->
            <template v-if="getReturnPoint(product)">
              <span class="c_1D2330 lh18">
                {{ $t("receipt.returnPointsM") }}
              </span>
              <span class="c_1D2330 fs14 fw500 lh22">
                {{ getReturnPoint(product) }}
              </span>
              <el-divider class="ml16 mr16" direction="vertical"></el-divider>
            </template>
            <!-- 退货金额 -->
            <span class="c_1D2330 lh18">
              {{ $t("receipt.returnAmountM") }}
            </span>
            <span class="c_EC2D30 fs14 fw500 lh22">
              {{ formatMoney(product.totalPrice) }}
            </span>
          </div>
        </div>
      </template>
    </div>

    <template #footer>
      <div class="t_left">
        <div class="fs14" :class="props.noButton ? '' : 'pb12'">
          <!-- 退回积分 -->
          <span class="c_4F5363 lh18 fs14">
            {{ $t("receipt.returnPointsM") }}
            <span class="c_1D2330 fs14 fw500">{{ getTotalPoint() }} </span>
          </span>
          <el-divider class="ml12 mr12" direction="vertical"></el-divider>
          <!-- 退货总金额 -->
          <span class="c_4F5363 lh18 fs14">
            {{ $t("receipt.returnTotalAmount") }}
            <span class="c_EC2D30 fs14 fw500">
              {{ formatMoney(getTotalAmount()) }}
            </span>
          </span>
        </div>
        <template v-if="!props.noButton">
          <template v-if="infoData?.examineState?.code === 0">
            <!-- 挂起 -->
            <el-button
              type="primary"
              @click="
                operate('addReturnSalesOrder', {
                  data: infoData,
                })
              "
            >
              {{ $t("receipt.edit") }}
            </el-button>
            <el-button
              type="danger"
              @click="
                operate('delDialog', {
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
          <!-- 审核中 -->
          <template v-if="infoData?.examineState?.code === 1">
            <template v-if="checkAuth(infoData)">
              <!-- 通过 -->
              <el-button
                type="primary"
                @click="
                  operate('auditDialog', {
                    title: $t('receipt.auditPass'),
                  })
                "
              >
                {{ $t("receipt.pass") }}
              </el-button>
              <!-- 拒绝 -->
              <el-button
                type="danger"
                @click="
                  operate('auditDialog', {
                    title: $t('receipt.auditReject'),
                  })
                "
              >
                {{ $t("receipt.reject") }}
              </el-button>
            </template>
            <!-- 撤回 -->
            <el-button
              type="danger"
              @click="
                operate('returnDialog', {
                  title: $t('receipt.recallPrompt'),
                  content: $t('receipt.recallPromptContent'),
                })
              "
            >
              {{ $t("receipt.recall") }}
            </el-button>
          </template>
          <!-- 已通过 -->
          <template v-else-if="infoData?.examineState?.code === 2">
            <!-- 退货 -->
            <!-- <el-button type="primary" @click="operate('addReturnSalesOrder', {})">
            {{ $t("receipt.returnGoods") }}
          </el-button> -->
            <!-- 价格修订 -->
            <!-- <el-button type="primary" @click="operate('priceSalesOrderPage', {})">
            {{ $t("receipt.priceRevision") }}
          </el-button> -->
          </template>
          <!-- 已拒绝 -->
          <template v-else-if="infoData?.examineState?.code === 3">
            <!-- 重新提交 -->
            <el-button
              type="primary"
              @click="operate('addReturnSalesOrder', {})"
            >
              {{ $t("receipt.resubmit") }}
            </el-button>
            <!-- 删除 -->
            <el-button
              type="danger"
              @click="
                operate('delDialog', {
                  title: $t('receipt.deletePrompt'),
                  content: $t('receipt.deletePromptContent'),
                })
              "
            >
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
          <el-divider
            class="ml12 mr12"
            direction="vertical"
            v-if="infoData?.examineState?.code !== 2"
          ></el-divider>
          <!-- 返回 -->
          <el-button plain @click="onClose">{{
            $t("receipt.return")
          }}</el-button>
        </template>
      </div>
    </template>

    <!-- 撤回提示 -->
    <stop-dialog
      v-if="
        operateDialog.type === 'delDialog' ||
        operateDialog.type === 'returnDialog'
      "
      :title="operateDialog.title"
      :content="operateDialog.content"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
    ></stop-dialog>

    <!-- 审核 -->
    <audit-dialog
      v-if="operateDialog.type === 'auditDialog'"
      :title="operateDialog.title"
      v-model:show="operateDialog.show"
      @confirm="onAudit"
    ></audit-dialog>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps, watch, reactive } from "vue";
import { ElMessage, dayjs } from "element-plus";
import auditDialog from "@views/salesOrderManage/components/audit-dialog.vue";
import stopDialog from "@/components/dialog/delete.vue";
import { judgeStatusColorOpts } from "@views/salesOrderManage/ts/enum";
import {
  add,
  divide,
  formatMoney,
  multiply,
  round,
  subtract,
} from "@/util/numberUtil";
import { nextTick } from "vue";
import {
  deleteOrderReturn,
  examineFail,
  examineSuccess,
  getOrderReturnDetail,
  withdraw,
} from "@/api/returnOrderManage";
import lang from "@/lang/index";
import store from "@/store";
import router from "@/router";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh", "update:show"]);
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => {
      return {};
    },
  },
  show: {
    type: Boolean,
    required: true,
  },
  noButton: {
    type: Boolean,
    default: false,
  },
});
const activeNames = ref([]);
const infoData: any = ref({});
const operateDialog = reactive({
  show: false,
  type: "",
  data: {},
  title: "",
  content: "",
});
const loading = ref(false);
const productList = ref([]);
const giftsList = ref([]);

watch(
  [() => props.show, () => props.data.orderNo],
  (val) => {
    // 获取销售单详情
    if (val[0] && val[1]) {
      nextTick(() => {
        getInfo();
        activeNames.value = [];
      });
    }
  },
  { immediate: true }
);

// 获取销售单详情
const getInfo = () => {
  loading.value = true;
  getOrderReturnDetail({
    orderNo: props.data.orderNo,
  })
    .then((res) => {
      loading.value = false;
      if (res.code == 200) {
        infoData.value = res.data;
        productList.value = infoData.value.returnSkuList.filter((item: any) => {
          return item.isGiveaway.value === "NO";
        });
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

/**
 * 根据类型返回优惠信息字符串。
 *
 * @param {string} type - 优惠类型（exchange、max、discount、point）
 * @param {any} product - 商品对象
 * @param {boolean} isNumber - 是否返回数字（用于计算）
 * @return {string} 优惠信息字符串（例如“1”、“10.00”、“8折”等）
 */
const getString = (type: string, product: any, isNumber?: boolean) => {
  // 0: "COUPON_EXCHANGE", //"兑换券"
  // 1: "COUPON_DISCOUNT", //"折扣券"
  // 2: "COUPON_THRESHOLD_DISCOUNT", //"满减券"
  // 3: "REDEEM_POINTS", //"积分抵扣"
  if (type === "exchange") {
    // 兑换数量
    let num = 0;
    (infoData.value.returnDiscountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
    });
    return num ? num : "-";
  } else if (type === "max") {
    // 满减
    let num = 0;
    let price = 0;
    (infoData.value.returnDiscountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_EXCHANGE") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          num++;
        }
      }
      if (item.discountType.value === "COUPON_THRESHOLD_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          price = round(
            subtract(
              Number(product.originalTotalPrice),
              Number(product.discountTotalPrice)
            )
          );
        }
      }
    });
    if (price) {
      price = round(price - multiply(num, Number(product.originalUnitPrice)));
    }
    return price ? `${formatMoney(price)}` : "-";
  } else if (type === "discount") {
    // 折扣
    let discountRate = 0;
    (infoData.value.returnDiscountList || []).forEach((item: any) => {
      if (item.discountType.value === "COUPON_DISCOUNT") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          discountRate = item.discountRate;
        }
      }
    });
    return discountRate ? `${discountRate}折` : "-";
  } else if (type === "point") {
    // 积分抵扣
    let pointsPrice = 0;
    (infoData.value.returnDiscountList || []).forEach((item: any) => {
      if (item.discountType.value === "REDEEM_POINTS") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          pointsPrice = item.pointsPrice || 0;
        }
      }
    });
    return pointsPrice || 0;
  } else if (type === "usePoints") {
    //积分
    let usePoints = 0;
    (infoData.value.returnDiscountList || []).forEach((item: any) => {
      if (item.discountType.value === "REDEEM_POINTS") {
        const ids = item.skuId ? item.skuId.split(",") : [];
        if (ids.includes(product.skuId)) {
          usePoints = item.usePoints;
        }
      }
    });
    return usePoints || 0;
  }
};

/**
 * 计算指定商品的退货积分。
 *
 * @param {Object} item - 商品信息。
 * @returns {number} 计算出的退货积分。
 */
const getReturnPoint = (item: any) => {
  try {
    const point = Number(getString("usePoints", item, true));
    const unitPoint = divide(point, Number(item.quantity)) || 0;
    return round(multiply(Number(item.quantity || 0), unitPoint), 2);
  } catch (e) {
    console.error(e);
    return 0;
  }
};

/**
 * 计算总退货金额。
 *
 * @return {number} 总退回金额。
 */
const getTotalAmount = () => {
  if (!infoData.value?.returnSkuList || !infoData.value?.returnSkuList.length)
    return 0;
  let total = 0;
  infoData.value.returnSkuList.forEach((item: any) => {
    total = add(total, Number(item.totalPrice));
  });
  return total;
};

/**
 * 计算总退回积分
 *
 * @return {number} 总退回积分
 */
const getTotalPoint = () => {
  if (!infoData.value?.returnSkuList || !infoData.value?.returnSkuList.length)
    return 0;
  let total = 0;
  infoData.value.returnSkuList.forEach((item: any) => {
    total = add(total, Number(getReturnPoint(item)));
  });
  return total;
};

// 刷新
const refresh = () => {
  getInfo();
  emit("refresh");
};

// 操作
const operate = (type, val: any = {}) => {
  switch (type) {
    case "addReturnSalesOrder":
      onClose();
      sessionStorage.setItem(
        "addSaleData",
        JSON.stringify({
          data: infoData.value as any,
        })
      );
      router.push({
        path: "/returnOrderManage/add",
      });
      break;
    default:
      operateDialog.title = val.title || "";
      operateDialog.content = val.content || "";
      operateDialog.type = type;
      operateDialog.data = infoData.value;
      operateDialog.show = true;
      break;
  }
};

// 撤销、删除
const stopDialogConfirm = () => {
  const data: any = operateDialog.data;
  if (operateDialog.type === "returnDialog") {
    withdraw([data.id])
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          emit("refresh");
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    deleteOrderReturn({ id: data.id })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          emit("refresh");
          onClose();
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

/**
 * @description: 审核
 * @param {Object} form - 表单数据
 * @param {String} form.text - 审核备注
 * @return {void} 无返回值
 */
const onAudit = (form) => {
  const data: any = operateDialog.data;
  if (operateDialog.title === i18n.t("receipt.auditPass")) {
    examineSuccess({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          emit("refresh");
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  } else {
    examineFail({
      examineRemarks: form.text,
      idList: [data.id],
    })
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          operateDialog.show = false;
          emit("refresh");
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((res) => {
        ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
      });
  }
};

// 关闭
const onClose = () => {
  emit("update:show", false);
  emit("close");
};

// 检验审核权限
const checkAuth = (row: any) => {
  const personInfo = store.getters.personInfo;
  return personInfo.personId === row.examineId;
};
</script>

<style lang="scss" scoped>
//.el-collapse-item__header
::v-deep .el-collapse {
  margin-bottom: 2px;
  border: none;

  .el-collapse-item {
    display: flex;
    flex-direction: column-reverse;
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(-90deg);
  }

  .el-collapse-item__wrap {
    border: none;

    .el-collapse-item__content {
      padding: 0;
    }
  }

  .el-collapse-item__header {
    height: 30px;
    border-radius: 4px;
    flex-direction: row-reverse;
    justify-content: flex-end;
    border: none;

    .el-collapse-item__arrow {
      margin-left: 10px;
    }
  }
}

.second-tag {
  font-size: 10px;
  color: #ff9900;
  padding: 1px 8px;
  border-radius: 4px;
  border: 1px solid #ff9900;
  margin-right: 8px;
  vertical-align: bottom;
}
</style>
