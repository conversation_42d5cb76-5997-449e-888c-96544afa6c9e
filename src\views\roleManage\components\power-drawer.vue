<template>
  <el-drawer
    v-model="drawerVisible"
    direction="rtl"
    :append-to-body="true"
    :before-close="onClose"
    size="100%"
    class="no_shadow_drawer left_menu_drawer_modal top_drawer_modal"
    :title="$t('receipt.roleManage')"
  >
    <div>
      <!-- <div class="common-crumbs-box">
        <span class="disable-span">{{ $t("receipt.powerManage") }}</span>
        <el-icon class="ml8 mr8 c_4F5363">
          <ArrowRight />
        </el-icon>
        <span class="hover-span" @click="onClose">{{
          $t("receipt.roleManage")
        }}</span>
        <el-icon class="ml8 mr8 c_4F5363">
          <ArrowRight />
        </el-icon>
        <span class="current-span">{{ $t("receipt.managePower") }}</span>
      </div> -->

      <div class="content_wrap b_solid_default border_radius4 bgc_FAFBFC">
        <div class="border_radius4 bgc_F7F7F7 p24">
          <el-tree
            ref="menuTreeRef"
            :data="menuList"
            :props="defaultProps"
            show-checkbox
            node-key="id"
            default-expand-all
          >
            <template #default="{ data }">
              <span class="text_over">{{ generateName(data) }}</span>
            </template>
            <template #empty>
              <empty-box></empty-box>
            </template>
          </el-tree>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button type="danger" @click="onClose">
        {{ $t("receipt.close") }}
      </el-button>
      <el-button type="primary" :loading="btnLoading" @click="onSubmit">{{
        $t("receipt.submit")
      }}</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits, defineProps } from "vue";
import { ElMessage } from "element-plus";
import {
  bindMenuApi,
  getByRoleIdApi,
  getMenuByTenantIdApi,
} from "@/api/roleManage";
import store from "@/store";
import emptyBox from "@/components/empty/index.vue";
import lang from "@/lang/index";
import { generateName } from "@/util/util";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  operateRow: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const drawerVisible = ref(false);
const btnLoading = ref(false);
const menuList = ref([]);
const defaultProps = {
  children: "children",
  label: "label",
};
const roleMenuList = ref([]);
const menuTreeRef = ref();

onMounted(() => {
  drawerVisible.value = true;

  getMenu();
});

// 获取菜单列表
const getMenu = () => {
  const params = {
    moduleId: props.operateRow.moduleId,
    tenantId: store.getters.userInfo.tenantId,
    menuType: 1,
  };
  getMenuByTenantIdApi(params)
    .then((res) => {
      if (res.code == 200) {
        const list = res.data || [];
        menuList.value = list;
        getPower();
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};
// 获取菜单列表
const getPower = () => {
  getByRoleIdApi({ roleId: props.operateRow.id })
    .then((res) => {
      if (res.code == 200) {
        roleMenuList.value = res.data.map((el) => el.menuId);
        setTimeout(() => {
          roleMenuList.value.forEach((ele) => {
            menuTreeRef.value.setChecked(ele, true, false);
          });
        }, 400);
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch(() => {
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 提交
const onSubmit = () => {
  const checkedNodes = menuTreeRef.value.getCheckedNodes(false, true);
  if (!checkedNodes.length) {
    ElMessage.error(i18n.t("receipt.selectMenuTips"));
    return;
  }
  btnLoading.value = true;
  const ids = checkedNodes.map((el) => el.id);
  const params = {
    buId: props.operateRow.buId,
    menuIds: ids,
    moduleId: props.operateRow.moduleId,
    roleId: props.operateRow.id,
  };
  bindMenuApi(params)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
        emit("refresh");
        onClose();
      } else {
        ElMessage.error(res.msg);
      }
      btnLoading.value = false;
    })
    .catch(() => {
      btnLoading.value = false;
      ElMessage.error(i18n.t("receipt.networkError"));
    });
};

// 关闭
const onClose = () => {
  drawerVisible.value = false;
  emit("close");
};
</script>

<style lang="scss" scoped>
.bgc_F7F7F7 {
  background-color: #f7f7f7;
}
.content_wrap {
  height: calc(100vh - 134px);
  padding: 12px 72px 12px 12px;
  box-sizing: border-box;
  overflow-y: auto;
}
::v-deep {
  .el-tree {
    background-color: transparent;
  }
}
</style>
