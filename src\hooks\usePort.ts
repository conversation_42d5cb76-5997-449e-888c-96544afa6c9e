import { ElMessage } from "element-plus";
import {
  generateBy<PERSON>ode<PERSON><PERSON>,
  getImportTemp<PERSON>pi,
  getSysType<PERSON>pi,
} from "@/api/common";
import lang from "@/lang/index";

const i18n = lang.global;
export const usePort = () => {
  // 根据code生成编码
  const generateCode = (code: string) => {
    return new Promise<string>((resolve, reject) => {
      generateByCodeApi({ code })
        .then((res) => {
          if (res.code == 200) {
            resolve(res.data);
          } else {
            ElMessage.error(res.msg);
            reject();
          }
        })
        .catch((res) => {
          ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
          reject();
        });
    });
  };

  // 获取系统类型选择器
  const getSysType = (business: string) => {
    return new Promise<Array<any>>((resolve, reject) => {
      getSysTypeApi({ business })
        .then((res) => {
          if (res.code == 200) {
            const list = res.data || [];
            list.forEach((el) => {
              el.value = el.name;
              el.business = "customer";
            });
            resolve(list);
          } else {
            ElMessage.error(res.msg);
            reject();
          }
        })
        .catch((res) => {
          ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
          reject();
        });
    });
  };

  // 根据code获取导入模板详情
  const getImportTemp = (code: string) => {
    return new Promise<any>((resolve, reject) => {
      getImportTempApi(code)
        .then((res) => {
          if (res.code == 200) {
            resolve(res.data);
          } else {
            ElMessage.error(res.msg);
            reject();
          }
        })
        .catch((res) => {
          ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
          reject();
        });
    });
  };

  return {
    generateCode,
    getSysType,
    getImportTemp,
  };
};
