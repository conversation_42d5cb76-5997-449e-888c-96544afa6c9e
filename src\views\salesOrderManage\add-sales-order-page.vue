<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-06 11:39:44
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-10 09:42:32
 * @FilePath: \flb-receipt\src\views\salesOrderManage\add-sales-order-page.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="pt16 pb16 pl24 pr24 position_re bgc_EDF0F2"
    style="min-height: 820px"
  >
    <el-form
      class="heightP100 min-height flex flex_direction_column"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="props.type === 'price'"
    >
      <div
        class="heightP100 min-height flex flex_direction_column"
        v-loading="loading"
        :element-loading-text="$t('receipt.loadingInProgress')"
      >
        <div>
          <div class="common-crumbs-box bgc_white mb16 border_radius4">
            <span class="disable-span hover-span" @click="onClose">
              {{ $t("receipt.salesBilling") }}
            </span>
            <el-icon class="ml8 mr8 c_4F5363">
              <ArrowRight />
            </el-icon>
            <!-- <span class="hover-span" @click="onClose">{{
              $t("receipt.salesOrderManagement")
            }}</span>
            <el-icon class="ml8 mr8 c_4F5363">
              <ArrowRight />
            </el-icon> -->
            <span class="current-span">
              {{
                props.type == "price"
                  ? $t("receipt.priceRevision")
                  : form.id
                  ? $t("receipt.editSalesOrder")
                  : $t("receipt.addSalesOrder")
              }}
            </span>
          </div>
        </div>
        <div
          v-if="props.type === 'price'"
          class="mb8 bgc_white pt10 pb10 pl16 pr16 border_radius4 fs12 c_3D3D3D"
        >
          <el-icon class="mr8 main-color align_bottom" size="16">
            <InfoFilled />
          </el-icon>
          {{ $t("receipt.addSalesOrderTips") }}
        </div>
        <div class="flex widthP100" style="height: calc(100% - 40px)">
          <div class="flex flex_direction_column mr16 flex1 overflow_hidden">
            <div class="bgc_white mb16 border_radius8 p24">
              <el-row :gutter="20">
                <el-col :span="8">
                  <!-- 销售单号 -->
                  <el-form-item
                    :label="$t('receipt.salesOrderCodeM')"
                    prop="orderNo"
                  >
                    <el-input
                      v-model="form.orderNo"
                      :disabled="!!form.id"
                      maxlength="50"
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseEnter')
                      "
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 开单日期 -->
                  <el-form-item
                    :label="$t('receipt.billingDateM')"
                    prop="orderDate"
                  >
                    <el-date-picker
                      :disabled="props.type === 'price'"
                      v-model="form.orderDate"
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseChoose')
                      "
                      style="width: 100%"
                      type="date"
                      value-format="YYYY/MM/DD"
                      format="YYYY/MM/DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 门店 -->
                  <el-form-item :label="$t('receipt.storeM')" prop="storeId">
                    <el-select
                      :disabled="props.type === 'price'"
                      v-model="form.storeId"
                      filterable
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseChoose')
                      "
                      size="default"
                      @change="changeStore"
                      @clear="changeStore('')"
                    >
                      <el-option
                        v-for="item in storeList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 销售员 -->
                  <el-form-item
                    :label="$t('receipt.salesmanM')"
                    prop="salespersonId"
                  >
                    <el-select
                      :disabled="props.type === 'price'"
                      v-model="form.salespersonId"
                      filterable
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseChoose')
                      "
                      size="default"
                      @change="changeSalePerson"
                      @clear="changeSalePerson('')"
                    >
                      <el-option
                        v-for="item in salePersonList"
                        :key="item.sysUserPersonId"
                        :label="item.sysUserName"
                        :value="item.sysUserPersonId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="discountRole.auditFlag">
                  <!-- 审核人 -->
                  <el-form-item
                    :label="$t('receipt.reviewerM')"
                    prop="examineId"
                  >
                    <el-select
                      :disabled="props.type === 'price'"
                      v-model="form.examineId"
                      filterable
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseChoose')
                      "
                      size="default"
                      @change="changeExaminePerson"
                      @clear="changeExaminePerson('')"
                    >
                      <el-option
                        v-for="item in salePersonList"
                        :key="item.sysUserPersonId"
                        :label="item.sysUserName"
                        :value="item.sysUserPersonId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <div class="position_re">
                    <!-- 会员 -->
                    <el-button
                      v-if="props.type !== 'price'"
                      link
                      style="position: absolute; right: 0"
                      type="primary"
                      @click="operate('memberRegisterDialog')"
                      >{{ $t("receipt.registerAsAMember") }}</el-button
                    >
                    <el-form-item
                      class="mb4"
                      :label="$t('receipt.memberM')"
                      prop="memberCode"
                    >
                      <el-select
                        :disabled="props.type === 'price'"
                        clearable
                        v-model="form.memberCode"
                        :remote-method="searchMember"
                        class="widthP100"
                        filterable
                        :placeholder="
                          props.type === 'price'
                            ? ''
                            : $t('receipt.pleaseChoose')
                        "
                        remote
                        reserve-keyword
                        remote-show-suffix
                        @change="memberChange"
                        @clear="memberChange('')"
                      >
                        <el-option
                          v-for="item in memberOpts"
                          :key="item.memberCode"
                          :label="`${item.nickname} (${item.phoneNumber}) Lv.${item.level}`"
                          :value="item.memberCode"
                        />
                      </el-select>
                      <div
                        class="position_re widthP100"
                        v-if="props.type !== 'price'"
                      >
                        <div
                          class="border_radius4 bgc_F5F7F9 pt4 pb4 pl8 pr8 mt4 position_ab"
                          style="left: 0; right: 0"
                        >
                          <span class="c_4F5363">
                            {{ $t("receipt.memberPointsM") }}
                            <span class="c_1D2330">
                              {{ textFormat(form.currentPoints) }}
                            </span>
                          </span>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :span="8">
                  <!-- 备注 -->
                  <el-form-item
                    :label="$t('receipt.remarksM')"
                    prop="orderRemarks"
                    style="margin-bottom: 0"
                  >
                    <el-input
                      :disabled="props.type === 'price'"
                      show-word-limit
                      maxlength="200"
                      v-model="form.orderRemarks"
                      :rows="3"
                      :placeholder="
                        props.type === 'price' ? '' : $t('receipt.pleaseEnter')
                      "
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 商品 -->
            <div
              class="flex flex_direction_column justify_content_between flex1 bgc_white border_radius8 p24"
              style="overflow: hidden"
            >
              <div class="flex flex_direction_column overflow_hidden flex1">
                <div class="mb16" v-if="props.type !== 'price'">
                  <el-button
                    icon="CirclePlus"
                    @click="operate('addProductDialog')"
                  >
                    {{ $t("receipt.addProduct") }}
                  </el-button>
                  <el-button icon="CirclePlus" @click="operate('batchBarcode')">
                    {{ $t("receipt.addSerialCode") }}
                  </el-button>
                </div>
                <!--添加商品列表-->
                <div ref="tableRef" class="flex1 overflow_hidden">
                  <el-table
                    :data="showProductList"
                    size="small"
                    style="width: 100%"
                    :height="tableHeight"
                    preserve-expanded-content
                    :row-class-name="getShowExpend"
                    row-key="uuid"
                    :expand-row-keys="expandRowKeys"
                  >
                    <el-table-column type="expand" v-if="form.isBindDiscount">
                      <template #default="scope">
                        <div
                          v-for="(item, index) in getHasActivityList(scope.row)"
                          :key="item.skuId"
                          class="flex flex_direction_column overflow_hidden widthP100 pb16"
                        >
                          <div class="flex align_items_center">
                            <i
                              class="iconfont-flb icon-megaphone-fill mr4"
                              style="font-size: 16px; color: #ff9320"
                            ></i>
                            <div class="flex1">
                              <tooptip-text
                                text-class="c_4F5363"
                                :text="
                                  $t('receipt.activityNumM', {
                                    index: index + 1,
                                  }) + (item.ruleName || '')
                                "
                              />
                            </div>
                          </div>
                          <div
                            v-for="el in item.product || []"
                            :key="el.skuId"
                            class="flex align_items_center bgc_F5F7F9 border_radius4"
                            style="height: 42px"
                          >
                            <div class="pl16 pr16 flex flex1">
                              <tooptipText
                                :text="el.skuName?.value || ''"
                                text-class="c_1D2330 fw600"
                              />
                            </div>
                            <div class="pl16 pr16 flex" style="width: 72px">
                              <tooptipText
                                :text="el.stockQuantity || ''"
                                text-class="c_1D2330"
                              />
                            </div>
                            <div class="pl16 pr16 flex" style="width: 132px">
                              <tooptipText
                                :text="formatMoney(el.originalUnitPrice)"
                                text-class="c_1D2330"
                              />
                            </div>
                            <div
                              class="pl16 pr16 flex justify_content_center"
                              style="width: 124px"
                            >
                              <tooptipText
                                :text="el.quantity || 0"
                                text-class="c_1D2330"
                              />
                            </div>
                            <div class="pl16 pr16 flex" style="width: 60px">
                              <tooptipText
                                :text="el.unitName?.value || ''"
                                text-class="c_1D2330"
                              />
                            </div>
                            <div class="pl16 pr16 flex" style="width: 240px">
                              <div
                                v-if="
                                  el?.marketingUseMethod === 'ORIGINAL_PRICE'
                                "
                                class="pt2 pb2 pl8 pr8 border_radius4"
                                style="
                                  background-color: #dfe2e7;
                                  line-height: normal;
                                  height: min-content;
                                "
                              >
                                <tooptipText
                                  :text="$t('receipt.originPrice')"
                                  text-class="c_1D2330 fw500"
                                />
                              </div>
                              <div
                                v-if="el?.marketingUseMethod === 'FREE'"
                                class="pt2 pb2 pl8 pr8 border_radius4"
                                style="
                                  background-color: #ffd8d8;
                                  line-height: normal;
                                  height: min-content;
                                "
                              >
                                <tooptipText
                                  :text="$t('receipt.free')"
                                  text-class="c_1D2330 fw500"
                                />
                              </div>
                              <div
                                v-if="el?.marketingUseMethod === 'DISCOUNT'"
                                class="pt2 pb2 pl8 pr8 border_radius4"
                                style="
                                  background-color: #ffe1b5;
                                  line-height: normal;
                                  height: min-content;
                                "
                              >
                                <tooptipText
                                  :text="
                                    $t('receipt.rabateM') +
                                    el?.marketingDiscountAmount +
                                    $t('receipt.fracture')
                                  "
                                  text-class="c_1D2330 fw500"
                                />
                              </div>
                              <div
                                v-if="el?.marketingUseMethod === 'REDUCE_PRICE'"
                                class="pt2 pb2 pl8 pr8 border_radius4"
                                style="
                                  background-color: #bae7ff;
                                  line-height: normal;
                                  height: min-content;
                                "
                              >
                                <tooptipText
                                  :text="
                                    $t('receipt.deductionM2') +
                                    formatMoney(el.marketingReducePrice)
                                  "
                                  text-class="c_1D2330 fw500"
                                />
                              </div>
                            </div>
                            <div class="pl16 pr16 flex" style="width: 94px">
                              <tooptipText
                                :text="
                                  formatMoney(Number(el.discountUnitPrice))
                                "
                                text-class="c_EC2D30"
                              />
                            </div>
                            <div class="pl16 pr16 flex" style="width: 164px">
                              <tooptipText
                                :text="formatMoney(getTips(el))"
                                text-class="c_EC2D30"
                              />
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 商品 -->
                    <el-table-column
                      :label="$t('receipt.commodity')"
                      min-width="180"
                      prop="skuName"
                    >
                      <template #default="scope">
                        <div class="fs14 c_1D2330">
                          <span
                            class="second-tag"
                            v-if="scope.row?.stockType === 'SECOND_HAND'"
                          >
                            {{ $t("receipt.secondHand") }}
                          </span>
                          {{ scope.row.skuName?.value }}
                        </div>
                        <div class="flex flex_flow_column mt4">
                          <span class="fs12 c_989CAC">
                            {{ $t("receipt.codeM") }}
                          </span>
                          <span class="fs12 c_4F5363">
                            {{ scope.row.skuCode }}
                          </span>
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 库存 -->
                    <el-table-column
                      :label="$t('receipt.stock')"
                      width="72"
                      prop="stockQuantity"
                      :formatter="tableFormat"
                    >
                      <template #default="scope">
                        <span class="c_4F5363">
                          {{ getQuantity(scope.row) || "-" }}
                        </span>
                      </template>
                    </el-table-column>
                    <!-- 单价 -->
                    <el-table-column
                      :label="$t('receipt.unitPrice')"
                      width="132"
                      prop="originalUnitPrice"
                    >
                      <template #default="scope">
                        <div
                          v-if="
                            alterType === 'NO' &&
                            !(
                              ((props.type === 'price'
                                ? discountRole.auditAdjustmentPricePercent
                                : discountRole.salesAdjustmentPricePercent) ||
                                0) === 0 ||
                              form.isMultiDiscount ||
                              form.isBindDiscount
                            )
                          "
                        >
                          <el-input
                            size="default"
                            v-model="scope.row.originalUnitPrice"
                            :placeholder="$t('receipt.pleaseEnter')"
                            @input="priceInput(scope.row)"
                            @change="priceChange(scope.row)"
                            :formatter="digitFormat"
                            :parser="digitFormat"
                            @blur="blurPrice(scope.row)"
                          >
                            <template #append>
                              {{ store.getters.currencyInfo?.unit || "" }}
                            </template>
                          </el-input>
                        </div>
                        <span v-else>
                          {{ formatMoney(scope.row.originalUnitPrice || 0) }}
                        </span>
                      </template>
                    </el-table-column>
                    <!-- 数量 -->
                    <el-table-column
                      :label="$t('receipt.quantity')"
                      width="124"
                      prop="quantity"
                    >
                      <template #default="scope">
                        <template v-if="props.type !== 'price'">
                          <el-input-number
                            v-if="scope.row.isBarcode?.value === 'NO'"
                            :precision="0"
                            :placeholder="$t('receipt.pleaseEnter')"
                            v-model="scope.row.quantity"
                            :max="getQuantity(scope.row)"
                            :min="0"
                            style="width: 88px"
                            @change="(val) => quantityChange(scope.row, val)"
                          />
                          <div v-else class="flex flex_direction_column">
                            <template
                              v-for="(item, index) in scope.row.barcodeList"
                              :key="index"
                            >
                              <el-input
                                v-show="item.isEdit"
                                :ref="(el) => getBarcodeInputRef(el, index)"
                                class="mb8"
                                size="default"
                                clearable
                                v-model="item.barcode"
                                :placeholder="$t('receipt.pleaseEnter')"
                                style="width: 88px"
                                @blur="barCodeInputBlur(item, index, scope.row)"
                                @keydown.enter="
                                  barCodeInputEnter(item, index, scope.row)
                                "
                              >
                              </el-input>
                              <el-tag
                                v-show="!item.isEdit"
                                closable
                                type="info"
                                class="c_1D2330 mb8 text_over"
                                @close="deleteBarCode(item, index, scope.row)"
                              >
                                <el-tooltip
                                  effect="dark"
                                  :content="item.barcode"
                                >
                                  {{ item.barcode }}
                                </el-tooltip>
                              </el-tag>
                            </template>

                            <el-button
                              plain
                              size="default"
                              @click="addBarCode(scope.row)"
                              style="max-width: 88px"
                            >
                              <el-icon><Plus /></el-icon>
                              <tooptip-text
                                :text="$t('receipt.addSerialCode')"
                              ></tooptip-text>
                            </el-button>
                          </div>
                        </template>
                        <span v-else>{{ scope.row.quantity }}</span>
                      </template>
                    </el-table-column>
                    <!-- 单位 -->
                    <el-table-column
                      :label="$t('receipt.unit')"
                      width="60"
                      prop="unit"
                    >
                      <template #default="scope">
                        <span>
                          {{ textFormat(scope.row.unitName?.value) }}
                        </span>
                      </template>
                    </el-table-column>
                    <!-- 折扣活动规则 -->
                    <el-table-column
                      v-if="form.isMultiDiscount"
                      :label="$t('receipt.discountRule')"
                      width="156"
                      prop="unit"
                    >
                      <template #default="scope">
                        <el-select
                          v-if="scope.row.stockType !== 'SECOND_HAND'"
                          v-model="scope.row.grounpDiscountId"
                          :placeholder="$t('receipt.pleaseChoose')"
                          style="width: 100%"
                          :loading="discountRule.loading"
                          @change="(val) => changeDiscount(val, scope.row)"
                        >
                          <el-option
                            v-for="e in getProductDiscount(scope.row)"
                            :key="e.id"
                            :label="e.ruleName"
                            :value="e.id"
                          />
                        </el-select>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <!-- 捆绑销售 -->
                    <el-table-column
                      v-if="form.isBindDiscount"
                      :label="$t('receipt.bindActivityRule')"
                      width="240"
                      prop="isBindDiscount"
                    >
                      <template #default="scope">
                        <template v-if="scope.row.stockType !== 'SECOND_HAND'">
                          <div
                            v-if="getBindActivityList(scope.row).length"
                            class="border_radius4 pt4 pb4 pl8 pr8 flex align_items_center"
                            style="border: 1px solid #dfe2e7"
                          >
                            <div class="flex1">
                              <tooptipText
                                text-class="c_1D2330"
                                :text="
                                  $t('receipt.bundleActivityTip', [
                                    subtract(
                                      scope.row.quantity,
                                      getMainProductNum(scope.row)
                                    ),
                                    getMainProductNum(scope.row),
                                  ])
                                "
                              />
                            </div>
                            <i
                              class="iconfont-flb icon-tianjia ml8 pointer main-color"
                              style="font-size: 16px"
                              @click="showEditBind(scope.row)"
                            />
                          </div>
                          <div
                            v-else
                            class="border_radius4 pt4 pb4 pl8 pr8 flex align_items_center bgc_EDF0F2"
                            style="border: 1px solid #dfe2e7"
                          >
                            <div class="flex1">
                              <tooptipText
                                text-class="c_1D2330"
                                :text="$t('receipt.noBundleActivity')"
                              />
                            </div>
                          </div>
                        </template>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="form.isMultiDiscount"
                      :label="$t('receipt.disountOrPrice')"
                      width="142"
                      prop="disountOrPrice"
                    >
                      <template #default="scope">
                        <div
                          v-if="scope.row.grounpDiscountId"
                          class="flex flex1 widthP100 align_items_center pt4 pb4 pl8 pr8 white_nowrap border_radius4 overflow_hidden"
                          style="border: 1px solid #dfe2e7"
                        >
                          <div class="flex1 overflow_hidden">
                            <tooptipText
                              :text="`${
                                selectActivity.usageMethod?.value === 'DISCOUNT'
                                  ? $t('receipt.rabateM')
                                  : $t('receipt.deductionM2')
                              }${
                                selectActivity.usageMethod?.value === 'DISCOUNT'
                                  ? selectActivity.discountAmount +
                                    $t('receipt.fracture')
                                  : formatMoney(selectActivity.reducePrice)
                              }`"
                              :textClass="'c_1D2330'"
                            />
                          </div>
                          <i
                            v-if="selectActivity?.editPrice?.value === 'YES'"
                            class="iconfont-flb icon-bianji1 ml8 pointer main-color"
                            style="font-size: 16px"
                            @click="editProductDiscount(scope.row)"
                          />
                        </div>
                        <div
                          v-else
                          class="flex flex1 widthP100 align_items_center pt4 pb4 pl8 pr8 white_nowrap border_radius4 overflow_hidden"
                          style="
                            border: 1px solid #dfe2e7;
                            background-color: #dfe2e7;
                          "
                        >
                          <div class="flex1 overflow_hidden c_1D2330">-</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('receipt.discountedUnitPrice')"
                      width="94"
                      prop="discountPrice"
                    >
                      <template #default="scope">
                        <span class="fw500">
                          {{ getDiscountPrice(scope.row) }}
                        </span>
                      </template>
                    </el-table-column>
                    <!-- 积分抵扣 -->
                    <el-table-column
                      v-if="!form.isMultiDiscount && !form.isBindDiscount"
                      :label="$t('receipt.pointsDeduction')"
                      width="124"
                      prop="points"
                    >
                      <template #default="scope">
                        <el-input
                          v-if="
                            props.type !== 'price' &&
                            scope.row.stockType !== 'SECOND_HAND'
                          "
                          :disabled="pointDisabled(scope.row)"
                          :formatter="
                            (val) =>
                              numberFormat(
                                val,
                                getMin(scope.row),
                                getMax(scope.row)
                              )
                          "
                          :parser="
                            (val) =>
                              numberFormat(
                                val,
                                getMin(scope.row),
                                getMax(scope.row)
                              )
                          "
                          v-model="scope.row.points"
                          @change="(val) => pointsChange(val, scope.row)"
                          :placeholder="$t('receipt.pleaseEnter')"
                        />
                        <span v-else>{{ scope.row.points || "-" }}</span>
                        <div
                          class="mt4 c_1D2330 lh18"
                          v-if="props.type !== 'price' || scope.row.points"
                        >
                          {{ $t("receipt.deductionM") }}
                          {{
                            scope.row.pointsPrice
                              ? `${formatMoney(scope.row.pointsPrice)}`
                              : "-"
                          }}
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 小计 -->
                    <el-table-column
                      :label="$t('receipt.subtotals')"
                      width="100"
                      prop="v2"
                    >
                      <template #default="scope">
                        <span class="c_EC2D30">
                          {{ formatMoney(getTips(scope.row)) }}
                        </span>
                      </template>
                    </el-table-column>
                    <!-- 操作 -->
                    <el-table-column
                      width="64"
                      fixed="right"
                      :label="$t('receipt.operation')"
                      prop="operate"
                      v-if="props.type !== 'price'"
                      class-name="operate_column"
                    >
                      <template #default="scope">
                        <Delete
                          class="mr8 c_EC2D30 pointer"
                          style="width: 16px; height: 16px"
                          @click="deleteProduct(scope.row)"
                        />
                      </template>
                    </el-table-column>
                    <template #empty>
                      <empty-box></empty-box>
                    </template>
                  </el-table>
                </div>
              </div>
            </div>
          </div>
          <div class="right-block bgc_white border_radius8 pt24 position_re">
            <div
              class="flex1 flex flex_direction_column overflow_y_auto pb6 pl24 pr24"
            >
              <div class="common_title_with_flag mb16">
                {{ $t("receipt.discountedActivities") }}
              </div>
              <div class="alert_blue mb20">
                <el-icon class="mr8 main-color align_bottom" size="16">
                  <InfoFilled /> </el-icon
                >{{ $t("receipt.addSalesCouponTips") }}
              </div>
              <!-- 多件折扣 -->
              <template v-if="props.type !== 'price'">
                <bar-btn
                  v-if="discountRule.list.length"
                  :title="$t('receipt.useMultipleDiscount')"
                  disabled
                >
                  <template #label>
                    <el-tooltip
                      effect="dark"
                      :content="$t('receipt.multiDiscountTip')"
                      placement="top"
                    >
                      <i
                        class="iconfont-flb icon-caozuotishi ml4"
                        style="font-size: 16px; color: #3d3d3d"
                      />
                    </el-tooltip>
                  </template>
                  <template #right>
                    <el-tooltip
                      effect="dark"
                      placement="top"
                      :visible="showTip"
                      popper-class="tip-class"
                    >
                      <el-switch
                        @click.stop.prevent
                        @change="changeMulti"
                        v-model="form.isMultiDiscount"
                      />
                      <template #content>
                        <div class="flex flex_direction_column">
                          <div class="fs12 c_white">
                            {{ $t("receipt.multiDiscountTip2") }}
                          </div>
                          <div class="mt4 flex justify_content_end">
                            <el-button @click="showTip = false">
                              {{ $t("receipt.close") }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-tooltip>
                  </template>
                </bar-btn>
                <bar-btn
                  v-else
                  :title="
                    $t('receipt.cantMultiTips', {
                      name: `<span style='font-weight:550'>${$t(
                        'receipt.multiActivity'
                      )}</span>`,
                    })
                  "
                  disabled
                ></bar-btn>
              </template>
              <!-- 捆绑销售 -->
              <template v-if="props.type !== 'price'">
                <bar-btn
                  v-if="bindRule.list.length"
                  :title="$t('receipt.useBundledSale')"
                  disabled
                >
                  <template #label>
                    <el-tooltip
                      effect="dark"
                      :content="$t('receipt.bundleSaleTips2')"
                      placement="top"
                    >
                      <i
                        class="iconfont-flb icon-caozuotishi ml4"
                        style="font-size: 16px; color: #3d3d3d"
                      />
                    </el-tooltip>
                  </template>
                  <template #right>
                    <el-tooltip
                      effect="dark"
                      placement="bottom"
                      :visible="showTip2"
                      popper-class="tip-class"
                    >
                      <el-switch
                        @click.stop.prevent
                        @change="changeBindVal"
                        v-model="form.isBindDiscount"
                      />
                      <template #content>
                        <div class="flex flex_direction_column">
                          <div class="fs12 c_white">
                            {{ $t("receipt.bundleSaleTips") }}
                          </div>
                          <div class="mt4 flex justify_content_end">
                            <el-button @click="showTip2 = false">
                              {{ $t("receipt.close") }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-tooltip>
                  </template>
                </bar-btn>
                <bar-btn
                  v-else
                  :title="
                    $t('receipt.cantMultiTips', {
                      name: `<span style='font-weight:550'>${$t(
                        'receipt.bundleActivity'
                      )}</span>`,
                    })
                  "
                  disabled
                ></bar-btn>
              </template>
              <template v-if="!form.isMultiDiscount && !form.isBindDiscount">
                <!-- 兑换券 -->
                <bar-btn
                  :title="$t('receipt.exchangeVoucherDetailM')"
                  @click="operate('exchangeListDrawer')"
                  :disabled="props.type === 'price'"
                >
                  <template #right>
                    <span class="fw500">{{ getExchangeNum }}</span>
                  </template>
                </bar-btn>
                <!-- 优惠券 -->
                <bar-btn
                  :title="$t('receipt.discountM')"
                  @click="operate('couponListDrawer')"
                  :disabled="props.type === 'price'"
                >
                  <template #right>
                    <span class="fw500">{{ couponText }}</span>
                  </template>
                </bar-btn>
                <!-- 赠品 -->
                <bar-btn
                  :title="$t('receipt.giftGiveawayM')"
                  class="mb32"
                  @click="operate('giveGiftsDrawer')"
                  :disabled="props.type === 'price'"
                >
                  <template #right>
                    <span class="fw500">{{ getGiftsNum }}</span>
                  </template>
                </bar-btn>
              </template>
              <div class="common_title_with_flag mb16">
                {{ $t("receipt.paymentInformation") }}
              </div>
              <div class="flex flex_direction_column">
                <!-- 商品总价 -->
                <div class="flex justify_content_between fs14 mb12">
                  <span>{{ $t("receipt.totalPriceProductM") }}</span>
                  <span class="fw500">{{
                    formatMoney(form.originalPrice)
                  }}</span>
                </div>
                <!-- 共减 -->
                <div class="flex justify_content_between fs14 mb12">
                  <span>{{ $t("receipt.discountM") }}</span>
                  <span class="fw500">
                    {{
                      props.type === "price"
                        ? formatMoney(form.discountPrice)
                        : formatMoney(subtract(form.discountPrice, pointsPrice))
                    }}
                  </span>
                </div>
                <!-- 积分抵扣 -->
                <div
                  class="flex justify_content_between fs14 mb12"
                  v-if="pointsPrice"
                >
                  <span>{{ $t("receipt.pointsDeductionM") }}</span>
                  <span class="fw500">{{ formatMoney(pointsPrice) }}</span>
                </div>
                <!-- 应付 -->
                <div class="flex justify_content_between fs14 mb12">
                  <span>{{ $t("receipt.payableM") }}</span>
                  <span class="c_EC2D30 fw500">
                    {{ formatMoney(form.actualPrice) }}
                  </span>
                </div>
              </div>

              <!-- 修改总价 -->
              <!-- <div
                class="mt8 pt20 bt_dashed_default"
                v-if="props.type === 'price' && alterType === 'YES'"
              >
                <el-form-item
                  :label="$t('receipt.modifiedTotalOrderPriceM')"
                  prop="orderNo"
                >
                  <el-input
                    size="default"
                    v-model="form.actualPrice"
                    :placeholder="$t('receipt.pleaseEnter')"
                    :formatter="digitFormat"
                    :parser="digitFormat"
                  >
                    <template #append>
                      {{ store.getters.currencyInfo?.unit || "" }}
                    </template>
                  </el-input>
                </el-form-item>
              </div> -->

              <!-- 收款信息 -->
              <template v-if="props.type !== 'price'">
                <div class="common_title_with_flag mb16 mt20">
                  {{ $t("receipt.receiveInfo") }}
                </div>

                <el-form-item prop="orderNo">
                  <el-radio-group
                    class="flex flex_direction_column gap8 widthP100"
                    v-model="form.payType"
                    @change="changePayType"
                  >
                    <div class="card">
                      <el-radio value="1">
                        {{ $t("receipt.unionPay") }}
                      </el-radio>

                      <div
                        class="fs12 c_9EA2A9 lh18 pl22"
                        v-if="form.payType !== '1'"
                      >
                        {{ $t("receipt.uniPayTip") }}
                      </div>

                      <div
                        class="card-detail mt8 pt8 pb8 pl8 pr8"
                        v-if="form.payType === '1'"
                      >
                        <el-form-item
                          :label="$t('receipt.payType')"
                          prop="unionPay.configFinanceDataId"
                          label-position="left"
                          label-width="60"
                        >
                          <el-cascader
                            popper-class="add-order-page-cascader"
                            @change="(val) => changeConfig(val)"
                            style="width: 100%"
                            :placeholder="$t('receipt.financeConfigLabel')"
                            v-model="form.unionPay.configFinanceDataId"
                            :options="treeSelect.list"
                            :props="{
                              value: 'code',
                              label: 'name',
                              children: 'list',
                              emitPath: false,
                            }"
                            @expand-change="(val) => expande(val)"
                          />
                        </el-form-item>
                        <el-form-item
                          :label="$t('receipt.stageNum')"
                          prop="unionPay.periods"
                          label-position="left"
                          label-width="60"
                        >
                          <el-select
                            v-model="form.unionPay.periods"
                            :placeholder="$t('receipt.pleaseChoose')"
                            class="widthP100"
                          >
                            <el-option
                              v-for="item in form.unionPay.installmentValue"
                              :key="item"
                              :label="item"
                              :value="item"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item
                          :label="$t('receipt.payVoucher')"
                          prop="unionPay.attachment"
                          label-position="left"
                          label-width="60"
                        >
                          <el-upload
                            class="widthP100"
                            action=""
                            :show-file-list="false"
                            :on-change="(file) => uploadImg(file)"
                            :auto-upload="false"
                            accept=".jpg, .jpeg, .png, .gif"
                          >
                            <el-button
                              class="widthP100"
                              v-if="
                                !form.unionPay.attachment ||
                                !form.unionPay.attachment.length
                              "
                            >
                              <i
                                class="iconfont-flb icon-daochu-shangchuan mr4 main-color"
                              ></i>
                              <span class="main-color">
                                {{ $t("receipt.clickUpload") }}
                              </span>
                            </el-button>

                            <div class="file-box" v-else>
                              <i
                                class="iconfont-flb icon-fujian mr4"
                                style="font-size: 16px; color: #415fff"
                              ></i>
                              <span class="flex1 fs12 c_1D2330 overflow_hidden">
                                <tooptip-text
                                  :text="
                                    form.unionPay.attachment[0]['fileName']
                                  "
                                />
                              </span>
                            </div>
                          </el-upload>
                        </el-form-item>
                      </div>
                    </div>

                    <div class="card">
                      <el-radio value="2">
                        {{ $t("receipt.multiPay") }}
                      </el-radio>
                      <div
                        v-if="form.payType !== '2'"
                        class="fs12 c_9EA2A9 lh18 pl22"
                      >
                        {{ $t("receipt.multiPayTip") }}
                      </div>
                      <div
                        class="card-detail mt8 pt12 pb12 pl16 pr16"
                        v-if="form.payType === '2'"
                      >
                        <el-form-item
                          :label="$t('receipt.payType')"
                          label-position="left"
                          label-width="60"
                          class="pointer"
                          @click="operate('payType')"
                        >
                          <div
                            class="flex align_items_center flex1 justify_content_end"
                          >
                            <span
                              class="common-status-circle c_1D2330"
                              :class="getIsConfig ? 'green' : 'orange'"
                            >
                              {{
                                getIsConfig
                                  ? $t("receipt.configed")
                                  : $t("receipt.noConfig")
                              }}
                            </span>
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                  </el-radio-group>
                </el-form-item>
              </template>
            </div>

            <div class="flex justify_content_end pt20 pb20 pl24 pr24">
              <el-button @click="onClose">{{ $t("receipt.return") }}</el-button>
              <el-button
                v-if="props.type !== 'price'"
                :loading="btnLoading"
                @click="onSave('DRAFT')"
              >
                {{ $t("receipt.preservation") }}
              </el-button>
              <el-button
                :loading="btnLoading"
                type="primary"
                @click="onSubmit(formRef)"
              >
                {{ $t("receipt.submit") }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 注册会员 -->
    <member-register-dialog
      v-if="operateDialog.type === 'memberRegisterDialog'"
      v-model:show="operateDialog.show"
      :title="$t('receipt.registerAsAMember')"
      @confirm="memberRegisterConfirm"
    ></member-register-dialog>

    <!-- 新增商品 -->
    <add-product-dialog
      v-if="operateDialog.type === 'addProductDialog'"
      :storeId="form.storeId"
      v-model:show="operateDialog.show"
      :title="$t('receipt.addProduct')"
      :selected="selectedProduct"
      @confirm="addProductConfirm"
    ></add-product-dialog>

    <!--选择优惠券-->
    <coupon-list-drawer
      v-if="
        operateDialog.type === 'couponListDrawer' ||
        operateDialog.type === 'exchangeListDrawer'
      "
      :type="
        operateDialog.type === 'exchangeListDrawer' ? 'exchange' : 'coupon'
      "
      v-model:show="operateDialog.show"
      :memberId="form.memberId"
      :storeId="form.storeId"
      @confirm="couponListConfirm"
      :data="operateDialog.data"
      :products="selectedProduct"
      :couponProductIds="couponProductIds"
    ></coupon-list-drawer>

    <!--添加礼品-->
    <give-gifts-drawer
      v-if="operateDialog.type === 'giveGiftsDrawer'"
      :product-ids="productIds"
      :store-id="form.storeId"
      v-model:show="operateDialog.show"
      :data="props.data"
      @confirm="addGiftsConfirm"
    ></give-gifts-drawer>

    <!-- 支付方式 -->
    <pay-type-drawer
      v-if="operateDialog.type === 'payType'"
      v-model:show="operateDialog.show"
      :data="form"
      :options="treeSelect.list"
      @confirm="payTypeConfirm"
    />

    <!-- 批量串码录入 -->
    <batch-barcode-dialog
      v-if="operateDialog.type === 'batchBarcode'"
      v-model:show="operateDialog.show"
      @confirm="barCodeConfirm"
      :storeId="form.storeId"
      :productList="selectedProduct"
      :secondList="secondHandList"
    />

    <!-- 修改折扣/扣减 -->
    <edit-discount-dialog
      v-model:show="editDiscountData.show"
      :product="editDiscountData.data"
      :activity="selectActivity"
      @confirm="discountConfirm"
    />

    <!-- 修改商品数量 -->
    <product-quantity-dialog
      v-model:show="productQuantityData.show"
      :activityList="productQuantityData.list"
      :data="productQuantityData.data"
      @confirm="confirmEditQuantity"
    />

    <!-- 捆绑活动规则 -->
    <edit-bundle-dialog
      v-model:show="editBundleData.show"
      :data="editBundleData.data"
      :product="editBundleData.product"
      :selectProduct="productList"
      :storeId="form.storeId"
      @confirm="confirmBundle"
    />

    <!-- 撤回提示 -->
    <delete-dialog
      v-if="operateDialog.type === 'delProduct'"
      :title="$t('receipt.deletePrompt')"
      :content="$t('receipt.deleteProductTip')"
      v-model:show="operateDialog.show"
      @confirm="stopDialogConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeMount,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { dayjs, ElMessage } from "element-plus";
import { ArrowRight, Delete } from "@element-plus/icons-vue";
import { usePort } from "@/hooks/usePort";
import MemberRegisterDialog from "@views/salesOrderManage/components/member-register-dialog.vue";
import emptyBox from "@cp/empty/index.vue";
import AddProductDialog from "@views/salesOrderManage/components/add-product-dialog.vue";
import CouponListDrawer from "@views/salesOrderManage/components/coupon-list-drawer.vue";
import GiveGiftsDrawer from "@views/salesOrderManage/components/give-gifts-drawer.vue";
import editDiscountDialog from "@/views/salesOrderManage/components/edit-discount-dialog.vue";
import BarBtn from "@views/salesOrderManage/components/bar-btn.vue";
import {
  getDeductionRule,
  memberInfoPage,
  orderInfoSubmit,
  getOrderInfoDetail,
  updateOrderPrice,
  myCoupon,
  getStoreSelectorApi,
  calculateDiscount,
  getBarcodeType,
  getConfigFinanceTree,
  getDiscountRules,
  countMaxGroupDiscount,
  countMaxBindDiscount,
  countPriceGroupDiscount,
  countPriceBindDiscount,
  getSaleAndExaminePerson,
  querySkuActivity,
} from "@/api/salesOrderManage";
import store from "@/store";
import {
  textFormat,
  tableFormat,
  digitFormat,
  numberFormat,
  isEmpty,
} from "@/util/util";
import {
  multiply,
  round,
  add,
  subtract,
  divide,
  formatMoney,
} from "@/util/numberUtil";
import { discountType } from "./ts/enum";
import { countMaxCouponPrice } from "@/api/purchaseOrder";
import lang from "@/lang/index";
import { debounce } from "lodash";
import router from "@/router";
import website from "@/config/website";
import { getDiscountRole } from "@/api/discountRole";
import { upload } from "@/util/upload";
import tooptipText from "@/components/tooltip-text.vue";
import payTypeDrawer from "./components/pay-type-drawer.vue";
import batchBarcodeDialog from "./components/batch-barcode-dialog.vue";
import productQuantityDialog from "@/views/salesOrderManage/components/product-quantity-dialog.vue";
import editBundleDialog from "@/views/salesOrderManage/components/edit-bundle-dialog.vue";
import DeleteDialog from "@/components/dialog/delete.vue";
import { plus } from "number-precision";

const i18n = lang.global;

const { generateCode } = usePort();
const props = reactive({
  type: "",
  data: {} as any,
});

const operateDialog = reactive({
  type: "",
  show: false,
  data: null,
  loading: false,
});
const tableHeight = ref(0);
const tableRef = ref(null);
const loading = ref(false);
const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const form = ref({
  payType: "1",
  multiPay: [],
  unionPay: {
    installmentValue: [],
    attachment: [],
    bankCode: "",
    bankName: {
      f: "",
      s: "",
      t: "",
      value: "",
    },
    terminalName: {
      f: "",
      s: "",
      t: "",
      value: "",
    },
    configFinanceDataId: "",
    payWay: "",
    paymentPrice: "",
    periods: "",
    terminalCode: "",
  },
  orderPaymentChannelBaseDTOList: [],
  id: null, //id
  orderNo: "", //订单编号
  orderDate: dayjs().format("YYYY-MM-DD"), //开单日期
  storeCode: "", //门店编号
  storeId: "", //门店id
  storeName: {
    f: "",
    s: "",
    t: "",
    value: "",
  }, //门店名
  salespersonCode: "", //销售编码
  salespersonId: "", //销售id
  salespersonName: "", //销售名
  examineCode: "", //审核编码
  examineId: "", //审核id
  examineUserId: "",
  examineName: "", //审核人名
  memberCode: "", //会员编号
  memberId: "", //会员id
  memberName: "", //会员名
  orderRemarks: "", //订单备注
  currentPoints: "", //会员当前积分
  examineState: "", //审核状态（0挂起，1审核中，2通过，3不通过）,可用值:AUDIT_FAIL,AUDIT_NOW,AUDIT_SUCCESS,DRAFT
  originalPrice: 0, //原价合计
  actualPrice: 0, //实际合计
  discountPrice: 0, //优惠合计
  isMultiDiscount: false, //是否使用多件折扣
  isBindDiscount: false, //是否使用捆绑销售
});
const rules = reactive<FormRules>({
  orderNo: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnter"),
      trigger: "blur",
    },
  ],
  orderDate: [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  storeId: [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  salespersonId: [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  examineId: [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  "unionPay.configFinanceDataId": [
    {
      required: true,
      message: i18n.t("receipt.pleaseChoose"),
      trigger: "change",
    },
  ],
  // "unionPay.periods": [
  //   {
  //     required: true,
  //     message: i18n.t("receipt.pleaseChoose"),
  //     trigger: "change",
  //   },
  // ],
});
const discountRole = ref({
  auditAdjustmentPricePercent: 0, //审核人
  salesAdjustmentPricePercent: 0, //销售人
  auditFlag: 0,
}); //配置
const productList = ref([]); //商品列表
const salePersonList = ref([]); //销售人列表
const memberOpts = ref([]); //会员列表
const selectCoupon = ref([]); //选择的优惠券
const pointList = ref([]); //商品的积分数据
const giftsList = ref([]); //赠品
const pointsPrice = ref(0); //积分抵扣金额
const alterType = ref("NO"); //价格修改类型，0单价修改，1总价修改,可用值:NO,YES
const barCodeInputRef = ref<HTMLElement[]>([]); //条形输入框ref
const showTip = ref(true);
const showTip2 = ref(true);
const secondHandList = ref([]);
const treeSelect = reactive({
  loading: false,
  list: [],
});
const storeList = ref([]);
const discountRule = reactive({
  list: [],
  loading: false,
}); //团购规则
const bindRule = reactive({
  list: [],
  loading: false,
}); //捆绑销售
const selectActivity = ref({} as any); //选择的活动
const selectBind = ref({} as any); //捆绑销售
const editDiscountData = reactive({
  //折扣弹框
  show: false,
  data: {} as any,
});
const expandRowKeys = ref([]); //展开的行
const productQuantityData = reactive({
  //商品数量弹框
  show: false,
  list: [],
  data: {} as any,
});
const editBundleData = reactive({
  //捆绑活动规则
  show: false,
  data: null,
  product: null,
});
const activityData = reactive({
  loading: false,
  data: {},
});

// 是否配置支付方式（多种）
const getIsConfig = computed(() => {
  return form.value.multiPay
    .map((item) => {
      return item.configFinanceDataId;
    })
    .filter((item) => item).length;
});

const showProductList = computed({
  get() {
    // 获取显示的商品列表（包含商品 + 二手商品）
    const mergedList = productList.value.concat(secondHandList.value);
    return mergedList;
  },
  set(newList) {
    // 设置新的商品列表（你可以自定义分配逻辑）
    // 假设：把新的列表分回 productList 和 secondHandList
    newList.forEach((newItem) => {
      // 先找 productList
      const indexInProduct = productList.value.findIndex(
        (item) =>
          item.uuid === newItem.uuid && newItem.stockType !== "SECOND_HAND"
      );
      if (indexInProduct !== -1) {
        productList.value[indexInProduct] = newItem;
        return;
      }

      // 再找 secondHandList
      const indexInSecondHand = secondHandList.value.findIndex(
        (item) =>
          item.uuid === newItem.uuid && newItem.stockType === "SECOND_HAND"
      );
      if (indexInSecondHand !== -1) {
        secondHandList.value[indexInSecondHand] = newItem;
        return;
      }
    });
  },
});
const selectedProduct = computed(() => {
  return productList.value
    .map((item) => ({ ...item, id: item.skuId }))
    .filter((el) => el.stockType !== "SECOND_HAND");
});
const productIds = computed(() =>
  productList.value
    .map((item) => item.skuId)
    .filter((el) => el.stockType !== "SECOND_HAND")
); //商品ids
const couponProductIds = computed(() =>
  productList.value
    .filter((el) => {
      return el.discountTotalPrice;
    })
    .map((item) => item.skuId)
); //能使用优惠券商品ids
const getExchangeNum = computed(
  () => selectCoupon.value.filter((el) => el.couponType.code === 0).length
);
const couponText = computed(() => {
  let text = "-";
  selectCoupon.value.forEach((item) => {
    if (item.couponType.code === 1 || item.couponType.code === 2) {
      text = item?.name?.value || "";
      return;
    }
  });
  return text;
});
const getGiftsNum = computed(() =>
  giftsList.value.reduce((pre, cur) => pre + Number(cur.quantity), 0)
);

const userInfo = computed(() => {
  return store.getters.userInfo;
});

watch(
  [() => productList.value, () => secondHandList.value],
  (newVal, old) => {
    // 计算订单价格
    if (props.type !== "price") {
      let originalPrice = 0, //订单原价
        actualPrice = 0, //订单优惠后价格
        discountPrice = 0, //订单优惠价格
        pointPrice = 0; //积分抵扣金额
      newVal[0].forEach((item, index) => {
        if (form.value.isBindDiscount) {
          if (item.children) {
            // 主商品
            originalPrice = add(
              originalPrice,
              round(multiply(item.originalUnitPrice, getMainProductNum(item)))
            );
            actualPrice = add(
              actualPrice,
              round(multiply(item.originalUnitPrice, getMainProductNum(item)))
            );

            for (const key in item.children) {
              (item.children[key] || []).forEach((el) => {
                originalPrice = add(originalPrice, el.originalTotalPrice);
                actualPrice = add(actualPrice, el.discountTotalPrice);
              });
            }
          } else {
            // 辅商品
            originalPrice = add(originalPrice, item.discountTotalPrice);
            actualPrice = add(actualPrice, item.discountTotalPrice);
          }
        } else {
          let totalOriginalPrice = 0,
            totalDiscountPrice = 0;

          totalOriginalPrice = Number(item.originalTotalPrice);
          totalDiscountPrice = subtract(
            Number(item.discountTotalPrice),
            item.pointsPrice || 0
          );
          //总价
          originalPrice = add(originalPrice, totalOriginalPrice);
          actualPrice = add(actualPrice, totalDiscountPrice);
          pointPrice = add(pointPrice, item.pointsPrice || 0);
        }
      });

      // 二手商品
      newVal[1].forEach((item) => {
        let totalOriginalPrice = 0,
          totalDiscountPrice = 0;

        totalOriginalPrice = Number(item.originalTotalPrice);
        totalDiscountPrice = subtract(
          Number(item.discountTotalPrice),
          item.pointsPrice || 0
        );
        //总价
        originalPrice = add(originalPrice, totalOriginalPrice);
        actualPrice = add(actualPrice, totalDiscountPrice);
        pointPrice = add(pointPrice, item.pointsPrice || 0);
      });

      discountPrice = subtract(originalPrice, actualPrice);
      form.value.originalPrice = originalPrice;
      form.value.discountPrice = discountPrice;
      form.value.actualPrice = actualPrice;
      pointsPrice.value = pointPrice;
    } else {
      let originalPrice = 0, //订单原价
        actualPrice = 0, //订单优惠后价格
        discountPrice = 0; //订单优惠价格
      newVal[0].forEach((item) => {
        let totalOriginalPrice = 0,
          totalDiscountPrice = 0,
          num = 0;
        num = Number(item.quantity);
        // 货品总价
        totalOriginalPrice = round(
          multiply(num, Number(item.originalUnitPrice))
        );
        totalDiscountPrice = totalOriginalPrice;
        //总价
        originalPrice = add(originalPrice, totalOriginalPrice);
        actualPrice = add(actualPrice, totalDiscountPrice);
      });

      // 二手商品
      newVal[1].forEach((item) => {
        let totalOriginalPrice = 0,
          totalDiscountPrice = 0,
          num = 0;
        num = Number(item.quantity);
        // 货品总价
        totalOriginalPrice = round(
          multiply(num, Number(item.originalUnitPrice))
        );
        totalDiscountPrice = totalOriginalPrice;
        //总价
        originalPrice = add(originalPrice, totalOriginalPrice);
        actualPrice = add(actualPrice, totalDiscountPrice);
      });

      discountPrice = subtract(originalPrice, actualPrice);
      form.value.originalPrice = originalPrice;
      form.value.discountPrice = discountPrice;
      form.value.actualPrice = actualPrice;
      nextTick(() => {
        formRef.value?.clearValidate();
      });
    }
  },
  { deep: true }
);

watch(
  () => form.value.storeId,
  (val) => {
    // 清空券、商品
    productList.value = [];
    selectCoupon.value = [];
    if (val) {
      getStoreSelector(val);
      getPayType();
    }
  }
);

// 价格修订todo
watch(
  [() => selectCoupon.value, () => giftsList.value, () => productList.value],
  () => {
    // 价格修订判断能否修改商品单价
    if (props.type === "price") {
      if (
        selectCoupon.value.length ||
        giftsList.value.length ||
        productList.value.findIndex((item) => item.pointsPrice) > -1
      ) {
        alterType.value = "YES";
      } else {
        alterType.value = "NO";
      }
    }
  },
  { deep: true }
);

onBeforeMount(() => {
  //收起左侧菜单
  store.commit("common/SET_COLLAPSE");
});

onMounted(() => {
  let data = {} as any;
  try {
    data = JSON.parse(sessionStorage.getItem("addSaleData"));
  } catch (e) {
    console.log(e, "error");
    data = {};
  }
  props.type = data?.type;
  props.data = data?.data;
  getStore();
  getDiscount();
  formRef.value?.resetFields();
  if (props.data?.id) {
    getDetail();
  } else {
    generateCode("openOrder").then((res) => {
      form.value.orderNo = res;
    });
  }
  getTableHeight();
  window.addEventListener("resize", debounce(getTableHeight, 0));
});

onUnmounted(() => {
  window.removeEventListener("resize", debounce(getTableHeight, 0));
  //打开左侧菜单
  store.commit("common/SET_COLLAPSE");
});

const blurPrice = (row: any) => {
  const discount =
    props.type === "price"
      ? discountRole.value.auditAdjustmentPricePercent
      : discountRole.value.salesAdjustmentPricePercent;
  row.originalUnitPrice =
    discount === null
      ? row.referenceUnitPrice
      : row.originalUnitPrice <
        round(multiply(row.referenceUnitPrice, subtract(1, Number(discount))))
      ? round(multiply(row.referenceUnitPrice, subtract(1, Number(discount))))
      : row.originalUnitPrice > row.referenceUnitPrice
      ? row.referenceUnitPrice
      : row.originalUnitPrice;

  coputedDiscount(true);
};

/**
 * 获取积分抵扣最小值
 * @param row - 当前行
 * @returns {number} - 最小值
 */
const getMin = (row: any) => {
  return 0;
};

/**
 * 获取积分抵扣最大值
 * @param {Object} row - 当前行数据
 * @returns {number} - 返回当前行商品的最大可用积分抵扣值，如果商品不存在返回0
 */
const getMax = (row: any) => {
  const val = pointList.value.find((item) => item.productId == row.skuId);
  if (!val) return null;
  // 最大抵扣金额
  const price = multiply(
    multiply(
      Number(row.originalUnitPrice),
      divide(Number(val.maxDeductionRate), 100)
    ),
    Number(row.quantity)
  );

  // 已使用积分
  let usedPoint = 0;
  productList.value.forEach((item) => {
    if (item.points && item.skuId !== row.skuId) {
      usedPoint = add(usedPoint, Number(item.points));
    }
  });
  const point = subtract(Number(form.value.currentPoints), usedPoint);

  // 最大抵扣积分( 最多抵扣积分  该商品最多使用积分   会员积分 )
  return Math.min(
    multiply(price, Number(val.pointsRequired)),
    point,
    Number(form.value.currentPoints)
  );
};

/**
 * 判断积分兑换是否可用
 * @param {Object} row - 当前行数据
 * @returns {Boolean} - 如果兑换数量等于商品数量，返回true表示积分兑换不可用
 */
const pointDisabled = (row: any) => {
  const ids = pointList.value.map((item) => item.productId);
  return Number(row.discountUnitPrice) === 0 || !ids.includes(row.skuId);
};

/**
 * 获取商品的小记
 * @param {Object} row -当前行数据
 * @returns {String} tips
 */
const getTips = (row: any) => {
  // 数量*优惠单价-积分抵扣金额

  if (row.children) {
    // 捆绑活动
    return round(multiply(getMainProductNum(row), row.originalUnitPrice));
  } else {
    return round(
      subtract(Number(row.discountTotalPrice), row.pointsPrice || 0)
    );
  }
};

/**
 * 获取优惠单价
 * @param {Object} row -当前行数据
 */
const getDiscountPrice = (row: any) => {
  if (round(row.discountUnitPrice) === round(row.originalUnitPrice)) {
    return "-";
  } else {
    // row.discountUnitPrice = row.discountUnitPrice;
  }
  // return `￥${row.discountUnitPrice}`;
  return formatMoney(row.discountUnitPrice);
};

function getDiscount() {
  getDiscountRole({})
    .then((res) => {
      if (res.code == 200) {
        discountRole.value.auditAdjustmentPricePercent = Number(
          res.data.auditAdjustmentPricePercent || 0
        );
        discountRole.value.salesAdjustmentPricePercent = Number(
          res.data.salesAdjustmentPricePercent || 0
        );
        discountRole.value.auditFlag = res.data.auditFlag || 0;
        rules.examineId[0].required = discountRole.value.auditFlag === 1;
        setTimeout(() => {
          formRef.value?.clearValidate();
        }, 0);
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((res) => {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
}

/**
 * 获取门店
 *
 * @return {void}
 */
const getStore = () => {
  getStoreSelectorApi({}).then((res: any) => {
    if (res.code !== 200) return;
    let data = res.data ? res.data : [];
    data = data.map((ele: any) => {
      return {
        ...ele,
        nameValue: ele.name,
        name: ele.name.value,
      };
    });
    storeList.value = data;
    if (storeList.value.length === 1) {
      form.value.storeId = storeList.value[0].id;
      changeStore(storeList.value[0].id);
    }
  });
};

/**
 * @description 编辑态获取数据
 */
const getDetail = () => {
  loading.value = true;
  getOrderInfoDetail({
    orderNo: props.data.orderNo,
  }).then((res) => {
    loading.value = false;
    if (res.code == 200) {
      const data = res.data;
      searchMember(data.memberName || "", true);

      const groupVal = (data.discountList || []).find(
        (d) => d.discountType?.value === "GROUP_BUYING"
      ) as any;
      const bindVal = (data.discountList || []).find(
        (d) => d.discountType?.value === "BIND_DISCOUNT"
      ) as any;
      data.orderPaymentChannelBaseDTOList.forEach((el) => {
        el.attachment = el.attachment ? JSON.parse(el.attachment) : [];
      });

      form.value = {
        orderPaymentChannelBaseDTOList: [],
        payType: data.paymentGroupType + "",
        unionPay:
          data.paymentGroupType + "" === "1" &&
          data.orderPaymentChannelBaseDTOList.length
            ? data.orderPaymentChannelBaseDTOList[0]
            : {},
        multiPay:
          data.paymentGroupType + "" === "2" &&
          data.orderPaymentChannelBaseDTOList.length
            ? data.orderPaymentChannelBaseDTOList
            : [],
        isMultiDiscount: false,
        isBindDiscount: false,
        id: data.id,
        orderNo: data.orderNo,
        orderDate: data.orderDate,
        storeCode: data.storeCode,
        storeId: data.storeId,
        storeName: data.storeName,
        salespersonCode: data.salespersonCode,
        salespersonId: data.salespersonId,
        salespersonName: data.salespersonName,
        examineCode: data.examineCode,
        examineId: data.examineId,
        examineUserId: data.examineUserId,
        examineName: data.examineName,
        memberCode: data.memberCode,
        memberId: data.memberId,
        memberName: data.memberName,
        orderRemarks: data.orderRemarks,
        currentPoints: "",
        examineState: "",
        originalPrice: data.originalPrice,
        actualPrice: data.actualPrice,
        discountPrice: data.discountPrice,
      };
      if (props.type === "price") {
        form.value.actualPrice = isEmpty(data.modifyActualPrice)
          ? data.actualPrice
          : data.modifyActualPrice;
      }
      const list1 = [],
        list2 = [],
        list3 = [];

      const bindProductObj = {};
      const waitAdd = [];
      res.data.skuList.forEach((el) => {
        if (el.marketingActivityId) {
          if (
            !el.marketingMainSkuId &&
            !res.data.skuList.some(
              (c) => c.skuId === el.skuId && !c.marketingActivityId
            )
          ) {
            waitAdd.push({
              ...el,
              quantity: 0,
              marketingActivityId: null,
              marketingDiscountAmount: null,
              marketingMainSkuId: null,
              marketingReducePrice: null,
              marketingUseMethod: null,
              marketingUseNum: null,
              marketingActivityName: null,
              barcodeList: null,
            });
          }

          bindProductObj[el.marketingActivityId] =
            bindProductObj[el.marketingActivityId] || [];
          delete el.returnState;
          delete el.delFlag;
          bindProductObj[el.marketingActivityId].push({
            ...el,
            productName: el.skuName,
            productCode: el.skuCode,
            // item.isBarcode = item.isBarcode.value;
            isGiveaway: el.isGiveaway.value,
            stockType: el.stockType?.value,
            marketingUseMethod: el?.marketingUseMethod?.value,
          });
        }
      });
      res.data.skuList.push(...waitAdd);
      for (const key in bindProductObj) {
        (bindProductObj[key] || []).forEach((el) => {
          const index = res.data.skuList.findIndex(
            (c) =>
              ((c.skuId === el.skuId && !el.marketingMainSkuId) ||
                c.skuId === el.marketingMainSkuId) &&
              !c.marketingActivityId
          );
          if (index !== -1) {
            res.data.skuList[index].children =
              res.data.skuList[index]?.children || {};
            res.data.skuList[index].children[key] =
              res.data.skuList[index].children[key] || [];

            res.data.skuList[index].children[key].push(el);
          }
        });
      }

      res.data.skuList.forEach((item: any) => {
        if (item.marketingActivityId) {
          return;
        }
        let num = 0;
        if (item.children) {
          for (const key in item.children) {
            (item.children[key] || []).forEach((c) => {
              if (c.skuId === item.skuId) {
                num = add(num, c.quantity);

                if (item.isBarcode?.value === "YES") {
                  item.barcodeList = item.barcodeList || [];
                  item.barcodeList.push(...c.barcodeList);
                  c.barcodeList = null;
                }
              }
            });
          }
        }

        item.quantity = add(item.quantity, num);

        item.productName = item.skuName;
        item.productCode = item.skuCode;
        // item.isBarcode = item.isBarcode.value;
        item.isGiveaway = item.isGiveaway.value;
        item.stockType = item.stockType?.value;
        item.barcodeList = (item.barcodeList || []).map((el: any) => {
          return {
            ...el,
            stockType: el.stockType?.value,
          };
        });
        if (props.type == "price" && !isEmpty(item.modifyUnitPrice)) {
          item.originalUnitPrice = item.modifyUnitPrice;
          item.discountUnitPrice = item.modifyUnitPrice;
        }

        if (groupVal) {
          // 处理团购活动
          const ids = groupVal?.skuId?.split(",");
          if (item.skuId && ids.includes(item.skuId)) {
            item.grounpDiscountId = groupVal?.groupBuyingId;
          }
        }

        delete item.countTotalPrice;
        delete item.modifyTotalPrice;
        delete item.modifyUnitPrice;
        delete item.returnOrderNo;
        delete item.returnState;
        delete item.delFlag;

        if (item.isGiveaway === "NO") {
          if (item.stockType === "SECOND_HAND") {
            list3.push(item);
          } else {
            list1.push(item);
          }
        } else {
          list2.push(item);
        }
      });

      nextTick(() => {
        addProductConfirm(list1, async () => {
          if (!bindVal) {
            const disList = [];
            (data.discountList || []).forEach((el) => {
              el.discountAmount = el.discountRate; //折扣
              el.couponType = {
                ...el.discountType,
                value:
                  el.discountType.value.split("_")[1] || el.discountType.value,
              }; //类型
              el.minPurchase = el.deductionAmountMin; //满减条件
              el.minPurchaseAmount = el.deductionValue; //满减金额
              el.discountType = el.couponType.value;
              el.name = {
                value: el.discountName,
              };
              el.couponProductVoList = (el.skuId || "")
                .split(",")
                .map((item) => ({ productId: item }));
              delete el.returnState;
              delete el.delFlag;

              if (el.couponType && el.couponType.code == 3) {
                productList.value.forEach((item) => {
                  const ids = el.skuId.split(",");
                  if (ids.includes(item.skuId)) {
                    item.pointsPrice = el.pointsPrice;
                    item.points = el.usePoints;
                  }
                });
              } else {
                disList.push(el);
              }
            });
            selectCoupon.value = disList.filter(
              (d) => d.discountType !== "BUYING"
            );
          }

          if (props.type !== "price") {
            // 价格修订不需要重新计算
            if (groupVal) {
              // 处理团购活动
              form.value.isBindDiscount = false;
              form.value.isMultiDiscount = true;
              await getDiscountRule(groupVal);
              coputedDiscount(false);
            } else {
              coputedDiscount(true);
            }
            if (bindVal) {
              // 处理团购活动
              form.value.isBindDiscount = true;
              form.value.isMultiDiscount = false;
              await getDiscountRule();
              coputedDiscount(false);
            } else {
              coputedDiscount(true);
            }
          }
        });
        giftsList.value = list2;
        secondHandList.value = list3;
      });
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取积分抵扣规则
 */
const getRules = () => {
  getDeductionRule(productList.value.map((item) => item.skuId)).then((res) => {
    pointList.value = res.data || [];
    // currencyDeducted  可抵扣的货币金额（整数）
    // maxDeductionRate  单人抵扣上限比例
    // pointsRequired  所需积分数
    // productId  商品id
    // ruleId  积分规则id
    // ruleName  规则名称
  });
};

/**
 *  originalUnitPrice 货品修改原价  变动->计算  originalTotalPrice 货品优惠前总价  discountTotalPrice 货品优惠后总价
 * @param {Object} row - 当前行数据
 */
const priceChange = (row: any) => {
  row.originalTotalPrice = round(multiply(row.originalUnitPrice, row.quantity));
};

const priceInput = (row: any) => {
  row.originalUnitPrice = round(Number(row.originalUnitPrice));
  row.discountUnitPrice = round(row.originalUnitPrice);
};

function changeMulti(val) {
  if (val) {
    // 去除优惠券
    selectCoupon.value = [];
    form.value.isBindDiscount = false;
    clearBindActivity();
    computedActivity();
  } else {
    selectActivity.value = {};
    coputedDiscount(true);
  }
}

function changeBindVal(val) {
  if (val) {
    // 去除优惠券
    selectCoupon.value = [];
    form.value.isMultiDiscount = false;
    computedActivity();
  } else {
    selectBind.value = {};
    clearBindActivity(false);
    coputedDiscount(true);
  }
}

// 清除商品绑定
const clearBindActivity = (isFetch = true) => {
  const waitList = [];
  productList.value.forEach((el) => {
    if (el.children) {
      for (const key in el.children) {
        (el.children[key] || []).forEach((item) => {
          if (item.skuId !== el.skuId) {
            const c = waitList.findIndex((c) => c.skuId === item.skuId);
            if (c !== -1) {
              waitList[c].quantity = add(
                Number(waitList[c].quantity || 0),
                Number(item.quantity || 0)
              );
            } else {
              waitList.push({
                ...item,
                marketingActivityId: null,
                marketingDiscountAmount: null,
                marketingMainSkuId: null,
                marketingReducePrice: null,
                marketingUseMethod: null,
                marketingUseNum: null,
                children: null,
                marketingActivityName: null,
              });
            }
          }
        });
      }
      delete el.children;
    }

    return {
      ...el,
      marketingActivityId: null,
      marketingDiscountAmount: null,
      marketingMainSkuId: null,
      marketingReducePrice: null,
      marketingUseMethod: null,
      marketingUseNum: null,
      marketingActivityName: null,
      children: null,
    };
  });

  waitList.forEach((el, index2) => {
    const index = productList.value.findIndex(
      (c) => c.skuId + "" === el.skuId + ""
    );
    if (index !== -1) {
      waitList.splice(index2, 1);
      productList.value[index].quantity = plus(
        Number(productList.value[index]?.quantity || 0),
        Number(el?.quantity || 0)
      );
    }
  });
  productList.value.push(...waitList);

  if (isFetch) {
    getDiscountRule();

    getActivity();
  }
};

const computedActivity = () => {
  // 根据活动修改商品属性
  if (form.value.isMultiDiscount) {
    if (!discountRule.list.length) {
      return;
    }
    const params = formateParams("DRAFT") as any;
    params.orderDiscountType = "GROUP_DISCOUNT";
    params.discountList = discountRule.list.map((item) => {
      const ids = (item.activityMultipleRuleProductVOList || []).map(
        (el) => el.productId
      );
      return {
        discountName: item.ruleName || "",
        groupBuyingId: item.id,
        discountType: "GROUP_BUYING",
        groupBuyingInfo: {
          ...item,
          groupBuyingSkuIdList: ids,
          status: item?.status?.value,
          usageMethod: item?.usageMethod?.value,
          usageType: item?.usageType?.value,
        },
        skuId: ids.filter((item) => productIds.value.includes(item)).join(","),
      };
    });
    if (form.value.isMultiDiscount) {
      countMaxGroupDiscount(params)
        .then((res) => {
          if (res.code === 200) {
            selectActivity.value =
              discountRule.list.find((el) => el.id == res.data.groupBuyingId) ||
              {};

            coputedDiscount();
          } else {
            ElMessage.error(
              res?.msg || res.data?.msg || i18n.t("receipt.networkError")
            );
          }
        })
        .catch((e) => {
          ElMessage.error(
            e?.msg || e.data?.msg || i18n.t("receipt.networkError")
          );
        });
    }
  } else if (form.value.isBindDiscount) {
    productList.value.forEach((el) => {
      el.originalUnitPrice = round(el.originalUnitPrice);
      el.originalTotalPrice = round(
        multiply(Number(el.originalUnitPrice), Number(el.quantity))
      );

      el.discountUnitPrice = el.originalUnitPrice;
      el.discountTotalPrice = el.originalTotalPrice;

      let num = 0;

      if (el.children) {
        for (const key in el.children) {
          (el.children[key] || []).forEach((item) => {
            if (item.skuId === el.skuId) {
              num = add(num, item.quantity);
            }

            item.originalUnitPrice = round(item.originalUnitPrice);
            item.originalTotalPrice = round(
              multiply(Number(item.originalUnitPrice), Number(item.quantity))
            );
            if (item.marketingUseMethod === "FREE") {
              item.discountTotalPrice = 0;
            } else if (item.marketingUseMethod === "ORIGINAL_PRICE") {
              item.discountTotalPrice = item.originalTotalPrice;
            } else if (item.marketingUseMethod === "DISCOUNT") {
              item.discountTotalPrice = round(
                multiply(
                  divide(item.marketingDiscountAmount, 100),
                  item.originalTotalPrice
                )
              );
            } else if (item.marketingUseMethod === "REDUCE_PRICE") {
              item.discountTotalPrice = round(
                subtract(
                  item.originalTotalPrice,
                  multiply(item.marketingReducePrice, item.marketingUseNum || 0)
                )
              );
            }
            item.discountUnitPrice = round(
              divide(item.discountTotalPrice, Number(item.quantity))
            );
          });
        }
      }
      el.quantity = Math.max(el.quantity, num);
    });
  }
};

/**
 * @description: 积分修改计算抵扣金额
 * @param {*} val 值
 * @param {*} row
 */
const pointsChange = (val: any, row: any) => {
  const value = pointList.value.find((item) => item.productId == row.skuId);
  if (value) {
    const pointRate = round(Number(value.pointsRequired));
    row.pointsPrice = round(divide(Number(val), pointRate));
  }
};

/**
 *  quantity 货品数量  变动->计算  originalTotalPrice 货品优惠前总价   discountTotalPrice 货品优惠后总价
 * @param {Object} row - 当前行数据
 */
const quantityChange = (row: any, val: any) => {
  if (form.value.isBindDiscount) {
    const num = getChildProductNum(row);
    if (val < num) {
      val = num;
      row.quantity = num;
      productQuantityData.data = row;
      productQuantityData.list = getHasActivityList(row);
      productQuantityData.show = true;
    }
  }

  row.originalTotalPrice = round(multiply(row.originalUnitPrice, row.quantity));
  nextTick(() => {
    coputedDiscount(true);
  });
};

// 优化接口
let storageProduct = "";
let storageProductIds = "";

/**
 * 计算商品优惠
 * @param {Object} row - 当前行数据
 * @param {Boolean} isCompute - 是否计算最优惠券
 */
const coputedDiscount = async (isCompute = false) => {
  // 单独处理二手商品
  secondHandList.value = secondHandList.value.map((item, index) => {
    const quantity = (item.barcodeList || []).length || 0;
    return {
      ...item,
      quantity: quantity,
      originalTotalPrice: round(
        multiply(Number(item.originalUnitPrice), quantity)
      ),
      discountUnitPrice: Number(item.originalUnitPrice),
      discountTotalPrice: round(
        multiply(Number(item.originalUnitPrice), quantity)
      ),
      uuid: item.skuId + "" + index,
    };
  });

  if (!productList.value.length) {
    selectCoupon.value = [];
    giftsList.value = [];

    discountRule.list = [];
    bindRule.list = [];
    form.value.isMultiDiscount = false;

    form.value.isBindDiscount = false;
    selectActivity.value = null;
    activityData.data = {};
    return;
  }
  productList.value.forEach((item, index) => {
    item.uuid = item.skuId + "" + index;

    // 活动清空积分
    if (form.value.isMultiDiscount || form.value.isBindDiscount) {
      item.originalUnitPrice = item.referenceUnitPrice;
      item.originalTotalPrice = round(
        multiply(Number(item.originalUnitPrice), Number(item.quantity || 0))
      );
      if (item.points) {
        item.pointsPrice = null;
        item.points = null;
      }
    }

    if (item.isBarcode?.value === "YES") {
      item.quantity = (item.barcodeList || []).length || 0;
    }
  });

  if (isCompute) {
    // 计算最优

    getDiscountRule();
    getActivity();

    if (form.value.isMultiDiscount || form.value.isBindDiscount) {
      // 活动
      computedActivity();
    } else {
      // 普通优惠
      couponListConfirm([]);
      if (!form.value.memberId || !form.value.storeId) {
        return;
      }
      productList.value.forEach((item) => {
        item.discountUnitPrice = round(Number(item.originalUnitPrice));
        item.discountTotalPrice = round(
          multiply(Number(item.quantity || 0), Number(item.discountUnitPrice))
        );
      });
      const couponList = [];
      loading.value = true;
      myCoupon({
        memberId: form.value.memberId, //用户id会员
        storeId: form.value.storeId, //门店id
      })
        .then((res) => {
          if (res.code === 200) {
            const maxProductIds = [
              ...productList.value
                .filter((el) => {
                  return Number(el.discountTotalPrice) !== 0;
                })
                .map((item) => item.skuId),
            ];
            res.data.forEach((el) => {
              if (
                el.couponType
                // &&
                // (el.couponType.value == "REDUCTION" ||
                //   el.couponType.value == "DISCOUNT")
              ) {
                // 满减、折扣
                const ids = (el.couponProductVoList || []).map(
                  (item) => item.productId
                );

                if (ids.length) {
                  if (
                    ids.some((item) => (maxProductIds || []).includes(item))
                  ) {
                    const skuIds = maxProductIds.filter((item) =>
                      ids.includes(item)
                    );

                    //  skuIds 数组去重
                    const uniqueArr = [...new Set(skuIds)];
                    couponList.push({ ...el, skuId: uniqueArr.join(",") });
                  }
                }
              }
            });

            // couponList 和 selectCoupon 去重
            const uniqueArray = [...couponList, ...selectCoupon.value].reduce(
              (acc, current) => {
                // 检查acc中是否已经存在id相同的对象
                if (!acc.some((item) => item.id === current.id)) {
                  acc.push(current);
                }
                return acc;
              },
              []
            );
            couponListConfirm(uniqueArray, false);
            const params = formateParams("DRAFT");
            delete params.orderPaymentChannelBaseDTOList;
            countMaxCouponPrice({
              ...params,
              // discountList: params.discountList.filter(
              //   (el) => el.couponType.code !== 0
              // ),
              skuList: params.skuList
                .map((el) => {
                  // const exchangeNum = selectCoupon.value.reduce(
                  //   (pre, current) => {
                  //     if (
                  //       current.couponType.code === 0 &&
                  //       (current.skuId || "").includes(el.skuId.toString())
                  //     ) {
                  //       return pre + 1;
                  //     } else {
                  //       return pre;
                  //     }
                  //   },
                  //   0
                  // );
                  return {
                    ...el,
                    quantity: Number(el.quantity),
                  };
                })
                .filter((el: any) => Number(el.quantity) > 0),
            })
              .then((res) => {
                if (res.code === 200) {
                  loading.value = false;
                  if (res.data && res.data.length) {
                    const maxCoupon = [...selectCoupon.value].reduce(
                      (acc, current) => {
                        // 检查acc中是否已经存在id相同的对象
                        if (
                          res.data.some(
                            (item) => item.discountCode === current.id
                          )
                        ) {
                          acc.push(current);
                        }
                        return acc;
                      },
                      []
                    );
                    if (maxCoupon && maxCoupon.length) {
                      couponListConfirm([...maxCoupon]);
                    }
                  } else {
                    couponListConfirm([]);
                  }
                } else {
                  ElMessage.error(res.msg || i18n.t("receipt.networkError"));
                  loading.value = false;
                  couponListConfirm([]);
                }
              })
              .catch((res) => {
                ElMessage.error(res.msg || i18n.t("receipt.networkError"));
                loading.value = false;
                couponListConfirm([]);
              });
          } else {
            ElMessage.error(res.msg || i18n.t("receipt.networkError"));
            loading.value = false;
          }
        })
        .catch((res) => {
          ElMessage.error(res.msg || i18n.t("receipt.networkError"));
          loading.value = false;
        });
    }
  } else {
    // 根据优惠券处理商品优惠价
    productList.value.forEach((item) => {
      item.originalTotalPrice = round(
        multiply(Number(item.originalUnitPrice), Number(item.quantity || 0))
      );
      item.discountUnitPrice = round(Number(item.originalUnitPrice));
      item.discountTotalPrice = round(
        multiply(Number(item.quantity || 0), Number(item.discountUnitPrice))
      );
    });

    if (form.value.isMultiDiscount) {
      // 活动

      if (!selectActivity.value) {
        return;
      }
      const params = formateParams("DRAFT") as any;

      params.orderDiscountType = "GROUP_DISCOUNT";
      const ids =
        (selectActivity.value?.activityMultipleRuleProductVOList || [])
          ?.filter((el) => {
            if (selectActivity.value?.usageType?.value === "SINGLE_PRODUCT") {
              const product = productList.value.find(
                (c) => c.skuId === el.productId
              );
              if (product) {
                return (
                  Number(product.quantity || 0) >=
                  Number(selectActivity.value?.productNum || 0)
                );
              }
              return false;
            } else {
              return true;
            }
          })
          .map((item) => item.productId) ?? [];
      params.discountList = [selectActivity.value].map((item: any) => {
        let type = "";
        type = "GROUP_BUYING";
        const newList = (item?.activityMultipleRuleProductVOList || []).map(
          (item) => ({
            ...item,
            type: item?.type?.value,
            usageMethod: item?.usageMethod?.value,
          })
        );
        return {
          discountName: item.ruleName || "",
          groupBuyingId: item.id,
          discountType: type,
          groupBuyingInfo: {
            ...item,
            bindRuleList: newList,
            activityMultipleRuleProductVOList: newList,
            groupBuyingSkuIdList: ids,
            status: item?.status?.value,
            usageMethod: item?.usageMethod?.value,
            usageType: item?.usageType?.value,
          },
          skuId: ids
            .filter((item) => productIds.value.includes(item))
            .join(","),
        };
      });
      countPriceGroupDiscount(params)
        .then((res) => {
          if (res.code === 200) {
            productList.value.forEach((item) => {
              const pro = (res.data?.skuList || []).find(
                (el) => el.skuId === item.skuId
              );
              if (pro) {
                item.discountUnitPrice = round(Number(pro.discountUnitPrice));
                item.discountTotalPrice = round(
                  multiply(
                    Number(item.quantity || 0),
                    Number(item.discountUnitPrice)
                  )
                );
                if (ids.includes(item.skuId)) {
                  item.grounpDiscountId = selectActivity.value.id;
                } else {
                  item.grounpDiscountId = null;
                }
              }
            });
          } else {
            ElMessage.error(
              res?.msg || res.data?.msg || i18n.t("receipt.networkError")
            );
          }
        })
        .catch((e) => {
          ElMessage.error(
            e?.msg || e.data?.msg || i18n.t("receipt.networkError")
          );
        });
    } else if (form.value.isBindDiscount) {
      computedActivity();
    } else {
      // 普通优惠
      selectCoupon.value.forEach((coupon) => {
        const ids = (coupon.couponProductVoList || []).map(
          (item) => item.productId
        );
        const hasIds = [];
        if (
          coupon.couponType.code === 0 &&
          productIds.value.some((a) => ids.includes(a))
        ) {
          // 兑换

          // 找到  1、价格最高 2、能够兑换 的商品
          const product = productList.value.reduce((pre, cur) => {
            if (
              Number(cur.originalUnitPrice || 0) >
                Number((pre && pre.originalUnitPrice) || 0) &&
              Number(cur.discountUnitPrice || 0) !== 0 &&
              ids.includes(cur.skuId)
            ) {
              return cur;
            } else {
              return pre;
            }
          }, null);

          if (product && product.quantity) {
            hasIds.push(product.skuId);
            product.discountTotalPrice = subtract(
              Number(product.discountTotalPrice),
              Number(product.originalUnitPrice)
            );
            product.discountUnitPrice =
              Number(product.discountTotalPrice) === 0
                ? 0
                : round(
                    divide(
                      Number(product.discountTotalPrice),
                      Number(product.quantity)
                    )
                  );
          }
          coupon.skuId = [...new Set(hasIds)].join(",");
        } else if (
          coupon.couponType.code === 1 &&
          productIds.value.some((a) => ids.includes(a))
        ) {
          // 折扣

          // 找到  1、价格最高 2、能够折扣 的商品
          const product = productList.value.reduce((pre, cur) => {
            if (
              Number(cur.originalUnitPrice || 0) >
                Number((pre && pre.originalUnitPrice) || 0) &&
              Number(cur.discountUnitPrice || 0) !== 0 &&
              ids.includes(cur.skuId)
            ) {
              return cur;
            } else {
              return pre;
            }
          }, null);
          if (product && product.quantity) {
            hasIds.push(product.skuId);
            product.discountTotalPrice = subtract(
              Number(product.discountTotalPrice),
              round(
                multiply(
                  Number(product.originalUnitPrice),
                  subtract(1, divide(Number(coupon.discountAmount || 100), 100))
                )
              )
            );
            product.discountUnitPrice =
              Number(product.discountTotalPrice) === 0
                ? 0
                : round(
                    divide(
                      Number(product.discountTotalPrice),
                      Number(product.quantity)
                    )
                  );
          }
          coupon.skuId = [...new Set(hasIds)].join(",");
        } else if (
          coupon.couponType.code === 2 &&
          productIds.value.some((a) => ids.includes(a))
        ) {
          // 满减

          // 1、找到能够满减的商品，平均分配满减金额
          let totalAmountForEligibleItems = 0; //符合条件商品的总金额
          let eligibleItems = []; //符合条件的商品

          // 第一步：计算所有符合条件商品的总金额
          productList.value.forEach((item) => {
            if (ids.includes(item.skuId) && item.quantity) {
              eligibleItems.push({
                item,
              });
              totalAmountForEligibleItems += Number(item.discountTotalPrice); // 累加符合条件的商品总金额
            }
          });

          // 符合满减条件
          if (totalAmountForEligibleItems >= Number(coupon.minPurchase || 0)) {
            // 第二步：计算满减金额，并分配到每个商品

            // 剩余分配金额
            let remainPrice = totalAmountForEligibleItems;

            eligibleItems.forEach(({ item }, index) => {
              let discountAmount = 0;

              // 只有一个商品 全额分配
              if (eligibleItems.length === 1) {
                discountAmount = Number(coupon.minPurchaseAmount || 0);
              } else if (index !== eligibleItems.length - 1) {
                // 计算该商品在总金额中的比例
                let itemDiscount = round(
                  Number(item.discountTotalPrice) / totalAmountForEligibleItems
                );
                // 计算分配到该商品的满减金额
                discountAmount = round(
                  multiply(itemDiscount, Number(coupon.minPurchaseAmount || 0))
                );

                // 计算剩余分配金额
                remainPrice =
                  Number(coupon.minPurchaseAmount || 0) - discountAmount;
              } else {
                // 最后一个防止小数金额溢出
                discountAmount = remainPrice;
              }

              // 设置每个商品的 discountTotalPrice 和 discountUnitPrice
              item.discountTotalPrice = subtract(
                Number(item.discountTotalPrice),
                round(Number(discountAmount))
              );
              item.discountUnitPrice =
                Number(item.discountTotalPrice) === 0
                  ? 0
                  : round(
                      divide(
                        Number(item.discountTotalPrice),
                        Number(item.quantity)
                      )
                    );

              // 更新 coupon.skuId
              coupon.skuId = coupon.skuId ? coupon.skuId.split(",") : [];
              coupon.skuId.push(item.skuId);
              coupon.skuId = [...new Set(coupon.skuId)].join(",");
            });
          }
        }
      });
    }
  }
};

/**
 * 获取门店相关人员
 * @param {string | number} storeId 门店id
 */
const getStoreSelector = (storeId: string | number) => {
  if (!storeId) return;
  getSaleAndExaminePerson({ storeId: storeId }).then((res) => {
    if (res.code == 200) {
      salePersonList.value = res?.data || [];
      const salePerson = salePersonList.value.find(
        (el) => userInfo.value?.id === el.sysUserId
      );
      if (salePerson) {
        form.value.salespersonCode = salePerson.sysUserCode;
        form.value.salespersonId = salePerson.sysUserPersonId;
        form.value.salespersonName = salePerson.sysUserName;
      }
    } else {
      salePersonList.value = [];
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 根据提供的搜索字符串，分页查询会员信息并更新会员选项。
 *
 * @param {string} str - 搜索字符串，用于过滤会员信息。
 */
const searchMember = (str: string, isEdit = false) => {
  const params = {
    searchStr: str,
    pageNo: 1,
    pageSize: 100,
  };
  memberInfoPage(params).then((res) => {
    if (res.code == 200) {
      const list = res.data.records;
      memberOpts.value = list;
      if (isEdit) {
        const memberInfo = memberOpts.value.find(
          (item) => item.memberCode == form.value.memberCode
        );
        if (memberInfo) {
          form.value.currentPoints = memberInfo.currentPoints;
        }
      }
    } else {
      ElMessage.error(res.msg || i18n.t("receipt.networkError"));
    }
  });
};

/**
 * 会员选择赋值，
 * @param {string} val - 选择的会员code
 */
const memberChange = (val: string) => {
  const memberInfo = memberOpts.value.find((item) => item.memberCode == val);
  if (memberInfo) {
    form.value.currentPoints = memberInfo.currentPoints;
    form.value.memberId = memberInfo.id; //会员id
    form.value.memberName = memberInfo.nickname; //会员名
  } else {
    form.value.currentPoints = "";
    form.value.memberId = ""; //会员id
    form.value.memberName = ""; //会员名
  }

  // 清空券
  selectCoupon.value = [];
  productList.value.forEach((item) => {
    if (item.points) {
      item.pointsPrice = 0;
      item.points = 0;
    }
  });
  coputedDiscount(true);
  getRules();
};

/**
 * 审核人选择赋值
 * @param {string} val - 选择的审核人id
 */
const changeExaminePerson = (val: string) => {
  const examineInfo = salePersonList.value.find(
    (el) => val === el.sysUserPersonId
  );
  if (examineInfo) {
    form.value.examineCode = examineInfo.sysUserCode;
    form.value.examineId = examineInfo.sysUserPersonId;
    form.value.examineName = examineInfo.sysUserName;
    form.value.examineUserId = examineInfo.sysUserId;
  } else {
    form.value.examineCode = "";
    form.value.examineId = "";
    form.value.examineName = "";
    form.value.examineUserId = "";
  }
};

/**
 * 门店择赋值
 * @param {string} val - 选择的门店id
 */
const changeStore = (val: string) => {
  const storeInfo = storeList.value.find((item) => item.id == val);

  if (storeInfo) {
    form.value.storeCode = storeInfo.code;
    form.value.storeName = storeInfo.nameValue;
    form.value.storeId = storeInfo.id;
  }

  form.value.examineCode = "";
  form.value.examineId = "";
  form.value.examineUserId = "";
  form.value.examineName = "";
};

/**
 * 销售人选择赋值
 * @param {string} val - 选择的销售人id
 */
const changeSalePerson = (val: string) => {
  const salePersonInfo = salePersonList.value.find(
    (el) => val === el.sysUserPersonId
  );
  if (salePersonInfo) {
    form.value.salespersonCode = salePersonInfo.sysUserCode;
    form.value.salespersonId = salePersonInfo.sysUserPersonId;
    form.value.salespersonName = salePersonInfo.sysUserName;
  }
};

/**
 * 注册会员成功的回调，
 * @param {Object} data - 注册成功的会员信息，
 */
const memberRegisterConfirm = (data) => {
  searchMember(null);
  form.value.memberCode = data.memberCode;
  form.value.currentPoints = data.currentPoints;
  form.value.memberId = data.id; //会员id
  form.value.memberName = data.nickname; //会员名

  // 清空券
  selectCoupon.value = [];
  productList.value.forEach((item) => {
    if (item.points) {
      item.pointsPrice = 0;
      item.points = 0;
    }
  });
  coputedDiscount(true);
  getRules();
};

/**
 * 将新商品数据添加到 productList 中，并去除重复的商品项。
 * @param {Array} data - 待添加的商品数组。
 */
const addProductConfirm = (data, callback?: any) => {
  productList.value = data.map((el) => ({
    // id: el.id,
    ...el,
    skuId: el.skuId || el.id,
    skuCode: el.skuCode || el.productCode,
    skuName: el.skuName || el.productName,
    isBarcode: el.isBarcode,
    unitId: el.unitId,
    unitName: el.unitName,
    quantity:
      el.quantity ||
      (el.isBarcode?.value === "YES" || !getQuantity(el) ? 0 : 1), //货品数量
    referenceUnitPrice: round(
      Number(el.referenceUnitPrice || el.originalUnitPrice || el.price || 0)
    ), //货品原价
    originalUnitPrice: round(Number(el.originalUnitPrice || el.price || 0)), //货品原价
    discountUnitPrice: round(Number(el.discountUnitPrice || el.price || 0)), //货品优惠后价格

    originalTotalPrice: round(
      Number(
        el.originalTotalPrice ||
          multiply(
            Number(el.price),
            Number(
              el.quantity ||
                (el.isBarcode?.value === "YES" || !getQuantity(el) ? 0 : 1)
            )
          ) ||
          0
      )
    ), //货品优惠前总价
    discountTotalPrice: round(
      Number(
        el.discountTotalPrice ||
          multiply(
            Number(el.price),
            Number(
              el.quantity ||
                (el.isBarcode?.value === "YES" || !getQuantity(el) ? 0 : 1)
            )
          ) ||
          0
      )
    ), //货品优惠后总价
    stockQuantity: el.stockQuantity, //库存
  }));
  // 去除productList中id重复的项
  productList.value = productList.value.filter((item, index, self) => {
    return self.findIndex((t) => t.skuId === item.skuId) === index;
  });

  getRules();
  if (callback) {
    callback();
  } else {
    coputedDiscount(true);
  }
};

// 获取商品营销活动列表（全部）
const getActivity = () => {
  if (!form.value.storeId) {
    return;
  }
  const list = [];

  (productList.value || []).forEach((item) => {
    list.push(Number(item.skuId));
  });

  list.sort((a, b) => {
    return a - b;
  });

  if (storageProductIds !== JSON.stringify(list)) {
    // 优化接口
    storageProductIds = JSON.stringify(list);
  } else {
    return;
  }

  activityData.loading = true;
  querySkuActivity({
    storeId: form.value.storeId,
    data: productList.value.map((item) => item.skuId || item.id),
  })
    .then((res) => {
      if (res.code === 200) {
        activityData.data = res.data || {};
      }
      activityData.loading = false;
    })
    .catch((res) => {
      activityData.loading = false;
      ElMessage.error(res.msg || i18n.t("receipt.networkError"));
    });
};

// 获取团购规则列表
const getDiscountRule = async (val = null) => {
  if (!form.value.storeId) {
    return;
  }

  const list2 = [];

  // 需要带上子商品去查询活动
  const list = (productList.value || []).reduce((acc, cur) => {
    list2.push({
      id: Number(cur.skuId),
      quantity: cur.quantity,
    });

    acc.push(cur);
    if (cur?.children) {
      for (const key in cur.children) {
        (cur.children[key] || []).forEach((el) => {
          acc.push(el);
        });
      }
    }
    return acc;
  }, []);

  list2.sort((a, b) => {
    return a.id - b.id;
  });

  if (storageProduct !== JSON.stringify(list2)) {
    // 优化接口
    storageProduct = JSON.stringify(list2);
  } else {
    return;
  }

  discountRule.loading = true;
  bindRule.loading = true;

  await Promise.all([
    await getDiscountRules({
      storeId: form.value.storeId,
      data: productList.value.map((item) => ({
        productId: item.skuId || item.id,
        productNum: item.quantity,
      })),
    }).then((res) => {
      discountRule.loading = false;
      if (res.code === 200) {
        discountRule.list = res.data?.multipleDiscountRuleList || [];
        if (!discountRule.list.length) {
          form.value.isMultiDiscount = false;
        }
        if (val) {
          selectActivity.value =
            discountRule.list.find((el) => el.id == val?.groupBuyingId) || {};
        }
      }
    }),
    await getDiscountRules({
      storeId: form.value.storeId,
      data: list.map((item) => ({
        productId: item.skuId || item.id,
        productNum: item.quantity,
      })),
    }).then((res) => {
      bindRule.loading = false;
      if (res.code === 200) {
        bindRule.list = res.data?.bindRuleList || [];
        if (!bindRule.list.length) {
          form.value.isBindDiscount = false;
        }
      }
    }),
  ]).then((res) => {
    // coputedDiscount(isComputed);
  });
};

// 获取符合商品的团购规则
const getProductDiscount = (row) => {
  return discountRule.list.filter((el) => {
    if (el.usageType?.value === "SINGLE_PRODUCT") {
      const obj = (el.activityMultipleRuleProductVOList || []).find(
        (c) => c.productId === row.skuId
      );
      return obj && Number(row.quantity || 0) >= Number(el.productNum || 0);
    }
    return (el.activityMultipleRuleProductVOList || [])
      .map((c) => c.productId)
      .includes(row.skuId);
  });
};

// 获取符合商品的捆绑销售规则
// const getProductBind = (row) => {
//   return bindRule.list.filter((el) =>
//     (el.activityMultipleRuleProductVOList || [])
//       .map((c) => c.productId)
//       .includes(row.skuId)
//   );
// };

// 修改商品折扣
const changeDiscount = (val, row) => {
  selectActivity.value = discountRule.list.find((el) => el.id == val) || {};
  if (selectActivity.value) {
    coputedDiscount();
  }
};

// 修改捆绑销售
// const changeBind = (val, row) => {
//   selectBind.value = bindRule.list.find((el) => el.id == val) || {};
//   if (selectBind.value) {
//     coputedDiscount();
//   }
// };

/**
 * 新增、编辑、删除礼品回调
 * @param {Array} data - 最终的礼品
 */
const addGiftsConfirm = (data) => {
  giftsList.value = data;
};

/**
 * 删除商品项
 * @param {Object} row - 待删除的商品项
 */
const deleteProduct = (row) => {
  if (form.value.isBindDiscount && row.children) {
    operateDialog.type = "delProduct";
    operateDialog.data = row;
    operateDialog.show = true;
    return;
  }

  if (row.stockType === "SECOND_HAND") {
    secondHandList.value = secondHandList.value.filter(
      (item) => item.skuId !== row.skuId
    );
  } else {
    productList.value = productList.value.filter(
      (item) =>
        item.skuId !== row.skuId ||
        (item.skuId === row.skuId && item.stockType !== row.stockType)
    );
    coputedDiscount(true);
  }
};

/**
 * 选择优惠券回调 -处理优惠券
 * @param {Array} value - 选择的优惠券
 */
const couponListConfirm = (value, iscompute = true) => {
  const data = JSON.parse(JSON.stringify(value));
  console.log(data, "传入优惠券列表");

  data.forEach((item) => {
    item.discountCode = item.id;
  });
  // 获取兑换券，折扣券，满减券列表
  const couponList = selectCoupon.value.filter(
    (item) =>
      item.couponType.code === 0 ||
      item.couponType.code === 1 ||
      item.couponType.code === 2
  );

  // 比较data得出差集
  const removed = couponList.filter(
    (item1) => !data.some((item2) => item1.discountCode === item2.discountCode)
  );

  const added = data.filter(
    (item2) =>
      !couponList.some((item1) => item1.discountCode === item2.discountCode)
  );

  // 0兑换券，1折扣券，2满减券，3积分抵扣
  // discountUnitPrice 货品优惠后价格
  // discountTotalPrice 货品优惠后总价

  console.log(removed, "移除的优惠券");
  console.log(added, "添加的优惠券");

  /* 优惠券处理  start */
  // 去除
  removed.forEach((coupon) => {
    selectCoupon.value = selectCoupon.value.filter(
      (item) => item.discountCode !== coupon.discountCode
    );
  });

  // 添加
  added.forEach((coupon) => {
    selectCoupon.value.push({
      ...coupon,
      discountCode: coupon.discountCode, //卷码
      discountName: coupon.name?.value || "", //优惠券名称
      discountType: discountType[coupon.couponType.code], //优惠类型 可用值:COUPON_DISCOUNT,COUPON_EXCHANGE,COUPON_THRESHOLD_DISCOUNT,REDEEM_POINTS
      id: coupon.id,
    });
  });
  // 对优惠券列表排序，兑换券在前，折扣券和满减在后，最后是积分抵扣
  selectCoupon.value.sort((a, b) => {
    return a.couponType.code - b.couponType.code;
  });
  if (iscompute) {
    coputedDiscount();
  }

  /* 优惠券处理  end */
  operateDialog.show = false;
};

/**
 * 操作dialog/drawer
 * @param {string} type - dialog/drawer类型
 */
const operate = (type: string) => {
  switch (type) {
    case "payType":
      operateDialog.type = "payType";
      operateDialog.show = true;
      break;
    case "memberRegisterDialog":
      operateDialog.type = "memberRegisterDialog";
      operateDialog.show = true;
      break;
    case "addProductDialog":
      if (!form.value.storeId) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectAStoreFirst"));
        return;
      }
      operateDialog.type = type;
      operateDialog.show = true;
      break;
    case "couponListDrawer":
    case "exchangeListDrawer":
      if (props.type == "price") {
        return;
      }
      if (!form.value.storeId) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectAStoreFirst"));
        return;
      }
      if (!form.value.memberId) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectAMemberFirst"));
        return;
      }
      operateDialog.data = selectCoupon.value;
      operateDialog.type = type;
      operateDialog.show = true;
      break;
    case "giveGiftsDrawer":
      if (props.type == "price") {
        return;
      }
      if (!form.value.storeId) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectAStoreFirst"));
        return;
      }
      if (!productIds.value.length) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectTheProductFirst"));
        return;
      }
      operateDialog.type = type;
      operateDialog.show = true;
      break;
    case "batchBarcode":
      if (!form.value.storeId) {
        ElMessage.warning(i18n.t("receipt.pleaseSelectAStoreFirst"));
        return;
      }
      operateDialog.type = type;
      operateDialog.show = true;
      operateDialog.loading = false;
      break;
    default:
      operateDialog.type = type;
      operateDialog.show = true;
      break;
  }
};

/**
 * 格式化提交参数
 * @returns {Object} - 格式化后的优惠券/积分抵扣列表
 */
const formateParams = (type: string) => {
  let discountList = [...selectCoupon.value];

  const waitList = [];
  productList.value.forEach((item) => {
    if (item.pointsPrice) {
      discountList.push({
        discountType: "REDEEM_POINTS",
        memberId: form.value.memberId,
        orderNo: form.value.orderNo,
        pointsPrice: item.pointsPrice,
        usePoints: item.points,
        skuId: item.skuId,
      });
    }
    item.orderNo = form.value.orderNo;

    if (form.value.isBindDiscount) {
      const num = getMainProductNum(item);

      if (item.children) {
        for (const key in item.children) {
          const index = Object.keys(item.children).findIndex((c) => c === key);

          const bindItem = getBindActivityList(item).find((c) => c.id === key);
          if (bindItem) {
            const ids = [];
            waitList.push(
              ...(item.children[key] || []).map((d) => {
                // 计算barcodeList
                if (item.isBarcode?.value === "YES" && d.skuId === item.skuId) {
                  if (index === 0) {
                    if (num) {
                      item.factBarCode = item.barcodeList.slice(0, num);
                      item.unCodeList = item.barcodeList.slice(num);
                    } else {
                      item.factBarCode = [...item.barcodeList];
                      item.unCodeList = [...item.barcodeList];
                    }
                  }
                  d.factBarCode = item.unCodeList.slice(0, d.quantity);
                  item.unCodeList = item.unCodeList.slice(d.quantity);
                }
                return d;
              })
            );
            (item.children[key] || []).forEach((el) => {
              ids.push(el.skuId);
            });

            discountList.push({
              discountName: bindItem.ruleName || "",
              discountType: "BIND_DISCOUNT",
              groupBuyingId: bindItem.id,
              groupBuyingInfo: {
                bindRuleList: (
                  bindItem.activityMultipleRuleProductVOList || []
                ).map((c) => ({
                  ...c,
                  isBarcode: c?.isBarcode?.value,
                  type: c?.type?.value,
                  usageMethod: c?.usageMethod?.value,
                })),
              },
              skuId: ids.join(","),
            });
          }
        }
      }
    }
  });

  let discountType = "";
  if (form.value.isMultiDiscount) {
    discountType = "GROUP_DISCOUNT";
  } else if (form.value.isBindDiscount) {
    discountType = "BIND_DISCOUNT";
  } else {
    discountType = "NORMAL_DISCOUNT";
  }

  const params = {
    ...form.value,

    orderDiscountType: discountType, //订单优惠类型

    examineCode: discountRole.value.auditFlag
      ? form.value.examineCode || ""
      : "", //审核编码
    examineId: discountRole.value.auditFlag ? form.value.examineId || "" : "", //审核id
    examineUserId: discountRole.value.auditFlag
      ? form.value.examineUserId || ""
      : "",
    examineName: discountRole.value.auditFlag
      ? form.value.examineName || ""
      : "", //审核人名

    discountList: form.value.isMultiDiscount
      ? (selectActivity.value?.id ? [selectActivity.value] : []).map((item) => {
          const ids = (item.activityMultipleRuleProductVOList || [])
            .filter((el) => {
              const product = productList.value.find(
                (c) => c.skuId === el.productId
              );
              return (
                product &&
                Number(product.quantity || 0) >= Number(item?.productNum || 0)
              );
            })
            .map((el) => el.productId);
          return {
            discountName: item.ruleName || "",
            groupBuyingId: item.id,
            discountType: "GROUP_BUYING",
            groupBuyingInfo: {
              ...item,
              groupBuyingSkuIdList: ids,
              status: item?.status?.value,
              usageMethod: item?.usageMethod?.value,
              usageType: item?.usageType?.value,
            },
            skuId: ids
              .filter((item) => productIds.value.includes(item))
              .join(","),
          };
        })
      : form.value.isBindDiscount
      ? discountList
      : discountList.map((item) => ({
          ...item,
          discountRate: item.discountAmount,
          orderNo: form.value.orderNo,
          deductionAmountMin: item.minPurchase,
          deductionValue: item.minPurchaseAmount,
          couponType: item.couponType?.value,
          status: item.status?.value,
          couponProductVoList: (item?.couponProductVoList || []).map((c) => ({
            ...c,
            isBarcode: c.isBarcode?.value,
          })),
        })), //优惠信息
    skuList: [
      ...productList.value,
      ...waitList,
      ...giftsList.value,
      ...secondHandList.value,
    ]
      .map((el) => {
        delete el.specConfDto;
        let num = el.quantity,
          orginTotal = el.originalTotalPrice,
          discountTotal = el.discountTotalPrice;
        if (form.value.isBindDiscount) {
          if (el.children && Object.keys(el.children).length !== 0) {
            num = getMainProductNum(el);
            orginTotal = round(multiply(num, el.originalUnitPrice));
            discountTotal = orginTotal;
          }
        }
        return {
          ...el,
          barcodeList:
            form.value.isBindDiscount &&
            (el.marketingActivityId ||
              (el.children && Object.keys(el.children).length !== 0))
              ? el.factBarCode && el.factBarCode.length
                ? el.factBarCode.map((c) => ({
                    ...c,
                    isGiveaway: el?.isGiveaway?.value || el?.isGiveaway,
                    delFlag: el?.delFlag?.value,
                    returnState: el?.returnState?.value,
                    stockType: c.stockType ? c.stockType : "NEW",
                  }))
                : null
              : el.barcodeList && el.barcodeList.length
              ? el.barcodeList.map((c) => ({
                  ...c,
                  isGiveaway: el?.isGiveaway?.value || el?.isGiveaway,
                  delFlag: el?.delFlag?.value,
                  returnState: el?.returnState?.value,
                  stockType: c.stockType ? c.stockType : "NEW",
                }))
              : null,
          stockType: el.stockType ? el.stockType : "NEW",
          enabled: el.enabled?.value || "",
          isGift: el.isGift?.value || "",
          isBarcode: el.isBarcode?.value || el.isBarcode,
          quantity: num,
          originalTotalPrice: orginTotal,
          discountTotalPrice: discountTotal,
          children: null,
        };
      })
      .filter((el) => el.quantity && el.quantity !== 0), //货品信息
    examineState: type, //审核状态（0挂起，1审核中，2通过，3不通过）,可用值:AUDIT_FAIL,AUDIT_NOW,AUDIT_SUCCESS,DRAFT
    paymentGroupType: form.value.payType,
    orderPaymentChannelBaseDTOList: (form.value.payType === "1"
      ? [form.value.unionPay]
      : form.value.multiPay
    ).map((item) => ({
      ...item,
      attachment: item.attachment ? JSON.stringify(item.attachment) : "[]",
      paymentPrice:
        form.value.payType === "1" ? form.value.actualPrice : item.paymentPrice,
      periods: item.periods || null,
      // payWayName: item.payWay,
    })),
    isUseMarketing: form.value.isBindDiscount ? "YES" : "NO",
  };
  delete params.multiPay;
  delete params.unionPay;
  delete params.payType;
  delete params.isMultiDiscount;
  delete params.isBindDiscount;
  return params;
};

/**
 * 提交表单并验证其有效性。
 *
 * @param {FormInstance | undefined} formEl - 待验证的表单实例。
 * 如果表单有效，将状态设置为审核中并调用保存函数。
 */
const onSubmit = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      if (props.type == "price") {
        const params: any = {
          alterType: alterType.value,
          orderNo: form.value.orderNo,
          operatorUserCode: userInfo.value?.code || "",
        };
        if (alterType.value === "YES") {
          params.afterTotalPrice = form.value.actualPrice;
        } else {
          params.afterUnitPriceList = [
            ...productList.value,
            ...secondHandList.value,
          ].map((item) => ({
            afterUnitPrice: item.originalUnitPrice,
            skuId: item.skuId,
            id: item.id,
          }));
        }
        btnLoading.value = true;
        updateOrderPrice(params).then((res) => {
          btnLoading.value = false;
          if (res.code == 200) {
            ElMessage.success(i18n.t("receipt.operationSuccess"));
            onClose();
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        onSave("AUDIT_NOW");
      }
    }
  });
};

/**
 * 添加新条码。
 *
 * @param {Object} row - 包含条码列表的行对象。
 * @return {void}
 */
const addBarCode = (row: any) => {
  if (row.barcodeList && row.barcodeList.length) {
    row.barcodeList.push({
      isEdit: true,
      skuId: row.skuId,
      stockType: row.stockType,
    });
  } else {
    row.barcodeList = [
      { isEdit: true, skuId: row.skuId, stockType: row.stockType },
    ];
  }
  nextTick(() => {
    barCodeInputRef.value[row.barcodeList.length - 1].focus();
  });
};

/**
 * 设定条码输入元素的ref
 *
 * @param {HTMLElement} el - 条码输入元素。
 * @param {number} index - 条码输入引用的索引。
 */
const getBarcodeInputRef = (el: any, index: number) => {
  if (el) {
    barCodeInputRef.value[index] = el;
  }
};

/**
 * 条码输入框失焦后保存（包含去重）。
 *
 * @param {Object} item - 包含条码的对象。
 * @param {number} index - 条码的对象的索引。
 * @param {Object} row - 行对象。
 * @return {void}
 */
const barCodeInputBlur = async (item, index, row) => {
  if (!item.barcode) {
    // 输入为空删除
    row.barcodeList.splice(index, 1);
    row.quantity = row.barcodeList.length;
    coputedDiscount();
  } else {
    checkCodeAdd(item, index, row);
  }
};

/**
 * 删除条码。
 *
 * @param {Object} item - 条码对象。
 * @param {number} index - 条码对象的索引。
 * @param {Object} row - 包含条码列表的行对象。
 * @return {void}
 */
const deleteBarCode = (item, index, row) => {
  if (getMainProductNum(row) <= 0) {
    productQuantityData.data = row;
    productQuantityData.list = getHasActivityList(row);
    productQuantityData.show = true;
    return;
  }

  row.barcodeList.splice(index, 1);
  row.quantity = row.barcodeList.length;
  coputedDiscount(true);
};

const checkCodeAdd = async (item, index, row) => {
  await getBarcodeType({
    barcodeList: [item.barcode],
  })
    .then((res) => {
      if (res.code == 200) {
        if ((res.data || []).length) {
          if (res.data[0].stockType?.value === "SECOND_HAND") {
            // 二手价判断
            let price = 0;
            try {
              price = Number(JSON.parse(row.otherPrice)?.SECOND_PRICE);
              if (!price) {
                row.barcodeList.splice(index, 1);
                ElMessage.warning(i18n.t("receipt.secondHandTip"));
                return;
              }
            } catch (e) {
              row.barcodeList.splice(index, 1);
              ElMessage.warning(i18n.t("receipt.secondHandTip"));
              return;
            }

            const secondItem = secondHandList.value.findIndex((el) => {
              return el.skuId === row.skuId && el.stockType === "SECOND_HAND";
            });
            if (secondItem === -1) {
              if (!(row.secondHandQuantity || 0)) {
                row.barcodeList.splice(index, 1);
                ElMessage.warning(i18n.t("receipt.stockNumTip"));
                return;
              }
              const newItem = {
                ...row,
                stockType: "SECOND_HAND",
                barcodeList: [
                  {
                    ...row.barcodeList[index],
                    isEdit: false,
                    barcode: item.barcode,
                    stockType: "SECOND_HAND",
                  },
                ],
                quantity: 1,
                pointsPrice: null,
                points: null,
                price: price,
                originalUnitPrice: price,
                referenceUnitPrice: price,
                originalTotalPrice: price,
                discountUnitPrice: price,
                discountTotalPrice: price,
              };
              secondHandList.value.push(newItem);
              row.barcodeList.splice(index, 1);

              productList.value = productList.value.filter(
                (el) =>
                  (el.isBarcode?.value === "YES" &&
                    el.barcodeList?.filter((c) => c.barcode).length) ||
                  el.isBarcode?.value === "NO"
              );
            } else {
              if (!(row.secondHandQuantity || 0)) {
                row.barcodeList.splice(index, 1);
                ElMessage.warning(i18n.t("receipt.stockNumTip"));
                return;
              }
              const list = (
                secondHandList.value[secondItem]?.barcodeList || []
              ).map((el) => el.barcode);
              if (list.includes(item.barcode)) {
                item.isEdit = false;

                if (row.barcodeList?.[index]?.stockType === "SECOND_HAND") {
                  if (
                    row.barcodeList.filter((c) => c.barcode === item.barcode)
                      .length > 1
                  ) {
                    row.barcodeList.splice(index, 1);
                    ElMessage.warning(i18n.t("receipt.barcodeTip2"));
                    return;
                  }
                } else {
                  // 串码已存在
                  row.barcodeList.splice(index, 1);
                  ElMessage.warning(i18n.t("receipt.barcodeTip2"));
                  return;
                }
              } else {
                if (!secondHandList.value[secondItem].barcodeList) {
                  secondHandList.value[secondItem].barcodeList = [];
                }
                secondHandList.value[secondItem].barcodeList.push({
                  ...row.barcodeList[index],
                  isEdit: false,
                  barcode: item.barcode,
                  stockType: "SECOND_HAND",
                });
              }
            }
            coputedDiscount();
          } else {
            const newItem = productList.value.findIndex((el) => {
              return el.skuId === row.skuId && el.stockType !== "SECOND_HAND";
            });
            if (newItem === -1) {
              if (!(row.newQuantity || 0)) {
                row.barcodeList.splice(index, 1);
                ElMessage.warning(i18n.t("receipt.stockNumTip"));
                return;
              }

              let price = 0;
              try {
                price = Number(res.data[0].price);
              } catch (e) {
                console.log(e, "error");
              }

              productList.value.push({
                ...row,
                stockType: "NEW",
                barcodeList: [
                  {
                    ...row.barcodeList[index],
                    isEdit: false,
                    barcode: item.barcode,
                    stockType: "NEW",
                  },
                ],
                quantity: 1,
                pointsPrice: null,
                points: null,
                price: price,
                originalUnitPrice: price,
                referenceUnitPrice: price,
                originalTotalPrice: price,
                discountUnitPrice: price,
                discountTotalPrice: price,
              });
              row.barcodeList.splice(index, 1);
            } else {
              if (!(row.newQuantity || 0)) {
                row.barcodeList.splice(index, 1);
                ElMessage.warning(i18n.t("receipt.stockNumTip"));
                return;
              }
              const list = (productList.value[newItem]?.barcodeList || []).map(
                (el) => el.barcode
              );
              if (list.includes(item.barcode)) {
                item.isEdit = false;
                if (row.barcodeList?.[index]?.stockType !== "SECOND_HAND") {
                  if (
                    row.barcodeList.filter((c) => c.barcode === item.barcode)
                      .length > 1
                  ) {
                    row.barcodeList.splice(index, 1);
                    ElMessage.warning(i18n.t("receipt.barcodeTip2"));
                    return;
                  }
                } else {
                  // 串码已存在
                  row.barcodeList.splice(index, 1);
                  ElMessage.warning(i18n.t("receipt.barcodeTip2"));
                  return;
                }
              } else {
                if (!productList.value[newItem].barcodeList) {
                  productList.value[newItem].barcodeList = [
                    {
                      ...row.barcodeList[index],
                      isEdit: false,
                      barcode: item.barcode,
                      stockType: "NEW",
                    },
                  ];
                } else {
                  productList.value[newItem].barcodeList.push({
                    ...row.barcodeList[index],
                    isEdit: false,
                    barcode: item.barcode,
                    stockType: "NEW",
                  });
                }
              }
            }
            coputedDiscount(true);
          }
        } else {
          ElMessage.warning(i18n.t("receipt.barcodeTip"));
          row.barcodeList.splice(index, 1);
          coputedDiscount();
        }
      } else {
        ElMessage.error(res.msg || i18n.t("receipt.networkError"));
        row.barcodeList.splice(index, 1);
        coputedDiscount();
      }
    })
    .catch((res) => {
      ElMessage.error(res.msg || i18n.t("receipt.networkError"));
      row.barcodeList.splice(index, 1);
      coputedDiscount();
    });
};

/**
 * 处理条码输入框回车事件。（保存并新增条码）
 *
 * @param {Object} item - 条码对象。
 * @param {number} index - 条码对象的索引。
 * @param {Object} row - 包含条码列表的行对象。
 * @return {void}
 */
const barCodeInputEnter = async (item, index, row) => {
  if (!item.barcode) {
    return;
  } else {
    await checkCodeAdd(item, index, row);
    addBarCode(row);
  }
};

// 保存
const onSave = (type: string) => {
  if (selectCoupon.value.find((el) => !el.skuId)) {
    ElMessage.error(
      i18n.t("receipt.exchangeTips", {
        count: selectCoupon.value.filter(
          (el) => el.skuId && el.discountType == "COUPON_EXCHANGE"
        ).length,
      })
    );
    return;
  }

  if (type === "AUDIT_NOW") {
    if (!productList.value.length && !secondHandList.value.length) {
      return ElMessage.warning(i18n.t("receipt.addProductTips"));
    }

    let hasEdit = false;
    let isBreak = false;
    productList.value.forEach((item) => {
      if (isBreak) {
        return;
      }
      if (item.originalUnitPrice !== item.referenceUnitPrice) {
        hasEdit = true;
      }
      if (!item.quantity) {
        isBreak = true;
        ElMessage.warning(
          `${i18n.t("receipt.commodity")} ${item.skuName?.value} ${i18n.t(
            "receipt.pleaseEnterQuantity"
          )}`
        );
      }
      if (!item.originalUnitPrice) {
        isBreak = true;

        ElMessage.warning(
          `${i18n.t("receipt.commodity")} ${item.skuName?.value} ${i18n.t(
            "receipt.pleaseEnterPrice"
          )}`
        );
      }
    });
    if (isBreak) {
      return;
    }
    if (form.value.payType === "2" && !getIsConfig.value) {
      ElMessage.warning(i18n.t("receipt.payTypeTip"));
      return;
    }

    if (
      hasEdit &&
      discountRole.value.auditFlag === 2 &&
      !form.value.examineId
    ) {
      ElMessage.warning(i18n.t("receipt.pleaseChooseAudit"));
      return;
    }
  }

  const params = formateParams(type);

  let price = 0;
  params.orderPaymentChannelBaseDTOList.forEach((item) => {
    price = add(price, Number(item.paymentPrice));
  });
  if (price !== Number(params.actualPrice)) {
    ElMessage.warning(i18n.t("receipt.paymentTypeTip"));
    return;
  }

  btnLoading.value = true;
  if (type === "DRAFT") {
    orderInfoSubmit(params)
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(i18n.t("receipt.operationSuccess"));
          onClose();
        } else {
          ElMessage.error(res.msg);
        }
        btnLoading.value = false;
      })
      .catch((res) => {
        btnLoading.value = false;
        ElMessage.error(
          res?.data?.msg || res?.msg || i18n.t("receipt.networkError")
        );
      });
  } else {
    if (form.value.isMultiDiscount || form.value.isBindDiscount) {
      orderInfoSubmit(params)
        .then((res) => {
          if (res.code == 200) {
            ElMessage.success(i18n.t("receipt.operationSuccess"));
            onClose();
          } else {
            ElMessage.error(res.msg);
          }
          btnLoading.value = false;
        })
        .catch((res) => {
          btnLoading.value = false;
          ElMessage.error(
            res?.data?.msg || res?.msg || i18n.t("receipt.networkError")
          );
        });
    } else {
      calculateDiscount(params)
        .then((res) => {
          if (res.code == 200) {
            if (
              Number(params.actualPrice) === Number(res.data.actualPrice) &&
              Number(params.originalPrice) === Number(res.data.originalPrice)
            ) {
              orderInfoSubmit(params)
                .then((res) => {
                  if (res.code == 200) {
                    ElMessage.success(i18n.t("receipt.operationSuccess"));
                    onClose();
                  } else {
                    ElMessage.error(res.msg);
                  }
                  btnLoading.value = false;
                })
                .catch((res) => {
                  btnLoading.value = false;
                  ElMessage.error(
                    res?.data?.msg || res?.msg || i18n.t("receipt.networkError")
                  );
                });
            } else {
              btnLoading.value = false;
              ElMessage.error(
                res?.data?.msg || res?.msg || i18n.t("receipt.networkError")
              );
            }
          } else {
            ElMessage.error(res.msg);
            btnLoading.value = false;
          }
        })
        .catch((res) => {
          btnLoading.value = false;
          ElMessage.error(
            res?.data?.msg || res?.msg || i18n.t("receipt.networkError")
          );
        });
    }
  }
};

// 关闭
const onClose = () => {
  router.back();
};

const getTableHeight = () => {
  setTimeout(() => {
    tableHeight.value = tableRef.value.clientHeight - 9;
  }, 200);
};

function getQuantity(row: any) {
  if (row.isBarcode?.value === "YES") {
    if (row.stockType !== "SECOND_HAND") {
      return row.newQuantity || 0;
    } else {
      return row.secondHandQuantity || 0;
    }
  } else {
    return row.stockQuantity;
  }
}

function getPayType() {
  treeSelect.loading = true;
  getConfigFinanceTree({ storeId: form.value.storeId })
    .then((res) => {
      treeSelect.loading = false;

      if (res.code == 200) {
        treeSelect.list = formatTreeData(res.data || []);
        treeSelect.list = pruneTree(treeSelect.list);

        if (form.value.unionPay.configFinanceDataId) {
          const val = findNodePath(
            treeSelect.list,
            form.value.unionPay.configFinanceDataId
          );
          form.value.unionPay.installmentValue = val[2].installmentValue
            ? val[2].installmentValue.split(",")
            : [];
        }
        form.value.multiPay.forEach((el) => {
          if (el.configFinanceDataId) {
            const val = findNodePath(treeSelect.list, el.configFinanceDataId);
            el.installmentValue = val[2].installmentValue
              ? val[2].installmentValue.split(",")
              : [];
          }
        });
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((res) => {
      treeSelect.loading = false;
      ElMessage.error(res.msg || i18n.t("receipt.networkError"));
    });
}

/**
 * 过滤tree
 * @param {Array<Object>} nodes   当前层的节点数组
 * @param {Number} depth          当前深度（根层 = 1）
 * @param {String} childKey       子节点字段名（默认 "list"）
 * @returns {Array<Object>}       裁剪后的新数组
 */
function pruneTree(nodes, depth = 1, childKey = "list") {
  if (!Array.isArray(nodes)) return [];

  // ① 到第 3 层：直接滤掉 enabled === 1
  if (depth === 3) {
    return nodes.filter((node) => !(node && node.enabled === 1));
  }

  // ② 其他层：递归处理子节点，再回收“空父亲”
  const result = [];
  for (const node of nodes) {
    if (!node) continue; // 忽略 null / undefined

    const children = pruneTree(node[childKey] || [], depth + 1, childKey);
    if (children.length) {
      // 只有子节点还存在才保留自己
      // 浅拷贝，避免污染原数据
      result.push({ ...node, [childKey]: children });
    }
  }
  return result;
}

function formatTreeData(data) {
  return data.map((item) => {
    const newItem = {
      ...item,
      code: item.id || item.code,
      oName: item.name,
      name: item?.name?.value || item.name || item.code,
    };
    if (newItem.list && newItem.list.length > 0) {
      newItem.list = formatTreeData(newItem.list);
    }
    return newItem;
  });
}

function findNodePath(data, targetCode) {
  const result = [];

  function dfs(nodes, path) {
    for (const node of nodes) {
      const newPath = [...path, node];
      if (node.code === targetCode && node.isEnd === 1) {
        result.push(...newPath);
        return true;
      }
      if (node.list && dfs(node.list, newPath)) {
        return true;
      }
    }
    return false;
  }

  dfs(data, []);
  return result;
}

function changeConfig(value) {
  const val = findNodePath(treeSelect.list, value);
  if (val) {
    form.value.unionPay.installmentValue = val[2].installmentValue
      ? val[2].installmentValue.split(",")
      : [];
    form.value.unionPay.periods = "";
    form.value.unionPay.bankCode = val[1].code;
    form.value.unionPay.bankName = val[1].oName;
    form.value.unionPay.payWay = val[2].name;
    form.value.unionPay.terminalCode = val[0].code;
    form.value.unionPay.terminalName = val[0].oName;
  }
}

function changePayType(val) {
  if (val === "1") {
    form.value.unionPay = {
      installmentValue: [],
      attachment: [],
      bankCode: "",
      bankName: {
        f: "",
        s: "",
        t: "",
        value: "",
      },
      terminalName: {
        f: "",
        s: "",
        t: "",
        value: "",
      },
      configFinanceDataId: "",
      payWay: "",
      paymentPrice: "",
      periods: "",
      terminalCode: "",
    };
  } else {
    form.value.multiPay = [];
  }
}

const uploadImg = (file) => {
  const params = {
    moduleId: website.moduleId,
    uploadType: "1",
    file: file.raw,
    maxSize: 5120,
    fileType: ["jpg", "jpeg", "png", "gif"],
  };
  upload(params)
    .then((res) => {
      form.value.unionPay.attachment = [
        {
          fileName: res.fileName,
          fileUrl: res.fileUrl,
          fileSize: file.size,
        },
      ];
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};

// 多种支付方式弹框回调
function payTypeConfirm(val) {
  form.value.multiPay = val;
}

// 自动选中
function expande(val) {
  const node = findNodeByPathWithIndexes(treeSelect.list, val);
  chooseNext(node.indexes.length, node.node?.list);
}

function chooseNext(index, list) {
  if (list?.length === 1) {
    nextTick(() => {
      const panel = document
        .querySelector(".add-order-page-cascader")
        .querySelector(".el-cascader-panel");
      const menus = panel?.querySelectorAll(".el-cascader-menu");
      const targetMenu = menus?.[index];
      const nodes = targetMenu?.querySelectorAll(".el-cascader-node");
      const targetNode = nodes?.[0] as any;
      targetNode?.click();
    });
  }
}

/**
 * 根据 code 路径查找树中的某个节点
 * @param {Array} tree - 树的根节点数组
 * @param {Array<string|number>} pathCodes - 表示路径的 code 数组
 * @returns {Object|null} - 找到的节点对象，找不到返回 null
 */
function findNodeByPathWithIndexes(tree, pathCodes) {
  let currentLevel = tree;
  let currentNode = null;
  const indexes = [];

  for (const code of pathCodes) {
    const index = currentLevel.findIndex((node) => node.code === code);
    if (index === -1) {
      console.warn(`找不到 code=${code}`);
      return { node: null, indexes: [] };
    }

    indexes.push(index);
    currentNode = currentLevel[index];
    currentLevel = currentNode.list || [];
  }

  return { node: currentNode, indexes };
}

// 批量串码录入 - 确定回调
function barCodeConfirm({ skuList, secondList }) {
  secondHandList.value = secondList;
  addProductConfirm(skuList);
}

// 是否有展开按钮
function getShowExpend({ row }) {
  if (!row.children || Object.keys(row.children).length === 0) {
    return "no-expend";
  } else {
    return "";
  }
}

// 获取商品的捆绑活动规则
function getBindActivityList(row) {
  let val = null;
  if (row.skuId) {
    for (const key in activityData.data) {
      if (key + "" === row.skuId + "") {
        val = (activityData.data[key]["bindRuleList"] || []).filter((el) => {
          return (el.activityMultipleRuleProductVOList || []).every((c) => {
            return c?.isBarcode?.value !== "YES" || c.productId === row.skuId;
          });
        });
      }
    }
  }
  if (val) {
    return val;
  }
  return [];
}

// 修改折扣弹框回调
function discountConfirm(data) {
  if (selectActivity.value?.usageMethod?.value === "DISCOUNT") {
    selectActivity.value.originDiscountAmount =
      selectActivity.value.originDiscountAmount ||
      selectActivity.value.discountAmount;

    selectActivity.value.discountAmount = data.discountAmount;
  } else {
    selectActivity.value.originReducePrice =
      selectActivity.value.originReducePrice ||
      selectActivity.value.reducePrice;
    selectActivity.value.reducePrice = data.reducePrice;
  }
  coputedDiscount();
}

// 添加捆绑活动规则弹框展示
function showEditBind(row) {
  if (row?.isBarcode?.value === "YES" && !row?.barcodeList?.length) {
    ElMessage.warning(i18n.t("receipt.pleaceAddCode"));
    return;
  }
  editBundleData.product = row;
  editBundleData.data = getBindActivityList(row);
  editBundleData.show = true;
}

function confirmBundle(data) {
  productList.value = data;
  coputedDiscount(true);
}

/**
 * 获取某个商品的活动列表（用于表格渲染）
 * @param row
 */
function getHasActivityList(row) {
  if (row.children) {
    const list = [];
    const item = getBindActivityList(row);
    for (const key in row.children) {
      const el = item.find((c) => c.id + "" === key + "");
      if (el) {
        list.push({
          ...el,
          product: row.children[key],
        });
      }
    }
    return list;
  } else {
    return [];
  }
}

/**
 * 获取主商品的未参与活动的数量
 * @param row 商品
 */
const getMainProductNum = (row) => {
  let num = row.quantity;
  for (const key in row.children) {
    if (row.children[key]) {
      row.children[key].forEach((item) => {
        if (item.skuId === row.skuId) {
          num = subtract(num, item.quantity);
        }
      });
    }
  }
  return num;
};

/**
 * 获取子商品的数量
 * @param row 商品
 */
const getChildProductNum = (row) => {
  let num = 0;
  for (const key in row.children) {
    if (row.children[key]) {
      row.children[key].forEach((item) => {
        if (item.skuId === row.skuId) {
          num = add(num, item.quantity);
        }
      });
    }
  }
  return num;
};

// 删除商品(捆绑销售)
const stopDialogConfirm = () => {
  const waitList = [];
  const data = operateDialog.data.children;
  if (data) {
    for (const key in data) {
      (data[key] || []).forEach((el) => {
        waitList.push({
          ...el,
          marketingActivityId: null,
          marketingDiscountAmount: null,
          marketingMainSkuId: null,
          marketingReducePrice: null,
          marketingUseMethod: null,
          marketingUseNum: null,
          marketingActivityName: null,
        });
      });
    }

    waitList.forEach((el) => {
      const index = productList.value.findIndex(
        (c) => c.skuId + "" === el.skuId + ""
      );
      if (el.skuId !== operateDialog.data?.skuId) {
        if (index !== -1) {
          productList.value[index].quantity = plus(
            Number(productList.value[index]?.quantity || 0),
            Number(el?.quantity || 0)
          );
        } else {
          productList.value.push(el);
        }
      }
    });
    productList.value = productList.value.filter(
      (el) => el.skuId !== operateDialog.data?.skuId
    );
    coputedDiscount(true);
  }
};

// 修改商品数量弹框回调
const confirmEditQuantity = (product, formData) => {
  const waitList = [];
  productList.value.forEach((el) => {
    if (el.skuId === product.skuId && el.children) {
      for (const key in el.children) {
        let isDelete = false;
        if (key + "" === formData.activityId + "") {
          el.children[key].forEach((item) => {
            item.quantity = subtract(item.quantity, formData.quantity || 0);
            item.marketingUseNum = item.quantity;
            if (!item.quantity || item.quantity === 0) {
              isDelete = true;
            }
            waitList.push({
              ...item,
              quantity: formData.quantity || 0,
              marketingActivityId: null,
              marketingDiscountAmount: null,
              marketingMainSkuId: null,
              marketingReducePrice: null,
              marketingUseMethod: null,
              marketingUseNum: null,
              marketingActivityName: null,
            });
          });
        }
        if (isDelete) {
          delete el.children[key];
        }
      }
    }
  });
  waitList.forEach((el, index2) => {
    if (el.skuId === product?.skuId) {
      productQuantityData.data.quantity = subtract(
        product.quantity,
        formData.quantity || 0
      );
      if (productQuantityData.data.isBarcode?.value === "YES") {
        productQuantityData.data.barcodeList =
          productQuantityData.data.barcodeList.slice(
            0,
            productQuantityData.data.quantity
          );
      }
    } else {
      const index = productList.value.findIndex(
        (c) => c.skuId + "" === el.skuId + ""
      );
      if (index !== -1) {
        productList.value[index].quantity = plus(
          Number(productList.value[index]?.quantity || 0),
          Number(el?.quantity || 0)
        );
      } else {
        productList.value.push(el);
      }
    }
  });
  productList.value = productList.value.filter(
    (el) => el.quantity && el.quantity !== 0
  );
  coputedDiscount(true);
};

// 修改折扣弹框
const editProductDiscount = (row) => {
  editDiscountData.data = row;
  editDiscountData.show = true;
};
</script>

<style lang="scss" scoped>
:deep {
  .el-input-group__append {
    padding: 0 6px;
  }

  .el-input-number .el-input__wrapper {
    padding-left: 18px;
    padding-right: 18px;
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    background: transparent;
    width: 16px;
  }

  .el-tag__content {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

:deep {
  td.el-table__cell {
    align-content: center;
  }
}

.right-block {
  min-width: 410px;
  display: flex;
  flex-direction: column;
}

.min-height {
  min-height: 560px;
}

.alert_blue {
  padding: 10px 16px;
  background: #eff4ff;
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  color: #3d3d3d;
  line-height: 18px;
  text-align: left;
}

:deep(.is-disabled) {
  .el-select__selected-item,
  .el-textarea__inner,
  .el-input__inner {
    color: #989cac;
    -webkit-text-fill-color: #989cac;

    &::placeholder {
      color: #989cac !important;
    }
  }
}

:deep(.el-form-item__label) {
  color: #1d2300;
}

.second-tag {
  font-size: 10px;
  color: #ff9900;
  padding: 1px 8px;
  border-radius: 4px;
  border: 1px solid #ff9900;
  margin-right: 8px;
}
</style>
<style lang="scss">
.tip-class {
  width: 320px;
  padding: 8px 20px;
  button {
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px 12px;
    height: min-content;
    line-height: normal;
    border-radius: 4px;
    background: #717171;
    border-color: #717171;
  }
}

.card {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 4px;
  background: #ffffff;
  border: 1px solid #dfe2e7;
  padding: 8px;
  transition: all 0.2s ease-in;

  &:has(.is-checked) {
    border: 1px solid #415fff;
  }

  .el-radio {
    line-height: normal;
    height: min-content;

    .el-radio__label {
      font-size: 14px;
      color: #1d2330;
      line-height: 24px;
    }
  }

  .card-detail {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border-radius: 4px;
    background: #f5f7f9;
  }

  .el-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .el-form-item__content {
    line-height: normal;
  }

  .el-form-item__label {
    padding: 0;
    line-height: normal;
  }
}

.file-box {
  display: flex;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #dfe2e7;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  overflow: hidden;
}

.el-upload {
  width: 100%;
}

.no-expend {
  .el-table__expand-icon {
    display: none;
  }
}
</style>
