/*
 * @Author: spanull <EMAIL>
 * @Date: 2025-01-06 11:39:44
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-21 15:06:49
 * @FilePath: \flb-receipt\src\util\auth.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Cookies from 'js-cookie'
import website from '@/config/website'
import { domain } from '@/config/env'
const Authorization = website.Authorization
const inFifteenMinutes = new Date(new Date().getTime() + website.tokenTime * 1000)

export function getToken() {
  const token = Cookies.get(Authorization) || 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJidUlkIjozNjksInVpZCI6NDYzOTYsImlzU3VwZXIiOjAsImVudGl0eU5hbWUiOiIiLCJ1dHlwZSI6NCwidGVuYW50SWQiOjEwMzMsImlzcyI6IlNKQUNPIiwiZW50aXR5SWQiOjEwNzU2NDUsIm1vZHVsZUlkIjoxMiwiZXhwIjoxNzU1NjczNTA5fQ.gaHAVoWwWqCLKw9kEgvelVXVKGcVjMZ3osD2aZhGucQ'
  return token
}

export function setToken(token: string) {
  return Cookies.set(Authorization, token, { 'max-age': website.tokenTime + '', domain })
}

export function removeToken() {
  return Cookies.remove(Authorization)
}

// 获取验证码一小时发送次数
export function getHourNum() {
  return Cookies.get('hourNum')
}
// 存储验证码一小时发送次数
// 过期时间1小时
// 因为expires存储过期自动清除后不刷新不能二次存储，所以使用max-age存储
export function setHourNum(num) {
  return Cookies.set('hourNum', num, { 'max-age': '3600', domain })
}

// 获取验证码一天发送次数
export function getDayNum() {
  return Cookies.get('dayNum')
}
// 存储验证码一天时发送次数
// 过期时间24小时
export function setDayNum(num) {
  return Cookies.set('dayNum', num, { 'max-age': '86400', domain })
}