<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-04-11 11:33:46
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-05-22 14:13:03
 * @FilePath: \flb-receipt\src\views\discountRole\list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    v-loading="loading"
    class="p24 bgc_white position_re"
    :element-loading-text="$t('receipt.loadingInProgress')"
  >
    <div class="mt40 flex flex_direction_column align_items_center">
      <div class="flex flex_direction_column" style="width: 580px">
        <div
          class="fs14 c_1D2330 pb8"
          style="border-bottom: 1px dashed #e7e9f0"
        >
          {{ $t("receipt.discountRole") }}
        </div>
        <el-form class="mt24" :model="formData" ref="formRef">
          <el-form-item
            :label="$t('receipt.needApproveM')"
            label-width="150"
            prop="auditFlag"
          >
            <el-radio-group class="lc_radio" v-model="formData.auditFlag">
              <el-radio :value="1">
                {{ $t("receipt.yes") }}
              </el-radio>
              <el-radio :value="0">
                {{ $t("receipt.no") }}
              </el-radio>
              <el-radio :value="2">
                {{ $t("receipt.changePriceAudit") }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            :label="$t('receipt.discountPrivilegesM')"
            class="no-margin"
          >
            <el-form-item
              class="ml48 lable-mr8"
              prop="salesAdjustmentPricePercent"
              :label="$t('receipt.discountProductPrice')"
            >
              <div class="suf_input" style="width: 100px">
                <el-input-number
                  :min="0"
                  :max="100"
                  :step="1"
                  step-strictly
                  :placeholder="$t('receipt.pleaseEnter')"
                  style="width: 100%"
                  :controls="false"
                  v-model="formData.salesAdjustmentPricePercent"
                ></el-input-number>
                <div class="suf">%</div>
              </div>
            </el-form-item>
          </el-form-item>
          <!-- <el-form-item
            :label="$t('receipt.authChangePriceM')"
            class="no-margin"
          >
            <el-form-item
              class="ml48 lable-mr8"
              prop="auditAdjustmentPricePercent"
              :label="$t('receipt.discountProductPrice')"
            >
              <div class="suf_input" style="width: 100px">
                <el-input-number
                  :min="0"
                  :max="100"
                  :step="1"
                  step-strictly
                  :placeholder="$t('receipt.pleaseEnter')"
                  style="width: 100%"
                  :controls="false"
                  v-model="formData.auditAdjustmentPricePercent"
                ></el-input-number>
                <div class="suf">%</div>
              </div>
            </el-form-item>
          </el-form-item> -->
        </el-form>
        <div>
          <el-button class="mt28" type="primary" @click="submit">
            {{ $t("receipt.preservation") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getDiscountRole, updateDiscountRole } from "@/api/discountRole";
import lang from "@/lang/index";
import { ElMessage } from "element-plus";
import { divide, multiply, round } from "@/util/numberUtil";

const i18n = lang.global;
const loading = ref(false);
const formRef = ref(null);
const formData = ref({
  auditAdjustmentPricePercent: null,
  salesAdjustmentPricePercent: null,
  auditFlag: 0,
});

onMounted(() => {
  loading.value = true;

  getDiscountRole()
    .then((res) => {
      if (res.code == 200) {
        formData.value = res.data || {};
        formData.value.auditAdjustmentPricePercent = multiply(
          Number(res.data.auditAdjustmentPricePercent || 0),
          100
        );
        formData.value.salesAdjustmentPricePercent = multiply(
          Number(res.data.salesAdjustmentPricePercent || 0),
          100
        );
        formData.value.auditFlag = Number(res.data.auditFlag);
      } else {
        ElMessage.error(res.msg);
      }
      loading.value = false;
    })
    .catch((res) => {
      loading.value = false;
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
});

function submit() {
  updateDiscountRole({
    auditAdjustmentPricePercent: round(
      divide(Number(formData.value.auditAdjustmentPricePercent), 100),
      2
    ),
    auditFlag: formData.value.auditFlag,
    salesAdjustmentPricePercent: round(
      divide(Number(formData.value.salesAdjustmentPricePercent), 100),
      2
    ),
  })
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success(i18n.t("receipt.operationSuccess"));
      } else {
        ElMessage.error(res.data?.msg || i18n.t("receipt.networkError"));
      }
    })
    .catch((res) => {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 16px;

  .el-form-item__label {
    justify-content: flex-start;
  }
}

.no-margin {
  margin-bottom: 0;
}

.lable-mr8 {
  :deep(.el-form-item__label) {
    padding-right: 8px;
  }
}
</style>
