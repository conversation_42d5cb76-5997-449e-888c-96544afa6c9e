import request from "./axios";
import { baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 退货单据
// 列表(分页)
export const getOrderReturn = (data: Params) =>
  request({
    url: baseUrl + "/order/orderReturn/",
    method: "get",
    params: data,
  });

// 新增
export const addOrderReturn = (data: Params) =>
  request({
    url: baseUrl + "/order/orderReturn/",
    method: "post",
    data: data,
  });

// 退货单审核通过
export const examineSuccess = (data: Params) =>
  request({
    url: baseUrl + "/order/orderReturn/examineSuccess",
    method: "put",
    data: data,
  });

// 退货单审核拒绝
export const examineFail = (data: Params) =>
  request({
    url: baseUrl + "/order/orderReturn/examineFail",
    method: "put",
    data: data,
  });

// 退货单单个删除
export const deleteOrderReturn = (data: Params) =>
  request({
    url: baseUrl + `/order/orderReturn/${data.id}`,
    method: "delete",
  });

// 撤回
export const withdraw = (data: Params) =>
  request({
    url: baseUrl + "/order/orderReturn/withdraw",
    method: "put",
    data: data,
  });

// 通过退货单号查询详情数据（含货品、唯一值、优惠信息）
export const getOrderReturnDetail = (data: Params) =>
  request({
    url: baseUrl + `/order/orderReturn/${data.orderNo}`,
    method: "get",
  });

// 导出
export const exportReturnOrder = (data: Params) =>
  request({
    url: baseUrl + `/order/orderReturn/export`,
    method: "get",
    responseType: "blob",
    params: data,
  });
