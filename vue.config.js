const path = require("path");

const packageName = require("./package.json").name;
const resolve = (dir) => path.join(__dirname, dir);

const CompressionPlugin = require("compression-webpack-plugin")

module.exports = {
  publicPath: "/",
  // publicPath: '/microSalary',
  assetsDir: "static",
  productionSourceMap: false,
  lintOnSave: false,
  devServer: {
    port: process.env.VUE_APP_PORT,
    disableHostCheck: true,
    proxy: {
      "/s3api": {
        target: "https://101-emip-qas-01.ecss3.shanghai-electric.com:443",
        ws: true,
        changOrigin: true,
      },
      "/wellTencentMap": {
        target: "https://apis.map.qq.com",
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          "^/wellTencentMap": "", //重写接口
        },
      },
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  },
  // css: {
  //   loaderOptions: {
  //     // css: {
  //     // // options here will be passed to css-loader
  //     // },
  //     postcss: {
  //       // options here will be passed to postcss-loader
  //       plugins: [
  //         require('postcss-px2rem')({
  //           remUnit: 75
  //         })
  //         // require('postcss-px-to-viewport')({
  //         //   viewportWidth: 750,
  //         //   minPixelValue: 1
  //         // })
  //       ]
  //     }
  //   }
  // },
  chainWebpack: (config) => {
    config.module
      .rule("pdfjs-dist")
      .test({
        test: /\.js$/,
        include: path.join(__dirname, "node_modules/pdfjs-dist"),
      })
      .use("babel-loader")
      .loader("babel-loader")
      .options({
        presets: ["@babel/preset-env"],
        plugins: ["@babel/plugin-proposal-optional-chaining"],
      });
    config.module
      .rule("pdf")
      .test(/\.(pdf)$/)
      .use("url-loader")
      .loader("url-loader")
      .options({
        limit: 10000,
      })
      .end();
    config.resolve.alias
      .set("@", resolve("src"))
      .set("@img", resolve("src/assets/img"))
      .set("@styl", resolve("src/assets/styl"))
      .set("@js", resolve("src/assets/js"))
      .set("@ts", resolve("src/assets/ts"))
      .set("@fonts", resolve("src/assets/fonts"))
      .set("@css", resolve("src/assets/css"))
      .set("@libs", resolve("src/libs"))
      .set("@cp", resolve("src/components"))
      .set("@views", resolve("src/views"))
      .set("@plugins", resolve("src/plugins"))
      .end();
    config.module
      .rule("images")
      .test(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/)
      .use("url-loader")
      .loader("url-loader")
      .options({
        limit: 10000,
        fallback: {
          loader: "file-loader",
          options: {
            name: "static/img/[name].[hash:8].[ext]",
            publicPath:process.env.VUE_APP_WORKBENCH_URL + ":" + process.env.VUE_APP_PORT,
          },
        },
      })
      .end();
  },
  configureWebpack: {
    module: {
      rules: [
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: "javascript/auto",
        },
      ],
    },
    output: {
      // 子服务的包名，这里与主应用中注册的微应用名称一致
      library: `${packageName}-[name]`, // 根据实际情况来
      // 将你的 library 暴露为所有的模块定义下都可运行的方式
      libraryTarget: "umd",
      // 按需加载相关，设置为 webpackJsonp_子服务名称 即可
      jsonpFunction: `webpackJsonp_${packageName}`, // 根据实际情况来
    },
    plugins: [
      new CompressionPlugin({
        //gzip压缩配置
        test: /\.js$|\.html$|\.css/, //匹配文件名
        threshold: 10240, //对超过10kb的数据进行压缩
        deleteOriginalAssets: false, //是否删除原文件
      })
    ]
  },
};
