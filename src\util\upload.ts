import { upload_ali,fileDelete_ali } from "./upload_ali"
import { upload_tc,fileDelete_tc } from "./upload_tc"

interface Currency {
  [key: string]: any
}

//普通上传
export async function upload (fileObj: Currency) {
  if (fileObj.uploadType && fileObj.uploadType == 'emp') {
    return upload_tc(fileObj)
  } else {
    return upload_ali(fileObj)
  }
}

//从服务器删除文件
export async function fileDelete (fileUrl: string, moduleId: string, type: string, buId: string|number) {
  if (buId && buId == '16') {
    return fileDelete_tc(fileUrl,moduleId, type)
  } else {
    return fileDelete_ali(fileUrl, moduleId, type)
  }
}