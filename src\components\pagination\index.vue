<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-02-11 09:42:55
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-03-14 18:15:20
 * @FilePath: \flb-receipt\src\components\pagination\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="flex align_items_center justify_content_between bgc_white wrap_box"
    :class="props.showShadow ? 'show_Shadow' : ''"
  >
    <div v-if="props.showTotal" class="fs12 c_1D2330">
      {{
        $t("receipt.totalItems", {
          count: props.pageObj.total,
        })
      }}
    </div>
    <div v-else></div>
    <el-pagination
      :current-page="props.pageObj.pageNo"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="props.pageObj.pageSize"
      :total="props.pageObj.total"
      @size-change="onPageChange"
      @current-change="onInterpret"
      :layout="props.layout"
    >
    </el-pagination>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  pageObj: {
    type: Object,
    required: true,
  },
  showShadow: {
    type: Boolean,
    default: () => true,
  },
  layout: {
    type: String,
    default: () => "sizes,prev,pager,next,jumper",
  },
  showTotal: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["onPageChange", "currentChange"]);

const onPageChange = (pageSize) => {
  emit("onPageChange", pageSize);
};
const onInterpret = (pageNo) => {
  emit("currentChange", pageNo);
};
</script>

<style lang="scss" scoped>
.wrap_box {
  position: relative;
  height: 40px;
  padding: 0 16px;
  box-sizing: border-box;
  border-radius: 0px 0px 4px 4px;
}

.show_Shadow {
  border: 1px solid #dfe2e7;
}
</style>
