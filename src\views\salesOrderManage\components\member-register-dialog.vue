<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="title"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="400px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-position="top"
    >
      <el-form-item prop="phone" class="mb8">
        <el-radio-group v-model="form.method">
          <el-radio value="1" border>
            {{ $t("receipt.phoneRegister") }}
          </el-radio>
          <el-radio value="2" border>
            {{ $t("receipt.emailRegister") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="border_radius4" style="background-color: #f3f6f8; padding: 16px">
        <!-- 手机号输入框 -->
        <el-form-item :label="$t('receipt.phone')" prop="phone">
          <el-input
            v-model="form.phone"
            maxlength="11"
            :placeholder="$t('receipt.pleaseEnter')"
          />
        </el-form-item>
        <!-- 验证码输入框和发送验证码按钮 -->
        <el-form-item
          :label="$t('receipt.verificationCode')"
          prop="verificationCode"
        >
          <el-input
            v-model="form.verificationCode"
            :placeholder="$t('receipt.pleaseEnter')"
          >
            <template #suffix>
              <el-button
                :disabled="isCounting"
                link
                size="small"
                type="primary"
                @click="sendCode"
              >
                {{
                  isCounting
                    ? countdown + "s"
                    : $t("receipt.sendVerificationCode")
                }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="mb0" :label="$t('receipt.name')" prop="memberName">
          <el-input
            v-model="form.memberName"
            :placeholder="$t('receipt.pleaseEnter')"
            type="text"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="onClose">
        {{ $t("receipt.cancel") }}
      </el-button>
      <el-button type="primary" @click="onConfirm">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, reactive, defineProps } from "vue";
import { memberRegister, verificationCode } from "@/api/salesOrderManage";
import { ElMessage } from "element-plus";
import lang from "@/lang/index";

const i18n = lang.global;

const emit = defineEmits(["close", "confirm", "update:show"]);
defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
});
const form = reactive({
  phone: "",
  verificationCode: "",
  memberName: "",
  method: "1",
});
const isCounting = ref(false); // 是否在倒计时中
const countdown = ref(30); // 倒计时的秒数
const timer = ref(null);

const formRules = {
  phone: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnterPhoneNumber"),
      trigger: "blur",
    },
    {
      pattern: /^[1]([3-9])[0-9]{9}$/,
      message: i18n.t("receipt.pleaseEnterValidPhoneNumber"),
      trigger: "blur",
    },
  ],
  code: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnterVerificationCode"),
      trigger: "blur",
    },
  ],
  name: [
    {
      required: true,
      message: i18n.t("receipt.pleaseEnterName"),
      trigger: "blur",
    },
  ],
};
const formRef = ref(null);
// 发送验证码函数
const sendCode = () => {
  // 校验手机号字段
  formRef.value.validateField("phone", (valid) => {
    if (!valid) {
      console.log("手机号验证失败");
      return; // 如果手机号不合法，不继续执行倒计时
    }
    if (isCounting.value) return; // 如果正在倒计时，不允许再点击
    // 获取验证码
    verificationCode({ phone: form.phone }).then((res) => {
      if (res.code == 200) {
        // 手机号通过验证，获取验证码开始倒计时
        isCounting.value = true; // 启动倒计时
        countdown.value = 30; // 重置倒计时
        startCountdown();
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

// 启动倒计时
const startCountdown = () => {
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      clearInterval(timer.value); // 清除定时器
      isCounting.value = false; // 倒计时结束，按钮恢复
    }
  }, 1000);
};

// 组件销毁时清理定时器
watch(
  () => isCounting.value,
  (newVal) => {
    if (!newVal && timer.value) {
      clearInterval(timer.value);
    }
  }
);

// 已读
const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;

    memberRegister(form).then((res) => {
      if (res.code == 200) {
        onClose();
        emit("confirm", res.data);
        ElMessage.success(i18n.t("receipt.registerSuccess"));
      } else {
        ElMessage.error(res.msg || i18n.t("receipt.networkError"));
      }
    });
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped></style>
