<template>
  <el-drawer
    :model-value="show"
    :append-to-body="true"
    :before-close="onClose"
    :title="$t('receipt.payType')"
    class="title_drawer radius8_drawer"
    modal-class="transparent_modal top_drawer_modal"
    direction="rtl"
    size="956px"
  >
    <div
      v-loading="loading"
      :element-loading-text="$t('receipt.loadingInProgress')"
      class="flex flex_direction_column"
    >
      <div class="flex">
        <div
          class="pt4 pb4 pl12 pr12 border_radius4 bgc_F3F4FB flex align_items_center"
        >
          <div>
            <span class="pr4 fs12 lh18 c_1D2330">
              {{ $t("receipt.totalAmountM") }}
            </span>
            <span class="fs12 lh18 c_1D2330">
              {{ formatMoney(props.data?.actualPrice) }}
            </span>
          </div>
          <div class="line"></div>
          <div>
            <span class="pr4 fs12 lh18 c_1D2330">
              {{ $t("receipt.currentAmountM") }}
            </span>
            <span class="fs12 lh18 c_EC2D30">
              {{ formatMoney(totalPrice) }}
            </span>
          </div>
        </div>
      </div>
      <el-table
        :data="tableData"
        size="small"
        border
        class="custom_radius4_table mt8"
        style="width: 100%"
      >
        <el-table-column
          prop="configFinanceDataId"
          :label="$t('receipt.payTypeHeader')"
          min-width="468"
        >
          <template #header>
            {{ $t("receipt.payTypeHeader") }}
            <span class="c_EC2D30">*</span>
          </template>
          <template #default="scope">
            <el-cascader
              :popper-class="'pay-type-cascader' + scope.$index"
              @change="(val) => changeConfig(val, scope.row)"
              style="width: 100%"
              :placeholder="$t('receipt.financeConfigLabel')"
              v-model="scope.row.configFinanceDataId"
              :options="getOptions(scope.row)"
              :props="{
                value: 'code',
                label: 'name',
                children: 'list',
                emitPath: false,
              }"
              @expand-change="(val) => expande(val, scope.$index, row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="periods"
          :label="$t('receipt.stageNum')"
          min-width="128"
        >
          <!-- <template #header>
            {{ $t("receipt.stageNum") }}
            <span class="c_EC2D30">*</span>
          </template> -->
          <template #default="scope">
            <el-select
              style="width: 100%"
              v-model="scope.row.periods"
              :placeholder="$t('receipt.pleaseChoose')"
              size="default"
            >
              <el-option
                v-for="item in scope.row?.installmentValue || []"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="paymentPrice"
          :label="$t('receipt.receiveAmount')"
          min-width="160"
        >
          <template #header>
            {{ $t("receipt.receiveAmount") }}
            <span class="c_EC2D30">*</span>
          </template>
          <template #default="scope">
            <div class="suf_input" style="width: 100%">
              <el-input-number
                :min="0"
                :max="999999999999999"
                :step="getStep()"
                step-strictly
                :placeholder="$t('receipt.pleaseEnter')"
                style="width: 100%"
                :controls="false"
                v-model="scope.row.paymentPrice"
              ></el-input-number>
              <div class="suf">
                {{ store.getters.currencyInfo?.unit || "" }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="attachment"
          :label="$t('receipt.voucher')"
          min-width="96"
        >
          <template #default="scope">
            <el-upload
              action=""
              :show-file-list="false"
              :on-change="(file) => uploadImg(file, scope.row)"
              :auto-upload="false"
              accept=".jpg, .jpeg, .png, .gif"
            >
              <img
                v-if="scope.row.attachment && scope.row.attachment.length"
                :src="scope.row.attachment[0].fileUrl"
                style="width: 100%; height: 100%; border-radius: 4px"
              />
              <div v-else class="upload-box">
                <i class="iconfont-flb icon-a-tupian1" />
              </div>
            </el-upload>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('receipt.operation')"
          width="58"
          class-name="operate_column"
        >
          <template #default="scope">
            <el-button link type="danger" @click="del(scope.$index)">
              {{ $t("receipt.delete") }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
          <empty-box></empty-box>
        </template>
      </el-table>
      <div
        class="flex align_items_center justify_content_center btn"
        @click="addPay"
      >
        <i class="iconfont-flb icon-a-tupian1 mr4" />
        {{ $t("receipt.addPayType") }}
      </div>
    </div>
    <template #footer>
      <div class="t_right">
        <el-button @click="onClose">
          {{ $t("receipt.cancel") }}
        </el-button>
        <el-button type="primary" @click="submit">
          {{ $t("receipt.submit") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, watch, computed, defineProps, defineEmits } from "vue";
import store from "@/store";
import { add, formatMoney, round, getStep } from "@/util/numberUtil";
import emptyBox from "@cp/empty/index.vue";
import website from "@/config/website";
import { upload } from "@/util/upload";
import { dayjs, ElMessage } from "element-plus";
import lang from "@/lang/index";
import { nextTick } from "vue";
import { isEmpty } from "@/util/util";

const i18n = lang.global;

const emits = defineEmits(["close", "update:show", "confirm"]);

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  options: {
    type: Array,
    default: () => [],
  },
});

const tableData = ref([]);
const loading = ref(false);

const totalPrice = computed(() => {
  let price = 0;
  tableData.value.forEach((el) => {
    price = add(Number(el.paymentPrice), price);
  });
  return round(price);
});

watch(
  () => props.show,
  (val) => {
    if (val) {
      tableData.value = props.data?.multiPay || [
        {
          installmentValue: [],
          attachment: [],
          bankCode: "",
          configFinanceDataId: "",
          payWay: "",
          paymentPrice: null,
          periods: "",
          terminalCode: "",
          bankName: {
            f: "",
            s: "",
            t: "",
            value: "",
          },
          terminalName: {
            f: "",
            s: "",
            t: "",
            value: "",
          },
        },
      ];
    }
  },
  { immediate: true }
);

function submit() {
  let isBreak = false;
  tableData.value.forEach((item, index) => {
    if (isBreak) return;
    if (!item.configFinanceDataId) {
      isBreak = true;
      ElMessage.error(
        i18n.t("receipt.multiPayTip2", {
          line: index + 1,
        })
      );
      return;
    }
    // if (!item.periods) {
    //   isBreak = true;
    //   ElMessage.error(
    //     i18n.t("receipt.multiPayTip3", {
    //       line: index + 1,
    //     })
    //   );
    //   return;
    // }
    if (isEmpty(item.paymentPrice)) {
      isBreak = true;
      ElMessage.error(
        i18n.t("receipt.multiPayTip4", {
          line: index + 1,
        })
      );
      return;
    }
  });
  if (isBreak) return;
  emits("confirm", tableData.value);
  emits("update:show", false);
}

function addPay() {
  tableData.value.push({
    installmentValue: [],
    attachment: [],
    bankCode: "",
    bankName: {
      f: "",
      s: "",
      t: "",
      value: "",
    },
    terminalName: {
      f: "",
      s: "",
      t: "",
      value: "",
    },
    configFinanceDataId: "",
    payWay: "",
    paymentPrice: null,
    periods: "",
    terminalCode: "",
  });
}

function onClose() {
  emits("close");
  emits("update:show", false);
}

function del(index) {
  tableData.value.splice(index, 1);
}

function findNodePath(data, targetCode) {
  const result = [];

  function dfs(nodes, path) {
    for (const node of nodes) {
      const newPath = [...path, node];
      if (node.code === targetCode && node.isEnd === 1) {
        result.push(...newPath);
        return true;
      }
      if (node.list && dfs(node.list, newPath)) {
        return true;
      }
    }
    return false;
  }

  dfs(data, []);
  return result;
}

function expande(val, index, row) {
  const node = findNodeByPathWithIndexes(getOptions(row), val);
  chooseNext(node.indexes.length, node.node?.list, index);
}

function chooseNext(index, list, index2) {
  if (list?.length === 1) {
    nextTick(() => {
      const panel = document
        .querySelector(".pay-type-cascader" + index2)
        .querySelector(".el-cascader-panel");
      const menus = panel?.querySelectorAll(".el-cascader-menu");
      const targetMenu = menus?.[index];
      const nodes = targetMenu?.querySelectorAll(".el-cascader-node");
      const targetNode = nodes?.[0];
      targetNode?.click();
    });
  }
}

function changeConfig(value, row) {
  const val = findNodePath(props.options || [], value);
  if (val) {
    row.installmentValue = val[2].installmentValue
      ? val[2].installmentValue.split(",")
      : [];
    row.periods = "";
    row.bankCode = val[1].code;
    row.bankName = val[1].oName;
    row.payWay = val[2].name;
    row.terminalCode = val[0].code;
    row.terminalName = val[0].oName;
  }
}

const uploadImg = (file, row) => {
  const params = {
    moduleId: website.moduleId,
    uploadType: "1",
    file: file.raw,
    maxSize: 5120,
    fileType: ["jpg", "jpeg", "png", "gif"],
  };
  upload(params)
    .then((res) => {
      row.attachment = [
        {
          fileName: res.fileName,
          fileUrl: res.fileUrl,
          fileSize: file.size,
        },
      ];
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};

/**
 * 根据 code 路径查找树中的某个节点
 * @param {Array} tree - 树的根节点数组
 * @param {Array<string|number>} pathCodes - 表示路径的 code 数组
 * @returns {Object|null} - 找到的节点对象，找不到返回 null
 */
function findNodeByPathWithIndexes(tree, pathCodes) {
  let currentLevel = tree;
  let currentNode = null;
  const indexes = [];

  for (const code of pathCodes) {
    const index = currentLevel.findIndex((node) => node.code === code);
    if (index === -1) {
      console.warn(`找不到 code=${code}`);
      return { node: null, indexes: [] };
    }

    indexes.push(index);
    currentNode = currentLevel[index];
    currentLevel = currentNode.list || [];
  }

  return { node: currentNode, indexes };
}
function getOptions(row) {
  const selectedIds = tableData.value
    .map((item) => item.configFinanceDataId)
    .filter((id) => id && id !== row?.configFinanceDataId);

  let filtered = JSON.parse(JSON.stringify(props.options || []));
  filtered.forEach((item) => {
    item.list = item.list
      .map((child) => {
        return {
          ...child,
          list: child.list.filter((grandChild) => {
            return (
              !selectedIds.includes(grandChild.code) && grandChild.enabled === 0
            );
          }),
        };
      })
      .filter((child) => child.list.length > 0);
  });
  filtered = filtered.filter((item) => item.list.length > 0);
  return filtered;
}
</script>

<style lang="scss" scoped>
.line {
  margin: 0 8px;
  background: #dfe2e7;
  width: 1px;
  height: 12px;
}

.upload-box {
  width: 64px;
  height: 64px;
  display: flex;
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px dashed #dfe2e7;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    border-color: #415fff;

    i {
      color: #415fff !important;
    }
  }

  i {
    color: #646878;
  }
}

.btn {
  border-radius: 0px 0px 4px 4px;
  background: #fafbfc;
  border: 1px solid #dfe2e7;
  border-top: none;
  font-size: 12px;
  color: #415fff;
  padding: 5px 0;
  cursor: pointer;
}
:deep(.custom_radius4_table.el-table) {
  border-radius: 4px 4px 0 0;

  .el-table__inner-wrapper:before {
    background-color: #fff !important;
  }
}
</style>
