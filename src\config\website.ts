/**
 * 全局配置文件
 */
export default {
  title: "开单",
  logo: "IN",
  key: "receipt", //配置主键,目前用于存储
  indexTitle: "开单",
  lockPage: "/lock",
  tokenTime: 28800, //token过期时间(8小时)
  Authorization: "receipt-token",
  moduleId: 22,
  //http的status默认放行不才用统一处理的,
  statusWhiteList: [400],
  //配置首页不可关闭
  isFirstPage: false,
  fistPage: {
    label: "销售开单",
    value: "/salesOrderManage/list",
    params: {},
    query: {},
    meta: {
      i18n: "salesOrderManage",
    },
    group: [],
    close: true,
  },
  //配置菜单的属性
  menu: {
    iconDefault: "",
    props: {
      label: "label",
      path: "path",
      icon: "icon",
      children: "children",
    },
  },
};
