import request from "./axios";
import { baseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 优惠权限配置 查询
export const getDiscountRole = (data: Params) =>
  request({
    url: baseUrl + "/order/orderPermissionConfig/query",
    method: "get",
    params: data,
  });

//   修改
export const updateDiscountRole = (data: Params) =>
  request({
    url: baseUrl + "/order/orderPermissionConfig/update",
    method: "post",
    data: data,
  });
