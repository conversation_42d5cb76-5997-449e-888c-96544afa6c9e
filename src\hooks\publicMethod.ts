import { serialize } from "@/util/util";

// 过滤出id+name
const filterNameById = (value, optionArr) => {
  for (const item of optionArr) {
    if (item.id == value) {
      return item.name;
    }
  }
};

/**
 * 过滤数组中的对象，并返回第一个匹配给定值的对象中指定键的值。
 *
 * @param {Object} params - 函数的参数。
 * @param {string} [params.labelKey="name"] - 要从匹配对象中返回的值的键。
 * @param {string} [params.valueKey="id"] - 用于匹配给定值的键。
 * @param {any} params.value - 要与指定键匹配的值。
 * @param {Array<Object>} params.option - 要过滤的对象数组。
 * @returns {any} 从匹配对象中指定键的值，如果没有匹配项则返回 undefined。
 */
const filterByKeyValue = ({
  labelKey = "name",
  valueKey = "id",
  value,
  option,
}) => {
  for (const item of [...option]) {
    // 将路径分割成数组
    const labelKeys = labelKey.split(".");
    const valueKeys = valueKey.split(".");
    if (valueKeys.reduce((obj, key) => obj?.[key], item) == value) {
      return labelKeys.reduce((obj, key) => obj?.[key], item);
    }
  }
};


// 拼接导出数据地址
const splicingUrl = (url: string, query) => {
  url += "?" + serialize(query);
  return url;
};

// 处理tree的名称，取名称对象里的value
const handleTreeName = (data, field: string) => {
  if (!data || !field) {
    return;
  }
  data.forEach((el) => {
    el.name = el[field]?.value;
    if (el.children && el.children) {
      el.children = handleTreeName(el.children, field);
    }
  });
  return data;
};

export { filterNameById, splicingUrl, handleTreeName, filterByKeyValue };
