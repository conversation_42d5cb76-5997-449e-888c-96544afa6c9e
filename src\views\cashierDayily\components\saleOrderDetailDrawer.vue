<!--
 * @Author: spanull <EMAIL>
 * @Date: 2025-07-21 14:12:11
 * @LastEditors: spanull <EMAIL>
 * @LastEditTime: 2025-07-23 17:15:08
 * @FilePath: \flb-receipt\src\views\cashierDayily\components\saleOrderDetailDrawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    :model-value="show"
    :append-to-body="true"
    :before-close="onClose"
    class="title-drawer2 radius8_drawer"
    modal-class="top_drawer_modal"
    direction="rtl"
    size="calc(100vw - 80px)"
  >
    <template #header>
      <div class="flex align_items_center">
        <div class="fs16 fw500 lh24 c_1D2330">{{ titleStr || "" }}</div>
        <div
          class="ml24 pt4 pb4 pl8 pr8 flex align_items_center border_radius4"
          style="background-color: #dcf0ff"
        >
          <span class="fs12 lh18 c_4F5363 fw400">
            <span>
              {{ $t("receipt.storeM") }}
              {{ orderData.storeName?.value ?? "-" }}
            </span>
            <span class="pl16">
              {{ $t("receipt.dateM") }}
              {{ orderData.date ?? "-" }}
            </span>
          </span>
        </div>
      </div>
    </template>
    <saleOrderDetailContent v-if="show" :orderData="orderData" />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineExpose } from "vue";
import saleOrderDetailContent from "./saleOrderDetailContent.vue";


const show = ref(false);
const orderData = ref({} as any);
const titleStr = ref("");

function openDrawer({ data, title }) {
  show.value = true;
  orderData.value = data;
  titleStr.value = title;
}

function onClose() {
  show.value = false;
}

defineExpose({
  openDrawer,
});
</script>

<style lang="scss">
.title-drawer2 {
  box-shadow: -8px 0px 24px 0px rgba(0, 0, 0, 0.06);

  .el-drawer__header {
    color: #1d2330;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
    padding: 14px 24px;
    border-bottom: 1px solid #dfe2e7;
    .el-drawer__close {
      font-size: 16px;
      color: #7781a1;
    }
  }
}
</style>
