<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="
      props.activity?.usageMethod?.value === 'DISCOUNT'
        ? $t('receipt.editDiscount')
        : $t('receipt.editDeduction')
    "
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="368px"
  >
    <div
      class="border_radius4 bgc_EFF4FF flex flex_direction_column pt12 pb12 pl16 pr16 mb20"
    >
      <div class="fs12">
        <span class="c_4F5363">
          {{ $t("receipt.productNameM") }}
        </span>
        <span class="c_1D2330">{{ props.product?.skuName?.value || "" }}</span>
      </div>
      <div class="fs12 mt8">
        <span class="c_4F5363">
          {{ $t("receipt.unitPriceM") }}
        </span>
        <span class="c_1D2330">
          {{ formatMoney(props.product?.referenceUnitPrice) }}
        </span>
      </div>
    </div>
    <el-form ref="formRef" :model="form" label-position="top">
      <div
        class="flex mb6"
        style="gap: 8px"
        v-if="props.activity?.usageMethod?.value === 'DISCOUNT'"
      >
        <el-form-item
          :label="$t('receipt.rabateM')"
          class="flex1"
          prop="discountAmount"
          :rules="[
            {
              required: true,
              message: $t('receipt.pleaseEnter'),
              trigger: 'blur',
            },
          ]"
        >
          <div class="suf_input" style="width: 100%">
            <el-input-number
              :min="
                props.activity.originDiscountAmount ||
                props.activity.discountAmount
              "
              :max="100"
              :step="1"
              step-strictly
              :placeholder="$t('receipt.pleaseEnter')"
              style="width: 100%"
              :controls="false"
              v-model="form.discountAmount"
              @change="changeDiscount"
            ></el-input-number>
            <div class="suf">
              {{ $t("receipt.fracture") }}
            </div>
          </div>
        </el-form-item>
        <div class="c_3D3D3D" style="margin-top: 28px">-</div>
        <el-form-item
          :label="$t('receipt.discountedPriceM')"
          class="flex1"
          prop="price"
          :rules="[
            {
              required: true,
              message: $t('receipt.pleaseEnter'),
              trigger: 'blur',
            },
          ]"
        >
          <div class="suf_input" style="width: 100%">
            <el-input-number
              :min="getDiscountMinPrice"
              :max="props.product.referenceUnitPrice"
              :step="getStep()"
              step-strictly
              :placeholder="$t('receipt.pleaseEnter')"
              style="width: 100%"
              :controls="false"
              v-model="form.price"
              @change="changePrice"
            ></el-input-number>
            <div class="suf">
              {{ store.getters.currencyInfo?.unit || "" }}
            </div>
          </div>
        </el-form-item>
      </div>
      <div class="flex mb6" style="gap: 8px" v-else>
        <el-form-item
          :label="$t('receipt.deductionM2')"
          class="flex1"
          prop="reducePrice"
          :rules="[
            {
              required: true,
              message: $t('receipt.pleaseEnter'),
              trigger: 'blur',
            },
          ]"
        >
          <div class="suf_input" style="width: 100%">
            <el-input-number
              :min="0"
              :max="
                props.activity.originReducePrice || props.activity.reducePrice
              "
              :step="getStep()"
              step-strictly
              :placeholder="$t('receipt.pleaseEnter')"
              style="width: 100%"
              :controls="false"
              v-model="form.reducePrice"
              @change="changeReduce"
            ></el-input-number>
            <div class="suf">
              {{ store.getters.currencyInfo?.unit || "" }}
            </div>
          </div>
        </el-form-item>
        <div class="c_3D3D3D" style="margin-top: 28px">-</div>
        <el-form-item
          :label="$t('receipt.subPriceM')"
          class="flex1"
          prop="price"
          :rules="[
            {
              required: true,
              message: $t('receipt.pleaseEnter'),
              trigger: 'blur',
            },
          ]"
        >
          <div class="suf_input" style="width: 100%">
            <el-input-number
              :min="getReducePrice"
              :max="props.product.referenceUnitPrice"
              :step="getStep()"
              step-strictly
              :placeholder="$t('receipt.pleaseEnter')"
              style="width: 100%"
              :controls="false"
              v-model="form.price"
              @change="changePrice"
            ></el-input-number>
            <div class="suf">
              {{ store.getters.currencyInfo?.unit || "" }}
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm" :loading="loading">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import {
  ref,
  defineEmits,
  defineProps,
  watch,
  computed,
  onUnmounted,
} from "vue";
import lang from "@/lang/index";
import store from "@/store";
import {
  add,
  formatMoney,
  round,
  getStep,
  divide,
  multiply,
  subtract,
} from "@/util/numberUtil";
import { nextTick } from "vue";

const i18n = lang.global;

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  activity: {
    type: Object,
    required: true,
  },
  product: {
    type: Object,
    required: true,
  },
});
const form = ref({
  price: null,
  discountAmount: null,
  reducePrice: null,
});
const formRef = ref();
const loading = ref(false);

const getDiscountMinPrice = computed(() => {
  return round(
    multiply(
      divide(
        props.activity.originDiscountAmount || props.activity.discountAmount,
        100
      ),
      props.product.referenceUnitPrice
    ),
    0
  );
});

const getReducePrice = computed(() => {
  return round(
    subtract(
      props.product.referenceUnitPrice,
      props.activity.originReducePrice || props.activity.reducePrice
    )
  );
});

onUnmounted(() => {
  window.removeEventListener("keydown", onLisner);
});

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        formRef.value?.resetFields();
        if (props.activity?.usageMethod?.value === "DISCOUNT") {
          form.value.discountAmount = props.activity.discountAmount;
          form.value.price = props.product.discountUnitPrice;
        } else {
          form.value.reducePrice = props.activity.reducePrice;
          form.value.price = props.product.discountUnitPrice;
        }
      });
      window.addEventListener("keydown", onLisner);
    } else {
      // 取消监听（需要传入同一个函数引用）
      window.removeEventListener("keydown", onLisner);
    }
  }
);

const onLisner = (e) => {
  if (e.key === "Enter") {
    onConfirm();
  }
};

function changeDiscount(val) {
  form.value.price = round(
    multiply(divide(val, 100), props.product.referenceUnitPrice),
    0
  );
}

function changeReduce(val) {
  form.value.price = round(subtract(props.product.referenceUnitPrice, val));
}

function changePrice(val) {
  if (props.activity?.usageMethod?.value === "DISCOUNT") {
    form.value.discountAmount = round(
      multiply(divide(val, props.product.referenceUnitPrice), 100),
      0
    );
  } else {
    form.value.reducePrice = round(
      subtract(props.product.referenceUnitPrice, val)
    );
  }
}

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    emit("confirm", form.value);
    emit("update:show", false);
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped></style>
