<template>
  <div class="empty_box flex flex_direction_column align_items_center">
    <img src="@/assets/img/common/empty.png" width="100" height="72" />
    <p class="c_4F5363 lh18 mt8">{{ props.text || $t("receipt.noData") }}</p>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";

const props = defineProps({
  text: {
    type: String,
  },
});
</script>

<style lang="scss" scoped>
.empty_box {
  padding: 40px 0;
}
</style>
