<template>
  <el-dialog
    :model-value="show"
    :before-close="onClose"
    :title="$t('receipt.reduceProduct')"
    append-to-body
    class="common-dialog-center common_border_top_dialog"
    width="320px"
    :close-on-click-modal="false"
  >
    <div
      class="border_radius4 bgc_EFF4FF flex flex_direction_column pt12 pb12 pl16 pr16 mb20"
    >
      <div class="fs12">
        <span class="c_4F5363">
          {{ $t("receipt.mainProductM") }}
        </span>
        <span class="c_1D2330">{{ props.data?.skuName?.value || "" }}</span>
      </div>
    </div>
    <el-form ref="formRef" :model="form" label-position="top">
      <el-form-item
        :label="$t('receipt.chooseActivityM')"
        class="flex1"
        prop="activityId"
        :rules="[
          {
            required: true,
            message: $t('receipt.pleaseEnter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="form.activityId"
          :placeholder="$t('receipt.pleaseChoose')"
        >
          <el-option
            v-for="item in props.activityList || []"
            :key="item.id"
            :label="item.ruleName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('receipt.reduceProductM')"
        prop="quantity"
        :rules="[
          {
            required: true,
            message: $t('receipt.pleaseEnter'),
            trigger: 'change',
          },
        ]"
      >
        <el-input-number
          class="widthP100"
          v-model="form.quantity"
          :max="getProduct"
          :min="0"
          :step="1"
          step-strictly
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ $t("receipt.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm" :loading="loading">
        {{ $t("receipt.determine") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { ref, defineEmits, defineProps, watch, computed } from "vue";
import lang from "@/lang/index";
import store from "@/store";
import { add, formatMoney, round, getStep } from "@/util/numberUtil";
import { nextTick } from "vue";

const i18n = lang.global;

const emit = defineEmits(["close", "confirm", "update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  activityList: {
    type: Array,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
});
const form = ref({
  quantity: null,
  activityId: "",
});
const formRef = ref();
const loading = ref(false);

const getProduct = computed(() => {
  if (props.data && props.data.children && form.value.activityId) {
    let el = null;
    for (const key in props?.data?.children) {
      if (
        key + "" === form.value.activityId + "" &&
        props?.data?.children[key].length
      ) {
        el = props?.data?.children[key][0];
      }
    }
    if (el) {
      return Number(el.marketingUseNum);
    }
  }
  return null;
});

watch(
  () => props.show,
  (val) => {
    if (val) {
      nextTick(() => {
        formRef.value?.resetFields();
        form.value.activityId = props.activityList[0]?.id || "";
        if (form.value.activityId) {
          form.value.quantity = 1;
        }
      });
    }
  }
);

const onConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    emit("confirm", props.data, form.value);
    emit("update:show", false);
  });
};

const onClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped></style>
