//useGetters .js
import { mapGetters, createNamespacedHelpers } from 'vuex'
import commonFn from './common'

//第一个参数为模块的名称，为了在模块中，也可以使用辅助函数
const useGetters = (moduleName: string, mapper: any) => {
  let mapperFn = mapGetters
  if (typeof moduleName === 'string' && moduleName.length > 0) {
    //防止出现useState('', [])发生了
    mapperFn = createNamespacedHelpers(moduleName).mapGetters
  } else {
    mapper = moduleName
  }
  return commonFn(mapper, mapperFn)
}
export default useGetters
