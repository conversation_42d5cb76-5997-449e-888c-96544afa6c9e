import { createI18n } from "vue-i18n";
import { getStore } from "@/util/store";
import elementEnLocale from "element-plus/es/locale/lang/en"; // 英文 lang
import elementZhLocale from "element-plus/es/locale/lang/zh-cn"; // 中文 lang
import zh from "./package/zh";

const i18n = createI18n({
  locale: getStore({ name: "language" }) || "zh",
  legacy: false,
  messages: {
    en: {
      ...elementEnLocale,
    },
    // 中文
    zh: {
      ...elementZhLocale,
      ...zh,
    },
  },
  silentTranslationWarn: true,
});

export default i18n;
