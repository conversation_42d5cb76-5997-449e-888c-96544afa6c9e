import request from './axios'
import { baseUrl } from '@/config/env'

interface Params {
  [key: string]: any
}

// 查询全部开关状态
export const getAllAuditApi = () => request({
  url: baseUrl + '/erp/businessAuditSwitch/getAll',
  method: 'get'
})

// 根据Id获取详情
export const getAuditInfoApi = (id: number) => request({
  url: baseUrl + '/erp/businessAuditSwitch/' + id,
  method: 'get'
})

// 批量修改审核开关
export const batchUpdateAuditApi = (data: Params) => request({
  url: baseUrl + '/erp/businessAuditSwitch/batchUpdate',
  method: 'put',
  data: data
})