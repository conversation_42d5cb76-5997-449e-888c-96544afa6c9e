import request from "./axios";
import { baseUrl, hrBaseUrl } from "@/config/env";

interface Params {
  [key: string]: any;
}

// 销售订单管理
// 列表(分页)
export const getOrderInfo = (data: Params) =>
  request({
    url: baseUrl + "/order/orderInfo/",
    method: "get",
    params: data,
  });

// 门店选项
export const getNoPowerStoreSelectorApi = (data: Params) =>
  request({
    url: baseUrl + "/sys/store/storeSelector",
    method: "get",
    params: data,
  });
// 门店选项（权限
export const getStoreSelectorApi = (data: Params) =>
  request({
    url: baseUrl + "/sys/store/havePowerStoreSelector",
    method: "get",
    params: data,
  });

// 获取客户的门店
export const getCustomStore = (data: Params) =>
  request({
    url: baseUrl + `/sys/store/getBaseInfo/${data.customerIdList}`,
    method: "get",
  });

// 通过销售单号查询详情数据（含货品、唯一值、优惠信息）
export const getOrderInfoDetail = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/${data.orderNo}`,
    method: "get",
    params: data,
  });

// 通过单个id删除销售单
export const deleteOrder = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/${data.id}`,
    method: "delete",
  });

// 批量审核拒绝操作
export const getExamineFail = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/examineFail`,
    method: "put",
    data: data,
  });

// 批量审核通过操作
export const getExamineSuccess = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/examineSuccess`,
    method: "put",
    data: data,
  });

// 批量撤回通过操作
export const getExamineWithdraw = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/withdraw`,
    method: "put",
    data: data,
  });

// 提交新订单
export const orderInfoSubmit = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/submit`,
    method: "post",
    data,
  });

// 会员基础信息列表(分页)
export const memberInfoPage = (data: Params) =>
  request({
    url: baseUrl + `/marking/memberInfo/page`,
    method: "post",
    data,
  });

// 会员注册获取验证码
export const verificationCode = (data: Params) =>
  request({
    url: baseUrl + `/marking/api/member/verificationCode`,
    method: "post",
    data,
  });

// 会员注册
export const memberRegister = (data: Params) =>
  request({
    url: baseUrl + `/marking/api/member/register`,
    method: "post",
    data,
  });

// 获取产品列表分类树
export const productGroupTree = (data: Params) =>
  request({
    url: baseUrl + `/sys/productGroup/tree`,
    method: "get",
    params: data,
  });

// 获取产品列表分类树(开单导航)
export const productGroupTreeNew = (data: Params) =>
  request({
    url: baseUrl + `/sys/productGroup/treeByOrderShow`,
    method: "get",
    params: data,
  });

// 获取产品列表分类
export const productGroupType = (data: Params) =>
  request({
    url: baseUrl + `/sys/productGroupType/`,
    method: "get",
    params: data,
  });

// 获取产品列表分类(开单导航)
export const productGroupTypeNew = (data: Params) =>
  request({
    url: baseUrl + `/sys/productGroupType/pageByOrderShow`,
    method: "get",
    params: data,
  });

// 获取产品列表
export const queryProduct = (data: Params) =>
  request({
    url: baseUrl + `/erp/stock/productInfoAndStoreStock`,
    method: "get",
    params: data,
  });

// 获取我的（会员）优惠券
export const myCoupon = (data: Params) =>
  request({
    url: baseUrl + `/erp/stock/couponAndStock`,
    method: "get",
    params: data,
  });

// 批量审核拒绝操作
export const examineFail = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/examineFail`,
    method: "put",
    params: data,
  });

// 批量审核通过操作
export const examineSuccess = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/examineSuccess`,
    method: "put",
    params: data,
  });

// 根据门店id获取门店相关人员(区域本级及上级的分管领导+门店所属的人员)
export const getStoreRelatedPerson = (data: Params) =>
  request({
    url: baseUrl + `/guidePortalWms/tmsapi/business/person/getStorePerson`,
    method: "get",
    params: data,
  });

// 获取销售人员、审核人员
export const getSaleAndExaminePerson = (data: Params) =>
  request({
    url: baseUrl + `/sys/sysUserManage/storePowerUser/${data.storeId}`,
    method: "get",
  });

// 获取审核人员
export const getBySearchPerson = (data: Params) =>
  request({
    url: baseUrl + `/guidePortalWms/tmsapi/business/person/getBySearchPerson`,
    method: "post",
    params: data,
  });

// 根据商品id列表获取赠品列表及库存
export const getGiftListByProductIds = (data: Params) =>
  request({
    url: baseUrl + `/erp/stock/getGiftListByProductIds`,
    method: "get",
    params: data,
  });

// 获取各状态单据数量
export const getAllStateNum = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/getAllStateNum`,
    method: "get",
    params: data,
  });

// 商品抵扣规则
export const getDeductionRule = (data: Params) =>
  request({
    url: baseUrl + `/marking/pointDeductionRule/getProductRule`,
    method: "post",
    data,
  });

// 计算优惠
export const calculateDiscount = (data: Params) =>
  request({
    url: baseUrl + `/order/orderDiscount/countPrice`,
    method: "post",
    data,
  });

// 价格修改
export const updateOrderPrice = (data: Params) =>
  request({
    url: baseUrl + `/order/orderPriceAlter/`,
    method: "put",
    data,
  });

// 获取价格修改记录
export const getOrderPriceHistory = (data: Params) =>
  request({
    url: baseUrl + `/order/orderPriceAlter/${data.orderNo}`,
    method: "get",
    params: data,
  });

// 导出
export const exportSaleOrder = (data: Params) =>
  request({
    url: baseUrl + `/order/orderInfo/export`,
    method: "get",
    params: data,
    responseType: "blob",
  });

// 查询串码库存类型
export const getBarcodeType = (data: Params) =>
  request({
    url: baseUrl + `/erp/stockDetail/queryBarcodeStockType`,
    method: "get",
    params: data,
  });

// 根据门店id获取财务录入数据配置树
export const getConfigFinanceTree = (data: Params) =>
  request({
    url: baseUrl + `/erp/configFinanceData/tree/${data.storeId}`,
    method: "get",
  });

// 根据商品查询可用团购活动
export const getDiscountRules = (data: Params) =>
  request({
    url: baseUrl + `/marking/multiple/discount/availableRules?storeId=${data.storeId}`,
    method: "post",
    data: data.data,
  });

// 计算最优惠-团购
export const countMaxGroupDiscount = (data: Params) =>
  request({
    url: baseUrl + `/order/orderDiscount/countMaxGroupDiscount`,
    method: "post",
    data: data,
  });

// 计算最优惠-捆绑
export const countMaxBindDiscount = (data: Params) =>
  request({
    url: baseUrl + `/order/orderDiscount/countMaxBindPrice`,
    method: "post",
    data: data,
  });

// 计算优惠-团购
export const countPriceGroupDiscount = (data: Params) =>
  request({
    url: baseUrl + `/order/orderDiscount/countPriceGroupDiscount`,
    method: "post",
    data: data,
  });

// 计算优惠-捆绑销售
export const countPriceBindDiscount = (data: Params) =>
  request({
    url: baseUrl + `/order/orderDiscount/countBindPrice`,
    method: "post",
    data: data,
  });

// 获取串码商品
export const querySkuInfoByBarcodeList = (data: Params) =>
  request({
    url: baseUrl + `/order/orderSkuBarcode/querySkuInfoByBarcodeList`,
    method: "get",
    params: data,
  });

// 获取商品的营销活动
export const querySkuActivity = (data: Params) =>
  request({
    url: baseUrl + `/marking/multiple/discount/allRulesByProductIds?storeId=${data.storeId}`,
    method: "post",
    data: data.data,
  });

// 获取商品库存
export const queryStoreStock = (data: Params) =>
  request({
    url: baseUrl + `/erp/stock/queryStoreStock`,
    method: "post",
    data: data,
  });
