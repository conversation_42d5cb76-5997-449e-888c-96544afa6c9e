export default [
  {
    path: "/login",
    name: "登录页",
    component: () => import("@/page/login/index.vue"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      i18n: "login",
    },
  },
  {
    path: "/forgetPwd",
    name: "忘记密码",
    component: () => import("@/page/login/forgetPwd.vue"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      i18n: "forgetPwd",
    },
  },
  {
    path: "/404",
    component: () =>
      import("@/page/error-page/404.vue"),
    name: "404",
    meta: {
      keepAlive: false,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/403",
    component: () =>
      import("@/page/error-page/403.vue"),
    name: "403",
    meta: {
      keepAlive: false,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/refresh",
    name: "refresh",
    component: () => import("@/page/refresh/refresh.vue"),
  }
];
