<template>
  <el-drawer
    v-model="drawerVisible"
    direction="rtl"
    :append-to-body="true"
    :modal="false"
    :before-close="onClose"
    size="720px"
    :title="$t('receipt.specDetails')"
    class="title_drawer radius8_drawer"
    modal-class="full_drawer_modal top_drawer_modal"
  >
    <div>
      <!-- 商品信息 -->
      <div class="b_solid_default border_radius8 mb24">
        <div class="pt12 pb8 pl16 pr16 bb_dashed_default flex">
          <span class="c_1D2330 fs16 fw600 lh24 mr8">
            {{ infoData.productName?.value }}
          </span>
          <span
            class="common-status-box"
            :class="infoData.enabled?.value == 'YES' ? 'green' : 'gray'"
          >
            {{ filterNameById(infoData.enabled?.value, statusOpts) }}
          </span>
        </div>
        <div class="pt8 pb8 pl16 pr16">
          <span class="c_4F5363 lh18">
            {{ $t("receipt.itemCodeM") }}
            <span class="c_1D2330">
              {{ infoData.productCode }}
            </span>
          </span>
        </div>
      </div>

      <!-- 规格信息 -->
      <div class="common_title_with_flag mb16 mt24">
        {{ infoData.specTemplateName?.value }}
      </div>

      <template v-for="(group, gIndex) in infoData.specConfDto" :key="group.id">
        <el-divider v-if="gIndex" border-style="dashed" class="mt16 mb16" />
        <p class="c_1D2330 fw600 lh18 mb8">{{ group.name?.value }}</p>
        <div
          class="c_4F5363 lh18 mt8"
          v-for="field in group.productSpecDetailDTOList"
          :key="field.id"
        >
          {{ field.name?.value }}：
          <span class="c_1D2330">
            {{ field.value }}
          </span>
        </div>
      </template>
    </div>

    <template #footer>
      <div class="t_left">
        <el-button @click="onClose">
          {{ $t("receipt.close") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { onMounted, ref, defineEmits, defineProps } from "vue";
import { ElMessage } from "element-plus";
import { getGoodsInfoApi } from "@/api/productInfo";
import { filterNameById } from "@/hooks/publicMethod";
import lang from "@/lang/index";

const i18n = lang.global;
const emit = defineEmits(["close", "refresh"]);
const props = defineProps({
  skuId: {
    type: Number,
    required: true,
  },
});

const drawerVisible = ref(false);
const infoData: any = ref({});
const statusOpts = [
  { id: "NO", name: i18n.t("receipt.stop") },
  { id: "YES", name: i18n.t("receipt.enable") },
];

onMounted(() => {
  drawerVisible.value = true;

  getInfo();
});

// 获取商品详情
const getInfo = () => {
  getGoodsInfoApi(props.skuId)
    .then((res) => {
      if (res.code == 200) {
        const info = res.data || {};
        infoData.value = info;
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((res) => {
      ElMessage.error(res?.msg || i18n.t("receipt.networkError"));
    });
};

// 关闭
const onClose = () => {
  drawerVisible.value = false;
  emit("close");
};
</script>
