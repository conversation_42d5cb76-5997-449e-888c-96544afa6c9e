import request from './axios'
import { uploadUrl, baseUrl, hrBaseUrl } from '@/config/env'

interface Params {
  [key: string]: any
}

//ali上传获取签名
export const apiGetAliOssSTS = (data: Params) => request({
  url: uploadUrl + '/core/sys/oss/getAliOssSTS',
  method: 'get',
  params: data
})

//tc上传获取签名
export const apiGetTcOssSTS = (data: Params) => request({
  url: uploadUrl + '/core/sys/tc/oss/getTcOssSTS',
  method: 'get',
  params: data
})

// 获取系统类型选择器
export const getSysTypeApi = (data: Params) => request({
  url: baseUrl + '/sys/sysType/sysTypeSelector',
  method: 'get',
  params: data
})

// 根据code生成编码
export const generateByCodeApi = (data: Params) => request({
  url: baseUrl + '/sys/sysCodeRule/generateByCode',
  method: 'get',
  params: data
})

// 根据code获取导入模板详情
export const getImportTempApi = (code: string) => request({
  url: baseUrl + '/sys/sysExcelImportTemp/code/' + code,
  method: 'get'
})

// 获取部门人员信息选择器 
export const getHrPersonApi = (data: Params) => request({
  url: hrBaseUrl + '/hrsalaryapi/person/pInfoDepartmentSelector',
  method: 'post',
  data
})

// 获取区域人员信息选择器  
export const getAreaPersonApi = (data: Params) => request({
  url: baseUrl + '/guidePortalWms/tmsapi/business/person/pInfoRegionSelector',
  method: 'post',
  data
})

// // 获取店长选择器  
// export const getStoreManagerSelectorApi = (data: Params) => request({
//   url: hrBaseUrl + '/hrsalaryapi/job/info/storeManagerSelector',
//   method: 'get',
//   params: data
// })


// 人资-根据条件查询全部花名册-新增账户用
export const queryAllByKeyApi = (data: Params) => request({
  url: baseUrl + '/guidePortalHr/hrsalaryapi/person/queryAllByKey',
  method: 'get',
  params: data
})

// 多语言，根据分组id获取获取多语言下的所有数据
export const getLangDataApi = (data: Params) => request({
  url: baseUrl + '/sys/sysLanguageGroup/treeNode',
  method: 'get',
  params: data
})

// 根据条件查询全部花名册
export const getTmsPersonApi = (data: Params) => request({
  url: baseUrl + '/guidePortalWms/tmsapi/business/person/queryAllByKey',
  method: 'get',
  params: data
})