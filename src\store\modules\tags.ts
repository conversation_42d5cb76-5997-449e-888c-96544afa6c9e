import { setStore, getStore } from '@/util/store'
import { diff } from '@/util/util'
import website from '@/config/website'
const isFirstPage = website.isFirstPage;
const tagWel = website.fistPage;
const tagObj = {
  label: '', //标题名称
  value: '', //标题的路径
  params: '', //标题的路径参数
  query: '', //标题的参数
  meta: {},//额外参数
  group: [], //分组
}
//处理首个标签
function setFistTag(list: any[]) {
  if (list.length == 1) {
    list[0].close = false;
  } else {
    list.forEach(ele => {
      if (ele.value === tagWel.value && isFirstPage === false) {
        ele.close = false
      } else {
        ele.close = true
      }
    })
  }
}
//const 不添加重复的标签
function isAddedTag(list: any, action: any) {
  for (let i = 0; i < list.length; i++) {
    if (action.value === list[i].value) {
      return false
    }
  }
  return true
}
const options = {
  namespaced: true,
  state: {
    tagList: getStore({ name: 'tagList' }) || [],
    tag: getStore({ name: 'tag' }) || tagObj,
    tagWel: tagWel
  },
  actions: {

  },
  mutations: {
    ADD_TAG: (state: any, action: any) => {
      state.tag = action;
      setStore({ name: 'tag', content: state.tag })
      // if (state.tagList.some((ele: any) => diff(ele, action))) return
      if (!isAddedTag(state.tagList, action)) {
        return
      }
      const curIndex = state.tagList.findIndex((el: any) => el.value === action.from)
      if (curIndex == -1) {
        state.tagList.push(action)
      } else {
        state.tagList.splice(curIndex+1, 0, action)
      }
      setFistTag(state.tagList);
      setStore({ name: 'tagList', content: state.tagList })
    },
    DEL_TAG: (state: any, action: any) => {
      state.tagList = state.tagList.filter((item: any) => {
        return !diff(item, action);
      })
      setFistTag(state.tagList);
      setStore({ name: 'tagList', content: state.tagList })
    },
    DEL_ALL_TAG: (state: any) => {
      state.tagList = state.tagList.filter((item: { value: string; }) => {
        if (item.value === '/home/<USER>') {
          return true;
        }
      })
      // state.tagList = [state.tagWel];
      setStore({ name: 'tagList', content: state.tagList })
    },
    DEL_TAG_OTHER: (state: any) => {
      state.tagList = state.tagList.filter((item: { value: string; }) => {
        if (item.value === state.tag.value) {
          return true;
        } else if (!website.isFirstPage && item.value === website.fistPage.value) {
          return true;
        } else if (item.value === '/home/<USER>') {
          return true;
        }
      })
      setFistTag(state.tagList);
      setStore({ name: 'tagList', content: state.tagList })
    },
    SET_TAG_LIST(state: any, tagList: any) {
      state.tagList = tagList;
      setStore({ name: 'tagList', content: state.tagList })
    }
  }
}
export default options