<template>
    <div class="forgetPwd-box">
        <div class="forgetPwd-header">
            <div><img src="@/assets/img/default/left-menu-logo.png" width="201" height="36" alt=""></div>
            <div class="forgetPwd-header-right">已有账号，<span @click="onLogin">马上登录</span></div>
        </div>
        <div class="login-container">
            <div class="forgetPwd_img_box"></div>
            <div class="forgetPwd_img_box-right"></div>
            <div>
                <!-- 登录框内容 -->
                <div class="login-weaper animated bounceInDown">
                    <div class="login-border">
                        <div class="login-main">
                            <h4 class="c_1D2330 fw600 t_center fs36 lh40">找回密码</h4>
                            <resetPassword></resetPassword>
                        </div>
                    </div>
                </div>

                <!-- 底部备案号 -->
                <div class="forgetPwd-footer-box">
                    <span class="record-number">
                        <a href="https://beian.miit.gov.cn" target="_blank">滇ICP备2020007267号-1</a>&emsp;Copyright © 2019 -
                        2021.
                        All Rights Reserved.
                    </span>
                </div>
            </div>
            
        </div>
    </div>
</template>
  
<script lang="ts">
import { defineComponent, ref } from 'vue'
import { workbenchUrl, logOutPage } from '../../config/env'
import userLogin from './userlogin.vue'
import resetPassword from './resetPassword.vue'
import useGetters from '@/store/hooks/useGetters'
import { useRouter } from "vue-router";

export default defineComponent({
    name: "login",
    components: { userLogin, resetPassword },
    setup() {
        const router = useRouter();
        //通过getters拿到vuex中的数据
        const storeGetters = useGetters('common', ["website", "language"])
        //切换登录方式
        let activeName = ref('user')
        const changeActiveName = (type: string): void => {
            activeName.value = type;
        }
        const onLogin = () => {
            // router.push({ path: '/login' })
            window.location.href = workbenchUrl + "/#/login" + logOutPage();
        }
        return {
            changeActiveName,
            activeName,
            onLogin,
            ...storeGetters
        }
    },
})
</script>
  
  
<style lang="scss">
@import "@/styles/login.scss";
</style>
  
<style lang="scss" scoped>
.forgetPwd-footer-box {
    position: absolute;
    width: 492px;
    text-align: center;
    // left: 50vh;
    // right: 0;
    bottom: 24px;

    .record-number {
        display: block;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        text-align: center;

        a {
            color: rgba(0, 0, 0, 0.6);
            cursor: pointer;
        }

        a:hover {
            color: rgba(0, 0, 0, 1);
        }
    }
}
</style>
  